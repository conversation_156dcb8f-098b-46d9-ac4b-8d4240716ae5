-- Migration: Add contact number fields to bookings table
-- Date: 2025-07-03

USE nestlings_db;

-- Add contact number fields to bookings table
ALTER TABLE bookings 
ADD COLUMN contact_number_1 VARCHAR(20) NULL AFTER special_requirements,
ADD COLUMN contact_number_2 VARCHAR(20) NULL AFTER contact_number_1;

-- Update existing bookings to use parent's phone number as contact_number_1
UPDATE bookings b
JOIN parents p ON b.parent_id = p.parent_id
SET b.contact_number_1 = p.phone
WHERE b.contact_number_1 IS NULL;

-- Add index for better performance
CREATE INDEX idx_bookings_contact ON bookings(contact_number_1);

-- Verify the changes
DESCRIBE bookings;
