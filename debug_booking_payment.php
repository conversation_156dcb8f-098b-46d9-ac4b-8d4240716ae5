<?php
require_once 'config/database.php';

echo "<h2>Debug Booking Payment Status</h2>";

$booking_id = $_GET['booking_id'] ?? 20;

try {
    // Check booking details
    echo "<h3>Booking Details for ID: $booking_id</h3>";
    
    $stmt = $pdo->prepare("
        SELECT b.*, p.business_name, p.district 
        FROM bookings b 
        JOIN providers p ON b.provider_id = p.provider_id 
        WHERE b.booking_id = ?
    ");
    $stmt->execute([$booking_id]);
    $booking = $stmt->fetch();
    
    if (!$booking) {
        echo "<p style='color: red;'>Booking not found!</p>";
        exit();
    }
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>Field</th><th>Value</th>";
    echo "</tr>";
    
    foreach ($booking as $key => $value) {
        echo "<tr>";
        echo "<td><strong>$key</strong></td>";
        echo "<td>" . ($value === null ? '<em>NULL</em>' : htmlspecialchars($value)) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check payments for this booking
    echo "<h3>Payment Records for Booking ID: $booking_id</h3>";
    
    $payment_stmt = $pdo->prepare("
        SELECT * FROM payments 
        WHERE booking_id = ? 
        ORDER BY payment_date DESC
    ");
    $payment_stmt->execute([$booking_id]);
    $payments = $payment_stmt->fetchAll();
    
    if (empty($payments)) {
        echo "<p style='color: orange;'>No payment records found for this booking.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>Payment ID</th><th>Amount</th><th>Type</th><th>Status</th><th>Date</th><th>Transaction ID</th>";
        echo "</tr>";
        
        foreach ($payments as $payment) {
            echo "<tr>";
            echo "<td>#{$payment['payment_id']}</td>";
            echo "<td>LKR " . number_format($payment['amount'], 2) . "</td>";
            echo "<td>" . ucfirst($payment['payment_type']) . "</td>";
            echo "<td style='color: " . ($payment['payment_status'] === 'completed' ? 'green' : 'red') . ";'>";
            echo ucfirst($payment['payment_status']) . "</td>";
            echo "<td>" . date('M d, Y g:i A', strtotime($payment['payment_date'])) . "</td>";
            echo "<td>{$payment['transaction_id']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check database schema
    echo "<h3>Database Schema Check</h3>";
    
    $columns = $pdo->query("SHOW COLUMNS FROM bookings")->fetchAll();
    echo "<p><strong>Bookings table columns:</strong></p>";
    echo "<ul>";
    foreach ($columns as $column) {
        $highlight = in_array($column['Field'], ['payment_status', 'paid_amount', 'remaining_amount']) ? 'style="color: blue; font-weight: bold;"' : '';
        echo "<li $highlight>{$column['Field']} - {$column['Type']} - Default: {$column['Default']}</li>";
    }
    echo "</ul>";
    
    // Test update query
    echo "<h3>Test Update Query</h3>";
    echo "<p><a href='?booking_id=$booking_id&test_update=1' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Update Payment Status</a></p>";
    
    if (isset($_GET['test_update'])) {
        echo "<p>Testing update query...</p>";
        
        $update_stmt = $pdo->prepare("
            UPDATE bookings 
            SET payment_status = 'paid', paid_amount = total_cost, remaining_amount = 0 
            WHERE booking_id = ?
        ");
        $result = $update_stmt->execute([$booking_id]);
        
        if ($result) {
            echo "<p style='color: green;'>✓ Update successful! Affected rows: " . $update_stmt->rowCount() . "</p>";
            echo "<p><a href='?booking_id=$booking_id' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Refresh Page</a></p>";
        } else {
            echo "<p style='color: red;'>❌ Update failed!</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
