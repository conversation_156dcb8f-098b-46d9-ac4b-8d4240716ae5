<?php
require_once 'config/database.php';

echo "<h2>Fix Payments Table - Add Missing Columns</h2>";

try {
    // Check if card columns exist
    $check_card_last_four = $pdo->query("SHOW COLUMNS FROM payments LIKE 'card_last_four'")->rowCount();
    $check_card_holder = $pdo->query("SHOW COLUMNS FROM payments LIKE 'card_holder_name'")->rowCount();
    
    if ($check_card_last_four == 0) {
        echo "<p>Adding card_last_four column...</p>";
        $pdo->exec("ALTER TABLE payments ADD COLUMN card_last_four VARCHAR(4) NULL");
        echo "<p style='color: green;'>✓ card_last_four column added!</p>";
    } else {
        echo "<p style='color: green;'>✓ card_last_four column already exists</p>";
    }
    
    if ($check_card_holder == 0) {
        echo "<p>Adding card_holder_name column...</p>";
        $pdo->exec("ALTER TABLE payments ADD COLUMN card_holder_name VARCHAR(100) NULL");
        echo "<p style='color: green;'>✓ card_holder_name column added!</p>";
    } else {
        echo "<p style='color: green;'>✓ card_holder_name column already exists</p>";
    }

    // Check if bookings table has payment columns
    echo "<h3>Checking Bookings Table Payment Columns:</h3>";
    $payment_status_col = $pdo->query("SHOW COLUMNS FROM bookings LIKE 'payment_status'")->rowCount();
    $paid_amount_col = $pdo->query("SHOW COLUMNS FROM bookings LIKE 'paid_amount'")->rowCount();
    $remaining_amount_col = $pdo->query("SHOW COLUMNS FROM bookings LIKE 'remaining_amount'")->rowCount();

    if ($payment_status_col == 0) {
        echo "<p>Adding payment_status column to bookings table...</p>";
        $pdo->exec("ALTER TABLE bookings ADD COLUMN payment_status ENUM('unpaid', 'partial', 'paid') NOT NULL DEFAULT 'unpaid'");
        echo "<p style='color: green;'>✓ payment_status column added!</p>";
    } else {
        echo "<p style='color: green;'>✓ payment_status column already exists</p>";
    }

    if ($paid_amount_col == 0) {
        echo "<p>Adding paid_amount column to bookings table...</p>";
        $pdo->exec("ALTER TABLE bookings ADD COLUMN paid_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00");
        echo "<p style='color: green;'>✓ paid_amount column added!</p>";
    } else {
        echo "<p style='color: green;'>✓ paid_amount column already exists</p>";
    }

    if ($remaining_amount_col == 0) {
        echo "<p>Adding remaining_amount column to bookings table...</p>";
        $pdo->exec("ALTER TABLE bookings ADD COLUMN remaining_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00");
        echo "<p style='color: green;'>✓ remaining_amount column added!</p>";

        // Update existing bookings to set remaining_amount = total_cost
        echo "<p>Updating existing bookings with remaining amounts...</p>";
        $pdo->exec("UPDATE bookings SET remaining_amount = total_cost WHERE remaining_amount = 0");
        echo "<p style='color: green;'>✓ Existing bookings updated!</p>";
    } else {
        echo "<p style='color: green;'>✓ remaining_amount column already exists</p>";
    }

    // Show current table structure
    echo "<h3>Current Payments Table Structure:</h3>";
    $structure = $pdo->query("DESCRIBE payments");
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'><th style='padding: 8px;'>Field</th><th style='padding: 8px;'>Type</th><th style='padding: 8px;'>Null</th><th style='padding: 8px;'>Key</th><th style='padding: 8px;'>Default</th></tr>";
    while ($row = $structure->fetch()) {
        echo "<tr>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . $row['Field'] . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . $row['Type'] . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . $row['Null'] . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . $row['Key'] . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . $row['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3 style='color: green;'>✅ Payments table is now ready!</h3>";
    echo "<p><a href='Parent/payment_confirmation.php?booking_id=1' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Test Payment</a>";
    echo "<a href='daycare/payments.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Daycare Payments</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
