<?php
session_start();

// Check if user is logged in and is a parent
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'parent') {
    header("Location: ../login.php");
    exit();
}

require_once '../config/database.php';

$booking_id = $_GET['booking_id'] ?? 0;

// Get parent information
$stmt = $pdo->prepare("SELECT * FROM parents WHERE parent_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$parent = $stmt->fetch();

// Get booking details with provider information
$stmt = $pdo->prepare("
    SELECT b.*, p.business_name, p.district, p.phone as provider_phone, p.email as provider_email,
           p.contact_person
    FROM bookings b 
    JOIN providers p ON b.provider_id = p.provider_id 
    WHERE b.booking_id = ? AND b.parent_id = ?
");
$stmt->execute([$booking_id, $_SESSION['user_id']]);
$booking = $stmt->fetch();

if (!$booking) {
    header("Location: bookings.php");
    exit();
}

// Service type display mapping
$service_type_display = [
    'full_day' => 'Full Day Care',
    'half_day' => 'Half Day Care',
    'hourly' => 'Hourly Care',
    'overnight' => 'Overnight Care',
    'weekend' => 'Weekend Care'
];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Booking Submitted - Nestlings</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        .booking-success {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
            text-align: center;
        }
        
        .success-icon {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            font-size: 2.5rem;
        }
        
        .booking-details {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-top: -50px;
            position: relative;
            z-index: 2;
        }
        
        .status-badge {
            background: linear-gradient(45deg, #ffa726, #ff9800);
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 20px;
        }
        
        .info-row {
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #666;
            margin-bottom: 5px;
        }
        
        .info-value {
            color: #333;
            font-size: 1.1rem;
        }
        
        .next-steps {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-top: 30px;
        }
        
        .step-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .step-number {
            width: 30px;
            height: 30px;
            background: #667eea;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
        }
        
        .btn-outline-primary {
            border: 2px solid #667eea;
            color: #667eea;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
        }
        
        .btn-outline-primary:hover {
            background: #667eea;
            border-color: #667eea;
        }
    </style>
</head>

<body>
    <!-- Booking Success Header -->
    <div class="booking-success">
        <div class="container">
            <div class="success-icon">
                <i class="fa fa-check"></i>
            </div>
            <h1 class="display-4 mb-3">Booking Submitted Successfully!</h1>
            <p class="lead">Your booking request has been sent to the daycare provider</p>
        </div>
    </div>

    <!-- Booking Details -->
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="booking-details p-4">
                    <div class="text-center mb-4">
                        <div class="status-badge">
                            <i class="fa fa-clock me-2"></i>PENDING CONFIRMATION
                        </div>
                        <h3>Booking #<?php echo $booking['booking_id']; ?></h3>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-row">
                                <div class="info-label">Daycare Provider</div>
                                <div class="info-value"><?php echo htmlspecialchars($booking['business_name']); ?></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Contact Person</div>
                                <div class="info-value"><?php echo htmlspecialchars($booking['contact_person']); ?></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Location</div>
                                <div class="info-value"><?php echo htmlspecialchars($booking['district']); ?></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Service Type</div>
                                <div class="info-value"><?php echo htmlspecialchars($service_type_display[$booking['service_type']] ?? $booking['service_type']); ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-row">
                                <div class="info-label">Child Name</div>
                                <div class="info-value"><?php echo htmlspecialchars($booking['child_name']); ?></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Child Age</div>
                                <div class="info-value"><?php echo $booking['child_age']; ?> years old</div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Date</div>
                                <div class="info-value"><?php echo date('M d, Y', strtotime($booking['booking_date'])); ?></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Time</div>
                                <div class="info-value"><?php echo date('g:i A', strtotime($booking['start_time'])) . ' - ' . date('g:i A', strtotime($booking['end_time'])); ?></div>
                            </div>
                        </div>
                    </div>

                    <div class="next-steps">
                        <h5 class="mb-3"><i class="fa fa-list-ol me-2"></i>What happens next?</h5>
                        
                        <div class="step-item">
                            <div class="step-number">1</div>
                            <div>
                                <strong>Provider Review</strong><br>
                                <small class="text-muted">The daycare provider will review your booking request and contact you if needed.</small>
                            </div>
                        </div>
                        
                        <div class="step-item">
                            <div class="step-number">2</div>
                            <div>
                                <strong>Booking Confirmation</strong><br>
                                <small class="text-muted">You'll receive a notification once the provider confirms your booking.</small>
                            </div>
                        </div>
                        
                        <div class="step-item">
                            <div class="step-number">3</div>
                            <div>
                                <strong>Payment</strong><br>
                                <small class="text-muted">After confirmation, you can make your payment through the booking details page.</small>
                            </div>
                        </div>
                        
                        <div class="step-item">
                            <div class="step-number">4</div>
                            <div>
                                <strong>Service & Review</strong><br>
                                <small class="text-muted">After the service is completed, you can leave a review for the provider.</small>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <a href="bookings.php" class="btn btn-primary me-3">
                            <i class="fa fa-calendar me-2"></i>View My Bookings
                        </a>
                        <a href="search.php" class="btn btn-outline-primary">
                            <i class="fa fa-search me-2"></i>Find More Providers
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
