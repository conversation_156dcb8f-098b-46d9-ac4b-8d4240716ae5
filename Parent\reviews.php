<?php
session_start();

// Check if user is logged in and is a parent
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'parent') {
    header("Location: ../login.php");
    exit();
}

require_once '../config/database.php';

// Get parent information
$stmt = $pdo->prepare("SELECT * FROM parents WHERE parent_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$parent = $stmt->fetch();

// Get confirmed bookings that can be reviewed (only confirmed bookings)
$reviews_stmt = $pdo->prepare("
    SELECT b.*, p.business_name, p.district, p.provider_id,
           r.review_id, r.rating, r.comment, r.created_at as review_date
    FROM bookings b
    JOIN providers p ON b.provider_id = p.provider_id
    LEFT JOIN reviews r ON b.booking_id = r.booking_id AND r.parent_id = ?
    WHERE b.parent_id = ? AND b.status = 'completed'
    ORDER BY b.booking_date DESC
");
$reviews_stmt->execute([$_SESSION['user_id'], $_SESSION['user_id']]);
$reviews = $reviews_stmt->fetchAll();

// Handle review submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_review'])) {
    $booking_id = $_POST['booking_id'];
    $provider_id = $_POST['provider_id'];
    $rating = $_POST['rating'];
    $comment = $_POST['comment'];

    try {
        // Check if review already exists
        $check_stmt = $pdo->prepare("SELECT review_id FROM reviews WHERE booking_id = ? AND parent_id = ?");
        $check_stmt->execute([$booking_id, $_SESSION['user_id']]);

        if ($check_stmt->rowCount() > 0) {
            // Update existing review
            $update_stmt = $pdo->prepare("
                UPDATE reviews
                SET rating = ?, comment = ?, updated_at = NOW()
                WHERE booking_id = ? AND parent_id = ?
            ");
            $update_stmt->execute([$rating, $comment, $booking_id, $_SESSION['user_id']]);
            $success_message = "Review updated successfully!";
        } else {
            // Insert new review
            $insert_stmt = $pdo->prepare("
                INSERT INTO reviews (booking_id, parent_id, provider_id, rating, comment, created_at)
                VALUES (?, ?, ?, ?, ?, NOW())
            ");
            $insert_stmt->execute([$booking_id, $_SESSION['user_id'], $provider_id, $rating, $comment]);
            $success_message = "Review submitted successfully!";
        }

        // Refresh the page to show updated data
        header("Location: reviews.php?success=1");
        exit();

    } catch (Exception $e) {
        $error_message = "Error submitting review: " . $e->getMessage();
    }
}

$success_message = isset($_GET['success']) ? "Review submitted successfully!" : '';
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>My Reviews - Nestlings</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="" name="keywords">
    <meta content="" name="description">

    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600&family=Nunito:wght@600;700;800&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">
    <link href="../lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        .review-card {
            transition: all 0.3s ease;
            border-radius: 15px;
            border: none;
            margin-bottom: 20px;
        }
        
        .review-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .rating-stars {
            color: #ffc107;
        }
        
        .user-dropdown {
            border-radius: 25px;
            padding: 8px 20px;
            background: linear-gradient(135deg, var(--primary) 0%, #6BB6FF 100%);
            border: none;
            color: white;
            font-weight: 500;
        }
        
        .user-dropdown:hover {
            background: linear-gradient(135deg, #6BB6FF 0%, var(--primary) 100%);
            color: white;
        }

        .rating-input {
            display: flex;
            gap: 5px;
            margin-bottom: 10px;
        }

        .rating-input input[type="radio"] {
            display: none;
        }

        .rating-input .star-label {
            font-size: 1.5rem;
            color: #ddd;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .rating-input input[type="radio"]:checked ~ .star-label,
        .rating-input input[type="radio"]:checked + .star-label,
        .rating-input .star-label:hover {
            color: #ffc107;
        }

        .rating-input input[type="radio"]:checked + .star-label ~ .star-label {
            color: #ddd;
        }
    </style>
</head>

<body>
    <!-- Spinner Start -->
    <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>
    <!-- Spinner End -->

    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light shadow sticky-top p-0">
        <a href="../index.html" class="navbar-brand d-flex align-items-center px-4 px-lg-5">
            <h2 class="m-0 text-primary"><i class="fa fa-baby me-3"></i>Nestlings</h2>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="dashboard.php" class="nav-item nav-link">Home</a>
                <a href="search.php" class="nav-item nav-link">Find DayCare</a>
                <a href="bookings.php" class="nav-item nav-link">My Bookings</a>
                <a href="favorites.php" class="nav-item nav-link">Favorites</a>
                <a href="reviews.php" class="nav-item nav-link active">Reviews</a>
            </div>
            <div class="dropdown">
                <button class="btn user-dropdown dropdown-toggle d-flex align-items-center" type="button" data-bs-toggle="dropdown">
                    <i class="fa fa-user-circle me-2"></i>
                    <span><?php echo htmlspecialchars($parent['name']); ?></span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="profile.php"><i class="fa fa-user me-2"></i>My Profile</a></li>
                    <li><a class="dropdown-item" href="settings.php"><i class="fa fa-cog me-2"></i>Settings</a></li>
                    <li><a class="dropdown-item" href="help.php"><i class="fa fa-question-circle me-2"></i>Help</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="../logout.php"><i class="fa fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Header Start -->
    <div class="container-fluid bg-primary py-5 mb-5 page-header">
        <div class="container py-5">
            <div class="row justify-content-center">
                <div class="col-lg-10 text-center">
                    <h1 class="display-3 text-white animated slideInDown">My Reviews</h1>
                    <p class="text-white">Your feedback and experiences with daycare providers</p>
                </div>
            </div>
        </div>
    </div>
    <!-- Header End -->

    <!-- Reviews Content Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h5 class="mb-0">
                    <i class="fa fa-star text-warning me-2"></i>
                    <?php echo count($reviews); ?> Review<?php echo count($reviews) != 1 ? 's' : ''; ?>
                </h5>
            </div>

            <?php if (empty($reviews)): ?>
                <div class="text-center py-5">
                    <i class="fa fa-star fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No reviews yet</h4>
                    <p class="text-muted">Complete a booking to leave your first review</p>
                    <a href="search.php" class="btn btn-primary">Find DayCare Providers</a>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($reviews as $review): ?>
                    <div class="col-12">
                        <div class="review-card card shadow"
                             data-review-id="<?php echo $review['review_id']; ?>"
                             data-booking-id="<?php echo $review['booking_id']; ?>"
                             data-provider-id="<?php echo $review['provider_id']; ?>"
                             data-rating="<?php echo $review['rating']; ?>"
                             data-comment="<?php echo htmlspecialchars($review['comment'] ?? ''); ?>">
                            <div class="card-body p-4">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                                <i class="fa fa-building text-white"></i>
                                            </div>
                                            <div>
                                                <h5 class="card-title mb-1"><?php echo htmlspecialchars($review['business_name']); ?></h5>
                                                <small class="text-muted">
                                                    <i class="fa fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($review['district']); ?>
                                                </small>
                                            </div>
                                        </div>
                                        
                                        <div class="rating-stars mb-3">
                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                                <i class="fa fa-star<?php echo $i <= $review['rating'] ? '' : '-o'; ?>"></i>
                                            <?php endfor; ?>
                                            <span class="ms-2 text-muted"><?php echo $review['rating']; ?>/5</span>
                                        </div>
                                        
                                        <?php if (!empty($review['comment'])): ?>
                                        <div class="mb-3">
                                            <p class="text-muted mb-0"><?php echo nl2br(htmlspecialchars($review['comment'])); ?></p>
                                        </div>
                                        <?php endif; ?>
                                        
                                        <div class="row g-3">
                                            <div class="col-sm-6">
                                                <small class="text-muted d-block">Child</small>
                                                <strong><?php echo htmlspecialchars($review['child_name']); ?></strong>
                                            </div>
                                            <div class="col-sm-6">
                                                <small class="text-muted d-block">Service Date</small>
                                                <strong><?php echo date('M d, Y', strtotime($review['booking_date'])); ?></strong>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="d-flex flex-column h-100">
                                            <div class="mb-3">
                                                <small class="text-muted d-block">Review Date</small>
                                                <strong><?php echo date('M d, Y', strtotime($review['created_at'])); ?></strong>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <small class="text-muted d-block">Privacy</small>
                                                <span class="badge <?php echo $review['is_anonymous'] ? 'bg-secondary' : 'bg-primary'; ?>">
                                                    <?php echo $review['is_anonymous'] ? 'Anonymous' : 'Public'; ?>
                                                </span>
                                            </div>
                                            
                                            <div class="mt-auto">
                                                <div class="d-grid gap-2">
                                                    <a href="provider_details.php?id=<?php echo $review['provider_id']; ?>" class="btn btn-outline-primary btn-sm">
                                                        <i class="fa fa-eye me-1"></i>View Provider
                                                    </a>
                                                    <button class="btn btn-outline-secondary btn-sm" onclick="editReview(<?php echo $review['review_id']; ?>)">
                                                        <i class="fa fa-edit me-1"></i>Edit Review
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <!-- Reviews Content End -->

    <!-- Footer Start -->
    <div class="container-fluid bg-dark text-light footer pt-5 mt-5 wow fadeIn" data-wow-delay="0.1s">
        <div class="container py-5">
            <div class="row g-5">
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3"><i class="fa fa-baby me-2"></i>Nestlings</h4>
                    <p>Connecting families with trusted childcare providers across Sri Lanka. Safe, verified, and reliable daycare solutions for your little ones.</p>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Quick Links</h4>
                    <a class="btn btn-link" href="../about.html">About Us</a>
                    <a class="btn btn-link" href="../contact.html">Contact Us</a>
                    <a class="btn btn-link" href="../services.html">Our Services</a>
                    <a class="btn btn-link" href="search.php">Find DayCare</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Support</h4>
                    <a class="btn btn-link" href="help.php">Help Center</a>
                    <a class="btn btn-link" href="">Safety Guidelines</a>
                    <a class="btn btn-link" href="">Provider Resources</a>
                    <a class="btn btn-link" href="">Parent Guide</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Contact Info</h4>
                    <p class="mb-2"><i class="fa fa-map-marker-alt me-3"></i>123 Main Street, Colombo, Sri Lanka</p>
                    <p class="mb-2"><i class="fa fa-phone-alt me-3"></i>+94 11 234 5678</p>
                    <p class="mb-2"><i class="fa fa-envelope me-3"></i><EMAIL></p>
                </div>
            </div>
        </div>
    </div>
    <!-- Footer End -->

    <!-- Back to Top -->
    <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/owlcarousel/owl.carousel.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>
    
    <!-- Edit Review Modal -->
    <div class="modal fade" id="editReviewModal" tabindex="-1" aria-labelledby="editReviewModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editReviewModalLabel">
                        <i class="fa fa-edit me-2"></i>Edit Review
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" id="editReviewForm">
                    <div class="modal-body">
                        <input type="hidden" name="submit_review" value="1">
                        <input type="hidden" name="booking_id" id="edit_booking_id">
                        <input type="hidden" name="provider_id" id="edit_provider_id">

                        <div class="mb-3">
                            <label class="form-label">Rating</label>
                            <div class="rating-input">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <input type="radio" name="rating" value="<?php echo $i; ?>" id="edit_star<?php echo $i; ?>" required>
                                    <label for="edit_star<?php echo $i; ?>" class="star-label">
                                        <i class="fa fa-star"></i>
                                    </label>
                                <?php endfor; ?>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="edit_comment" class="form-label">Review Comment</label>
                            <textarea class="form-control" name="comment" id="edit_comment" rows="4"
                                    placeholder="Share your experience with this daycare provider..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fa fa-save me-2"></i>Update Review
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function editReview(reviewId) {
            // Find the review data from the page
            const reviewCard = document.querySelector(`[data-review-id="${reviewId}"]`);
            if (!reviewCard) {
                console.error('Review card not found for ID:', reviewId);
                return;
            }

            // Extract data from the review card
            const bookingId = reviewCard.dataset.bookingId;
            const providerId = reviewCard.dataset.providerId;
            const currentRating = parseInt(reviewCard.dataset.rating);
            const currentComment = reviewCard.dataset.comment || '';

            // Populate the modal
            document.getElementById('edit_booking_id').value = bookingId;
            document.getElementById('edit_provider_id').value = providerId;
            document.getElementById('edit_comment').value = currentComment;

            // Set the rating
            const ratingInput = document.querySelector(`input[name="rating"][value="${currentRating}"]`);
            if (ratingInput) {
                ratingInput.checked = true;
            }

            // Show the modal
            const modal = new bootstrap.Modal(document.getElementById('editReviewModal'));
            modal.show();
        }
    </script>
</body>

</html>
