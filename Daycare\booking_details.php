<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a daycare provider
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'provider') {
    header("Location: ../login.php");
    exit();
}

$provider_id = $_SESSION['user_id'];
$booking_id = $_GET['id'] ?? null;

if (!$booking_id) {
    header("Location: payments.php");
    exit();
}

// Get booking details
$stmt = $pdo->prepare("
    SELECT
        b.*,
        p.name as parent_name,
        p.email as parent_email,
        p.phone as parent_phone,
        p.address as parent_address,
        pr.business_name,
        pay.payment_date,
        pay.card_last_four as card_number,
        pay.transaction_id,
        'online' as payment_method,
        CASE
            WHEN b.payment_status = 'paid' THEN b.total_cost
            ELSE 0
        END as paid_amount,
        CASE
            WHEN b.payment_status = 'pending' THEN b.total_cost
            ELSE 0
        END as outstanding_amount
    FROM bookings b
    JOIN parents p ON b.parent_id = p.parent_id
    JOIN providers pr ON b.provider_id = pr.provider_id
    LEFT JOIN payments pay ON b.booking_id = pay.booking_id
    WHERE b.booking_id = ? AND b.provider_id = ?
");
$stmt->execute([$booking_id, $provider_id]);
$booking = $stmt->fetch();

if (!$booking) {
    header("Location: payments.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Booking Details - Nestlings</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600&family=Inter:wght@600&family=Lobster+Two:wght@700&display=swap" rel="stylesheet">
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">
    <link href="../lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        .detail-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 0 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .detail-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
        }
        
        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 500;
            text-transform: uppercase;
            font-size: 0.85rem;
        }
        
        .status-confirmed { background: #d4edda; color: #155724; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-completed { background: #d1ecf1; color: #0c5460; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
        
        .payment-status {
            padding: 6px 12px;
            border-radius: 15px;
            font-weight: 500;
            font-size: 0.85rem;
        }
        
        .payment-paid { background: #d4edda; color: #155724; }
        .payment-pending { background: #fff3cd; color: #856404; }
        .payment-partial { background: #ffeaa7; color: #d63031; }
        
        .info-row {
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .amount-highlight {
            font-size: 1.2rem;
            font-weight: bold;
        }
        
        .amount-paid { color: #28a745; }
        .amount-pending { color: #dc3545; }
        .amount-total { color: #007bff; }
    </style>
</head>

<body>
    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light sticky-top px-4 px-lg-5 py-lg-0">
        <a href="dashboard.php" class="navbar-brand">
            <h1 class="m-0 text-primary"><i class="fa fa-baby me-3"></i>Nestlings</h1>
        </a>
        <button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav mx-auto">
                <a href="dashboard.php" class="nav-item nav-link">Dashboard</a>
                <a href="bookings.php" class="nav-item nav-link">Bookings</a>
                <a href="payments.php" class="nav-item nav-link active">Payments</a>
                <a href="analytics.php" class="nav-item nav-link">Analytics</a>
            </div>
            <div class="nav-item dropdown">
                <a href="#" class="nav-link dropdown-toggle user-dropdown" data-bs-toggle="dropdown">
                    <i class="fa fa-user me-2"></i><?php echo htmlspecialchars($_SESSION['business_name'] ?? 'Provider'); ?>
                </a>
                <div class="dropdown-menu m-0">
                    <a href="profile.php" class="dropdown-item">Profile</a>
                    <a href="../logout.php" class="dropdown-item">Logout</a>
                </div>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Page Header Start -->
    <div class="container-xxl py-5 page-header position-relative mb-5">
        <div class="container py-5">
            <h1 class="display-2 text-white animated slideInDown mb-4">Booking Details</h1>
            <nav aria-label="breadcrumb animated slideInDown">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="payments.php">Payments</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Booking #<?php echo $booking_id; ?></li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header End -->

    <!-- Booking Details Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <div class="row">
                <!-- Booking Information -->
                <div class="col-lg-8">
                    <div class="detail-card">
                        <div class="detail-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h4 class="mb-1">Booking #<?php echo $booking['booking_id']; ?></h4>
                                    <p class="mb-0">Booking Details and Information</p>
                                </div>
                                <div class="text-end">
                                    <div class="status-badge status-<?php echo $booking['status']; ?> mb-2">
                                        <?php echo ucfirst($booking['status']); ?>
                                    </div>
                                    <div class="payment-status payment-<?php echo $booking['payment_status']; ?>">
                                        <?php echo ucfirst($booking['payment_status']); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="info-row">
                                <div class="row">
                                    <div class="col-md-3"><strong>Booking Date:</strong></div>
                                    <div class="col-md-9"><?php echo date('F d, Y \a\t g:i A', strtotime($booking['booking_date'])); ?></div>
                                </div>
                            </div>
                            <div class="info-row">
                                <div class="row">
                                    <div class="col-md-3"><strong>Child Name:</strong></div>
                                    <div class="col-md-9"><?php echo htmlspecialchars($booking['child_name']); ?></div>
                                </div>
                            </div>
                            <div class="info-row">
                                <div class="row">
                                    <div class="col-md-3"><strong>Child Age:</strong></div>
                                    <div class="col-md-9"><?php echo htmlspecialchars($booking['child_age']); ?> years old</div>
                                </div>
                            </div>
                            <div class="info-row">
                                <div class="row">
                                    <div class="col-md-3"><strong>Service Type:</strong></div>
                                    <div class="col-md-9"><?php echo htmlspecialchars($booking['service_type']); ?></div>
                                </div>
                            </div>
                            <div class="info-row">
                                <div class="row">
                                    <div class="col-md-3"><strong>Duration:</strong></div>
                                    <div class="col-md-9"><?php echo htmlspecialchars($booking['duration']); ?></div>
                                </div>
                            </div>
                            <?php if ($booking['special_requirements']): ?>
                            <div class="info-row">
                                <div class="row">
                                    <div class="col-md-3"><strong>Special Requirements:</strong></div>
                                    <div class="col-md-9"><?php echo htmlspecialchars($booking['special_requirements']); ?></div>
                                </div>
                            </div>
                            <?php endif; ?>
                            <div class="info-row">
                                <div class="row">
                                    <div class="col-md-3"><strong>Created:</strong></div>
                                    <div class="col-md-9"><?php echo date('F d, Y \a\t g:i A', strtotime($booking['created_at'])); ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment & Parent Information -->
                <div class="col-lg-4">
                    <!-- Payment Summary -->
                    <div class="detail-card">
                        <div class="detail-header">
                            <h5 class="mb-0"><i class="fa fa-credit-card me-2"></i>Payment Summary</h5>
                        </div>
                        <div class="p-4">
                            <div class="info-row">
                                <div class="d-flex justify-content-between">
                                    <span>Total Amount:</span>
                                    <span class="amount-highlight amount-total">Rs. <?php echo number_format($booking['total_cost'], 2); ?></span>
                                </div>
                            </div>
                            <div class="info-row">
                                <div class="d-flex justify-content-between">
                                    <span>Paid Amount:</span>
                                    <span class="amount-highlight amount-paid">Rs. <?php echo number_format($booking['paid_amount'], 2); ?></span>
                                </div>
                            </div>
                            <div class="info-row">
                                <div class="d-flex justify-content-between">
                                    <span>Outstanding:</span>
                                    <span class="amount-highlight amount-pending">Rs. <?php echo number_format($booking['outstanding_amount'], 2); ?></span>
                                </div>
                            </div>
                            <?php if ($booking['payment_date']): ?>
                            <div class="info-row">
                                <div class="d-flex justify-content-between">
                                    <span>Payment Date:</span>
                                    <span><?php echo date('M d, Y', strtotime($booking['payment_date'])); ?></span>
                                </div>
                            </div>
                            <?php endif; ?>
                            <?php if ($booking['transaction_id']): ?>
                            <div class="info-row">
                                <div class="d-flex justify-content-between">
                                    <span>Transaction ID:</span>
                                    <span><code><?php echo htmlspecialchars($booking['transaction_id']); ?></code></span>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Parent Information -->
                    <div class="detail-card">
                        <div class="detail-header">
                            <h5 class="mb-0"><i class="fa fa-user me-2"></i>Parent Information</h5>
                        </div>
                        <div class="p-4">
                            <div class="info-row">
                                <div><strong>Name:</strong></div>
                                <div><?php echo htmlspecialchars($booking['parent_name']); ?></div>
                            </div>
                            <div class="info-row">
                                <div><strong>Email:</strong></div>
                                <div><a href="mailto:<?php echo htmlspecialchars($booking['parent_email']); ?>"><?php echo htmlspecialchars($booking['parent_email']); ?></a></div>
                            </div>
                            <div class="info-row">
                                <div><strong>Phone:</strong></div>
                                <div><a href="tel:<?php echo htmlspecialchars($booking['parent_phone']); ?>"><?php echo htmlspecialchars($booking['parent_phone']); ?></a></div>
                            </div>
                            <?php if ($booking['parent_address']): ?>
                            <div class="info-row">
                                <div><strong>Address:</strong></div>
                                <div><?php echo htmlspecialchars($booking['parent_address']); ?></div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Actions -->
                    <?php if ($booking['outstanding_amount'] > 0): ?>
                    <div class="detail-card">
                        <div class="detail-header">
                            <h5 class="mb-0"><i class="fa fa-cogs me-2"></i>Actions</h5>
                        </div>
                        <div class="p-4">
                            <button class="btn btn-warning w-100 mb-2" onclick="sendPaymentReminder(<?php echo $booking['booking_id']; ?>)">
                                <i class="fa fa-bell me-2"></i>Send Payment Reminder
                            </button>
                            <a href="payments.php" class="btn btn-secondary w-100">
                                <i class="fa fa-arrow-left me-2"></i>Back to Payments
                            </a>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <!-- Booking Details End -->

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/owlcarousel/owl.carousel.min.js"></script>
    <script src="../js/main.js"></script>

    <script>
        function sendPaymentReminder(bookingId) {
            if (confirm('Send payment reminder to the parent?')) {
                fetch('send_payment_reminder.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        booking_id: bookingId,
                        action: 'send_reminder'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Payment reminder sent successfully!');
                    } else {
                        alert('Error sending reminder: ' + (data.message || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error sending reminder. Please try again.');
                });
            }
        }
    </script>
</body>
</html>
