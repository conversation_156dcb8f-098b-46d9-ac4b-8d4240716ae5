<div class="booking-card card shadow">
    <div class="card-body p-4">
        <div class="row">
            <div class="col-md-8">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div>
                        <h5 class="card-title mb-1"><?php echo htmlspecialchars($booking['business_name']); ?></h5>
                        <small class="text-muted">
                            <i class="fa fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($booking['district']); ?>
                        </small>
                    </div>
                    <span class="status-badge status-<?php echo $booking['status']; ?>">
                        <?php echo ucfirst($booking['status']); ?>
                    </span>
                </div>
                
                <div class="row g-3 mb-3">
                    <div class="col-sm-6">
                        <small class="text-muted d-block">Child Name</small>
                        <strong><?php echo htmlspecialchars($booking['child_name']); ?></strong>
                    </div>
                    <div class="col-sm-6">
                        <small class="text-muted d-block">Age</small>
                        <strong><?php echo $booking['child_age']; ?> years old</strong>
                    </div>
                    <div class="col-sm-6">
                        <small class="text-muted d-block">Service Type</small>
                        <strong><?php echo htmlspecialchars($booking['service_type']); ?></strong>
                    </div>
                    <div class="col-sm-6">
                        <small class="text-muted d-block">Booking Date</small>
                        <strong><?php echo date('M d, Y', strtotime($booking['booking_date'])); ?></strong>
                    </div>
                </div>
                
                <div class="row g-3 mb-3">
                    <div class="col-sm-6">
                        <small class="text-muted d-block">Time</small>
                        <strong>
                            <?php echo date('g:i A', strtotime($booking['start_time'])); ?> - 
                            <?php echo date('g:i A', strtotime($booking['end_time'])); ?>
                        </strong>
                    </div>
                    <div class="col-sm-6">
                        <small class="text-muted d-block">Total Amount</small>
                        <strong>LKR <?php echo number_format($booking['total_cost'], 2); ?></strong>
                    </div>
                </div>

                <!-- Payment Status Section -->
                <?php if (isset($booking['payment_status'])): ?>
                <div class="row g-3 mb-3">
                    <div class="col-sm-4">
                        <small class="text-muted d-block">Payment Status</small>
                        <span class="payment-status status-<?php echo $booking['payment_status']; ?>">
                            <?php echo ucfirst($booking['payment_status']); ?>
                        </span>
                    </div>
                    <div class="col-sm-4">
                        <small class="text-muted d-block">Paid Amount</small>
                        <strong class="text-success">LKR <?php echo number_format($booking['paid_amount'], 2); ?></strong>
                    </div>
                    <div class="col-sm-4">
                        <small class="text-muted d-block">Remaining</small>
                        <strong class="text-primary">LKR <?php echo number_format($booking['remaining_amount'], 2); ?></strong>
                    </div>
                </div>
                <?php endif; ?>

                <?php if (!empty($booking['special_requirements'])): ?>
                <div class="mb-3">
                    <small class="text-muted d-block">Special Requirements</small>
                    <p class="mb-0"><?php echo htmlspecialchars($booking['special_requirements']); ?></p>
                </div>
                <?php endif; ?>
            </div>
            
            <div class="col-md-4">
                <div class="d-flex flex-column h-100">
                    <div class="mb-3">
                        <small class="text-muted d-block">Booking ID</small>
                        <strong>#<?php echo str_pad($booking['booking_id'], 6, '0', STR_PAD_LEFT); ?></strong>
                    </div>
                    
                    <div class="mb-3">
                        <small class="text-muted d-block">Created</small>
                        <strong><?php echo date('M d, Y', strtotime($booking['created_at'])); ?></strong>
                    </div>
                    
                    <div class="mt-auto">
                        <div class="d-grid gap-2">
                            <a href="booking_details.php?id=<?php echo $booking['booking_id']; ?>" class="btn btn-outline-primary btn-sm">
                                <i class="fa fa-eye me-1"></i>View Details
                            </a>

                            <!-- Status-based Actions -->
                            <?php if ($booking['status'] == 'pending'): ?>
                                <div class="alert alert-info p-2 mb-2">
                                    <small><i class="fa fa-clock me-1"></i>Waiting for provider confirmation</small>
                                </div>
                                <button class="btn btn-outline-danger btn-sm" onclick="cancelBooking(<?php echo $booking['booking_id']; ?>)">
                                    <i class="fa fa-times me-1"></i>Cancel
                                </button>
                            <?php elseif ($booking['status'] == 'confirmed'): ?>
                                <!-- Payment Actions - Only available after confirmation -->
                                <?php if (isset($booking['payment_status']) && $booking['remaining_amount'] > 0): ?>
                                    <a href="payment_confirmation.php?booking_id=<?php echo $booking['booking_id']; ?>" class="btn btn-warning btn-sm">
                                        <i class="fa fa-credit-card me-1"></i>Make Payment
                                    </a>
                                <?php else: ?>
                                    <div class="alert alert-success p-2 mb-2">
                                        <small><i class="fa fa-check me-1"></i>Payment completed</small>
                                    </div>
                                <?php endif; ?>
                            <?php elseif ($booking['status'] == 'completed'): ?>
                                <a href="reviews.php?booking_id=<?php echo $booking['booking_id']; ?>" class="btn btn-outline-warning btn-sm">
                                    <i class="fa fa-star me-1"></i>Add Review
                                </a>
                            <?php elseif ($booking['status'] == 'cancelled'): ?>
                                <div class="alert alert-danger p-2 mb-2">
                                    <small><i class="fa fa-times me-1"></i>Booking cancelled</small>
                                </div>
                            <?php endif; ?>
                            
                            <a href="tel:<?php echo $booking['provider_phone']; ?>" class="btn btn-outline-success btn-sm">
                                <i class="fa fa-phone me-1"></i>Call Provider
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
