<?php
session_start();

// Check if user is logged in and is a parent
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'parent') {
    header("Location: ../login.php");
    exit();
}

require_once '../config/database.php';

$booking_id = $_GET['booking_id'] ?? null;
if (!$booking_id) {
    header("Location: bookings.php");
    exit();
}

// Get parent information
$stmt = $pdo->prepare("SELECT * FROM parents WHERE parent_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$parent = $stmt->fetch();

// Get booking details with provider information
$stmt = $pdo->prepare("
    SELECT b.*, p.business_name, p.district, p.phone as provider_phone, p.email as provider_email
    FROM bookings b 
    JOIN providers p ON b.provider_id = p.provider_id 
    WHERE b.booking_id = ? AND b.parent_id = ?
");
$stmt->execute([$booking_id, $_SESSION['user_id']]);
$booking = $stmt->fetch();

if (!$booking) {
    header("Location: bookings.php");
    exit();
}

// Check if booking is confirmed before allowing payment
if ($booking['status'] !== 'confirmed') {
    $_SESSION['error_message'] = "Payment is only available for confirmed bookings.";
    header("Location: booking_details.php?id=" . $booking_id);
    exit();
}

// Initialize payment fields if they don't exist or are null
if (!isset($booking['payment_status']) || $booking['payment_status'] === null) {
    $booking['payment_status'] = 'unpaid';
}
if (!isset($booking['paid_amount']) || $booking['paid_amount'] === null) {
    $booking['paid_amount'] = 0;
}
if (!isset($booking['remaining_amount']) || $booking['remaining_amount'] === null) {
    $booking['remaining_amount'] = $booking['total_cost'];
}

// Handle payment processing
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['process_payment'])) {
    $payment_type = $_POST['payment_type'] ?? 'full'; // 'partial' or 'full' - default to full
    $payment_amount = $_POST['payment_amount'] ?? 0;

    // Get card details (for security, only store last 4 digits)
    $card_number = $_POST['card_number'] ?? '';
    $card_holder = $_POST['card_holder'] ?? '';
    $card_last_four = substr(str_replace(' ', '', $card_number), -4);

    // Validate payment amount
    if ($payment_amount <= 0 || $payment_amount > $booking['remaining_amount']) {
        $error_message = "Invalid payment amount.";
    } else {
        try {
            $pdo->beginTransaction();

            // Debug: Log payment attempt
            error_log("Processing payment: Amount=$payment_amount, Type=$payment_type, Booking=$booking_id");
            error_log("User ID: " . $_SESSION['user_id'] . ", Provider ID: " . $booking['provider_id']);

            // Validate required data
            if (empty($booking['provider_id'])) {
                throw new Exception("Provider ID is missing");
            }
            if (empty($_SESSION['user_id'])) {
                throw new Exception("User session is invalid");
            }

            // Insert payment record
            $payment_stmt = $pdo->prepare("
                INSERT INTO payments (booking_id, parent_id, provider_id, amount, payment_type, payment_status, payment_date, transaction_id, card_last_four, card_holder_name)
                VALUES (?, ?, ?, ?, ?, 'completed', NOW(), ?, ?, ?)
            ");
            $transaction_id = 'TXN_' . time() . '_' . $booking_id;
            $payment_stmt->execute([
                $booking_id,
                $_SESSION['user_id'],
                $booking['provider_id'],
                $payment_amount,
                $payment_type,
                $transaction_id,
                $card_last_four,
                $card_holder
            ]);
            
            // Update booking payment status
            $new_paid_amount = $booking['paid_amount'] + $payment_amount;
            $new_remaining_amount = $booking['total_cost'] - $new_paid_amount;
            
            $payment_status = 'unpaid';
            if ($new_remaining_amount <= 0) {
                $payment_status = 'paid';
            } elseif ($new_paid_amount > 0) {
                $payment_status = 'partial';
            }
            
            $update_stmt = $pdo->prepare("
                UPDATE bookings 
                SET paid_amount = ?, remaining_amount = ?, payment_status = ?
                WHERE booking_id = ?
            ");
            $update_stmt->execute([$new_paid_amount, $new_remaining_amount, $payment_status, $booking_id]);
            
            // Create notification for provider
            $notification_stmt = $pdo->prepare("
                INSERT INTO notifications (user_type, user_id, title, message, type, created_at)
                VALUES ('provider', ?, 'Payment Received', ?, 'payment', NOW())
            ");
            $notification_message = "Payment of LKR " . number_format($payment_amount, 2) . " received from " . htmlspecialchars($parent['name']) . " for booking #" . $booking_id;
            $notification_stmt->execute([$booking['provider_id'], $notification_message]);
            
            $pdo->commit();
            
            $success_message = "🎉 Payment Successful! LKR " . number_format($payment_amount, 2) . " has been processed successfully. Thank you for your payment!";
            
            // Refresh booking data
            $stmt = $pdo->prepare("
                SELECT b.*, p.business_name, p.district, p.phone as provider_phone, p.email as provider_email
                FROM bookings b 
                JOIN providers p ON b.provider_id = p.provider_id 
                WHERE b.booking_id = ? AND b.parent_id = ?
            ");
            $stmt->execute([$booking_id, $_SESSION['user_id']]);
            $booking = $stmt->fetch();
            
        } catch (Exception $e) {
            $pdo->rollBack();
            $error_message = "Payment processing failed: " . $e->getMessage();
            // Debug: Log the actual error
            error_log("Payment processing error: " . $e->getMessage());
        }
    }
}

// Get payment history for this booking
$payment_history = [];
try {
    $payment_history_stmt = $pdo->prepare("
        SELECT * FROM payments
        WHERE booking_id = ?
        ORDER BY created_at DESC
    ");
    $payment_history_stmt->execute([$booking_id]);
    $payment_history = $payment_history_stmt->fetchAll();
} catch (Exception $e) {
    // Payments table might not exist yet
    error_log("Payment history query error: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Payment Confirmation - Nestlings</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600&family=Inter:wght@600;700;800&display=swap" rel="stylesheet">
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">
    <link href="../lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">
    
    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        .payment-card {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            background: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }
        
        .payment-option {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .payment-option:hover {
            border-color: var(--primary);
            background: #f8f9fa;
        }
        
        .payment-option.selected {
            border-color: var(--primary);
            background: rgba(13, 110, 253, 0.1);
        }
        
        .booking-summary {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            border-radius: 15px;
            padding: 25px;
        }
        
        .payment-status {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        .status-unpaid { background: #ffeaa7; color: #d63031; }
        .status-partial { background: #74b9ff; color: white; }
        .status-paid { background: #00b894; color: white; }
        
        .amount-input {
            font-size: 1.2rem;
            font-weight: 600;
            text-align: center;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
        }
        
        .amount-input:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }
    </style>
</head>

<body>
    <div class="container-xxl bg-white p-0">
        <!-- Spinner Start -->
        <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
            <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                <span class="sr-only">Loading...</span>
            </div>
        </div>
        <!-- Spinner End -->

        <!-- Navbar Start -->
        <nav class="navbar navbar-expand-lg bg-white navbar-light shadow sticky-top p-0">
            <a href="../index.html" class="navbar-brand d-flex align-items-center px-4 px-lg-5">
                <h2 class="m-0 text-primary"><i class="fa fa-baby me-3"></i>Nestlings</h2>
            </a>
            <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarCollapse">
                <div class="navbar-nav ms-auto p-4 p-lg-0">
                    <a href="dashboard.php" class="nav-item nav-link">Home</a>
                    <a href="search.php" class="nav-item nav-link">Find DayCare</a>
                    <a href="bookings.php" class="nav-item nav-link">My Bookings</a>
                    <a href="favorites.php" class="nav-item nav-link">Favorites</a>
                </div>
                <div class="d-flex align-items-center gap-3">
                    <?php include '../components/notifications.php'; ?>
                    <div class="dropdown">
                        <button class="btn btn-primary dropdown-toggle me-4" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fa fa-user me-2"></i>
                            <span><?php echo htmlspecialchars($parent['name']); ?></span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php"><i class="fa fa-user me-2"></i>My Profile</a></li>
                            <li><a class="dropdown-item" href="bookings.php"><i class="fa fa-calendar me-2"></i>My Bookings</a></li>
                            <li><a class="dropdown-item" href="favorites.php"><i class="fa fa-heart me-2"></i>Favorites</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fa fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>
        <!-- Navbar End -->

        <!-- Header Start -->
        <div class="container-fluid header bg-primary p-0 mb-5">
            <div class="row g-0 align-items-center flex-column-reverse flex-lg-row">
                <div class="col-lg-6 p-5 wow fadeIn" data-wow-delay="0.1s">
                    <h1 class="display-4 text-white mb-5">Payment Confirmation</h1>
                    <div class="row g-4">
                        <div class="col-sm-3">
                            <div class="border-start border-light ps-4">
                                <h2 class="text-light mb-1">LKR</h2>
                                <p class="text-light mb-0"><?php echo number_format($booking['total_cost'], 0); ?></p>
                                <small class="text-light">Total Amount</small>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="border-start border-light ps-4">
                                <h2 class="text-light mb-1">LKR</h2>
                                <p class="text-light mb-0"><?php echo number_format($booking['paid_amount'], 0); ?></p>
                                <small class="text-light">Paid Amount</small>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="border-start border-light ps-4">
                                <h2 class="text-light mb-1">LKR</h2>
                                <p class="text-light mb-0"><?php echo number_format($booking['remaining_amount'], 0); ?></p>
                                <small class="text-light">Remaining</small>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="border-start border-light ps-4">
                                <span class="payment-status status-<?php echo $booking['payment_status']; ?>">
                                    <?php echo ucfirst($booking['payment_status']); ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 text-center text-lg-end overflow-hidden">
                    <img class="img-fluid" src="../img/hero.png" alt="">
                </div>
            </div>
        </div>
        <!-- Header End -->

        <!-- Payment Confirmation Start -->
        <div class="container-xxl py-5">
            <div class="container">
                <?php if ($success_message): ?>
                    <div class="alert alert-success alert-dismissible fade show border-0 shadow-lg" role="alert"
                         style="background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); border-radius: 15px; padding: 20px; margin-bottom: 30px;">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fa fa-check-circle" style="font-size: 2rem; color: #155724;"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h5 class="alert-heading mb-1" style="color: #155724;">Payment Successful!</h5>
                                <p class="mb-0" style="color: #155724;"><?php echo $success_message; ?></p>
                            </div>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error_message): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fa fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="row g-5">
                    <div class="col-lg-8">
                        <!-- Booking Summary -->
                        <div class="booking-summary mb-4">
                            <h4 class="mb-3"><i class="fa fa-calendar-check me-2"></i>Booking Confirmed</h4>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Provider:</strong> <?php echo htmlspecialchars($booking['business_name']); ?></p>
                                    <p class="mb-1"><strong>Child:</strong> <?php echo htmlspecialchars($booking['child_name']); ?></p>
                                    <p class="mb-1"><strong>Service:</strong> <?php echo ucfirst(str_replace('_', ' ', $booking['service_type'])); ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Date:</strong> <?php echo date('M d, Y', strtotime($booking['booking_date'])); ?></p>
                                    <p class="mb-1"><strong>Time:</strong> <?php echo date('g:i A', strtotime($booking['start_time'])); ?> - <?php echo date('g:i A', strtotime($booking['end_time'])); ?></p>
                                    <p class="mb-1"><strong>Duration:</strong> <?php echo $booking['duration']; ?> hours</p>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Options -->
                        <?php if ($booking['payment_status'] !== 'paid' && $booking['remaining_amount'] > 0): ?>
                        <div class="payment-card border border-primary">
                            <div class="bg-primary text-white p-3 rounded-top mb-4">
                                <h5 class="mb-0"><i class="fa fa-credit-card me-2"></i>💳 Complete Your Payment</h5>
                                <small>Secure payment processing</small>
                            </div>

                            <form method="POST" action="" id="paymentForm">
                                <input type="hidden" name="process_payment" value="1">

                                <div class="row g-4">
                                    <div class="col-md-6">
                                        <div class="payment-option" onclick="selectPaymentOption('partial')">
                                            <input type="radio" name="payment_type" value="partial" id="partial_payment" class="d-none">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-1">Partial Payment</h6>
                                                    <small class="text-muted">Pay a portion now, rest later</small>
                                                </div>
                                                <i class="fa fa-circle-o payment-radio"></i>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="payment-option" onclick="selectPaymentOption('full')">
                                            <input type="radio" name="payment_type" value="full" id="full_payment" class="d-none">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-1">Full Payment</h6>
                                                    <small class="text-muted">Pay the complete amount</small>
                                                </div>
                                                <i class="fa fa-circle-o payment-radio"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <label class="form-label">Payment Amount (LKR)</label>
                                    <input type="number" class="form-control amount-input" name="payment_amount" id="payment_amount"
                                           min="1" max="<?php echo $booking['remaining_amount']; ?>"
                                           value="<?php echo $booking['remaining_amount']; ?>" required>
                                    <small class="text-muted">Maximum: LKR <?php echo number_format($booking['remaining_amount'], 2); ?></small>
                                </div>

                                <!-- Card Details Section -->
                                <div class="mt-4">
                                    <h6 class="mb-3"><i class="fa fa-credit-card me-2"></i>Card Details</h6>
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-12 mb-3">
                                                    <label for="card_number" class="form-label">Card Number</label>
                                                    <input type="text" class="form-control card-input" id="card_number" name="card_number"
                                                           placeholder="1234 5678 9012 3456" maxlength="19" required>
                                                    <div class="form-text">
                                                        <i class="fab fa-cc-visa me-1"></i>
                                                        <i class="fab fa-cc-mastercard me-1"></i>
                                                        <i class="fab fa-cc-amex me-1"></i>
                                                        We accept major credit cards
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-8 mb-3">
                                                    <label for="card_holder" class="form-label">Cardholder Name</label>
                                                    <input type="text" class="form-control card-input" id="card_holder" name="card_holder"
                                                           placeholder="John Doe" required>
                                                </div>
                                                <div class="col-md-4 mb-3">
                                                    <label for="card_expiry" class="form-label">Expiry Date</label>
                                                    <input type="text" class="form-control card-input" id="card_expiry" name="card_expiry"
                                                           placeholder="MM/YY" maxlength="5" required>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="card_cvv" class="form-label">CVV</label>
                                                    <input type="text" class="form-control card-input" id="card_cvv" name="card_cvv"
                                                           placeholder="123" maxlength="4" required>
                                                    <div class="form-text">3-4 digits on the back of your card</div>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label for="billing_zip" class="form-label">Billing ZIP Code</label>
                                                    <input type="text" class="form-control card-input" id="billing_zip" name="billing_zip"
                                                           placeholder="10001" required>
                                                </div>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="save_card" name="save_card">
                                                <label class="form-check-label" for="save_card">
                                                    <i class="fa fa-shield-alt me-1"></i>Save card details for future payments (secure)
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <button type="submit" class="btn btn-success btn-lg w-100" style="background: linear-gradient(45deg, #28a745, #20c997); border: none; box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);">
                                        <i class="fa fa-lock me-2"></i>💳 Pay Now - Secure Payment
                                    </button>
                                    <small class="text-muted d-block text-center mt-2">
                                        <i class="fa fa-shield me-1"></i>Your payment is secured with SSL encryption
                                    </small>
                                </div>
                            </form>
                        </div>
                        <?php elseif ($booking['payment_status'] === 'paid'): ?>
                        <div class="alert alert-success">
                            <h5><i class="fa fa-check-circle me-2"></i>Payment Complete</h5>
                            <p class="mb-0">This booking has been fully paid. Thank you for your payment!</p>
                        </div>
                        <?php else: ?>
                        <div class="alert alert-warning">
                            <h5><i class="fa fa-exclamation-triangle me-2"></i>Payment Required</h5>
                            <p class="mb-0">Please complete your payment to confirm this booking.</p>
                        </div>
                        <?php endif; ?>

                        <!-- Payment History -->
                        <?php if (!empty($payment_history)): ?>
                        <div class="payment-card">
                            <h5 class="mb-4"><i class="fa fa-history me-2"></i>Payment History</h5>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Amount</th>
                                            <th>Type</th>
                                            <th>Status</th>
                                            <th>Transaction ID</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($payment_history as $payment): ?>
                                        <tr>
                                            <td><?php echo date('M d, Y g:i A', strtotime($payment['payment_date'])); ?></td>
                                            <td>LKR <?php echo number_format($payment['amount'], 2); ?></td>
                                            <td><span class="badge bg-info"><?php echo ucfirst($payment['payment_type']); ?></span></td>
                                            <td><span class="badge bg-success"><?php echo ucfirst($payment['payment_status']); ?></span></td>
                                            <td><small><?php echo htmlspecialchars($payment['transaction_id']); ?></small></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="col-lg-4">
                        <!-- Payment Summary -->
                        <div class="payment-card">
                            <h5 class="mb-4">Payment Summary</h5>
                            <div class="d-flex justify-content-between mb-3">
                                <span>Total Cost:</span>
                                <strong>LKR <?php echo number_format($booking['total_cost'], 2); ?></strong>
                            </div>
                            <div class="d-flex justify-content-between mb-3">
                                <span>Paid Amount:</span>
                                <span class="text-success">LKR <?php echo number_format($booking['paid_amount'], 2); ?></span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between mb-3">
                                <span><strong>Remaining:</strong></span>
                                <strong class="text-primary">LKR <?php echo number_format($booking['remaining_amount'], 2); ?></strong>
                            </div>

                            <div class="mt-4">
                                <span class="payment-status status-<?php echo $booking['payment_status']; ?>">
                                    <?php echo ucfirst($booking['payment_status']); ?>
                                </span>
                            </div>
                        </div>

                        <!-- Provider Contact -->
                        <div class="payment-card">
                            <h5 class="mb-3">Provider Contact</h5>
                            <p class="mb-2"><strong><?php echo htmlspecialchars($booking['business_name']); ?></strong></p>
                            <p class="mb-2"><i class="fa fa-map-marker-alt me-2"></i><?php echo htmlspecialchars($booking['district']); ?></p>
                            <p class="mb-2"><i class="fa fa-phone me-2"></i><a href="tel:<?php echo $booking['provider_phone']; ?>"><?php echo htmlspecialchars($booking['provider_phone']); ?></a></p>
                            <p class="mb-0"><i class="fa fa-envelope me-2"></i><a href="mailto:<?php echo $booking['provider_email']; ?>"><?php echo htmlspecialchars($booking['provider_email']); ?></a></p>
                        </div>

                        <!-- Quick Actions -->
                        <div class="payment-card">
                            <h5 class="mb-3">Quick Actions</h5>
                            <div class="d-grid gap-2">
                                <a href="bookings.php" class="btn btn-outline-primary">
                                    <i class="fa fa-calendar me-2"></i>View All Bookings
                                </a>
                                <a href="provider_details.php?id=<?php echo $booking['provider_id']; ?>" class="btn btn-outline-secondary">
                                    <i class="fa fa-eye me-2"></i>View Provider Profile
                                </a>
                                <a href="search.php" class="btn btn-outline-info">
                                    <i class="fa fa-search me-2"></i>Find More Providers
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Payment Confirmation End -->

        <!-- Footer Start -->
        <div class="container-fluid bg-dark text-light footer pt-5 mt-5 wow fadeIn" data-wow-delay="0.1s">
            <div class="container py-5">
                <div class="row g-5">
                    <div class="col-lg-3 col-md-6">
                        <h4 class="text-white mb-3"><i class="fa fa-baby me-2"></i>Nestlings</h4>
                        <p>Connecting families with trusted childcare providers across Sri Lanka. Safe, verified, and reliable daycare solutions for your little ones.</p>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <h4 class="text-white mb-3">Quick Links</h4>
                        <a class="btn btn-link" href="../about.html">About Us</a>
                        <a class="btn btn-link" href="../contact.html">Contact Us</a>
                        <a class="btn btn-link" href="../services.html">Our Services</a>
                        <a class="btn btn-link" href="search.php">Find DayCare</a>
                    </div>
                </div>
            </div>
        </div>
        <!-- Footer End -->

        <!-- Back to Top -->
        <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/counterup/counterup.min.js"></script>
    <script src="../lib/owlcarousel/owl.carousel.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>

    <style>
        .payment-card {
            background: #fff;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }

        .payment-option {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .payment-option:hover {
            border-color: #007bff;
            background: #e3f2fd;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.2);
        }

        .payment-option.selected {
            border-color: #007bff;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            box-shadow: 0 5px 20px rgba(0,123,255,0.3);
        }

        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }

        .amount-input {
            font-size: 1.2rem;
            font-weight: bold;
            text-align: center;
        }

        .card-input {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            transition: all 0.3s ease;
        }

        .card-input:focus {
            background: #fff;
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }
    </style>

    <script>
        function selectPaymentOption(type) {
            // Remove selected class from all options
            document.querySelectorAll('.payment-option').forEach(option => {
                option.classList.remove('selected');
                option.querySelector('.payment-radio').className = 'fa fa-circle-o payment-radio';
            });

            // Add selected class to clicked option
            event.currentTarget.classList.add('selected');
            event.currentTarget.querySelector('.payment-radio').className = 'fa fa-check-circle payment-radio text-primary';

            // Set radio button
            document.getElementById(type + '_payment').checked = true;

            // Update payment amount
            const remainingAmount = <?php echo $booking['remaining_amount']; ?>;
            const amountInput = document.getElementById('payment_amount');

            if (type === 'full') {
                amountInput.value = remainingAmount;
                amountInput.readOnly = true;
            } else {
                amountInput.value = Math.round(remainingAmount * 0.5); // Default to 50%
                amountInput.readOnly = false;
            }
        }

        // Hide spinner when page loads and initialize payment options
        window.addEventListener('load', function() {
            document.getElementById('spinner').classList.remove('show');

            // Default to full payment option
            selectPaymentOption('full');
        });

        // Card number formatting
        document.getElementById('card_number').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
            let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
            if (formattedValue.length > 19) formattedValue = formattedValue.substr(0, 19);
            e.target.value = formattedValue;
        });

        // Expiry date formatting
        document.getElementById('card_expiry').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length >= 2) {
                value = value.substring(0, 2) + '/' + value.substring(2, 4);
            }
            e.target.value = value;
        });

        // CVV validation
        document.getElementById('card_cvv').addEventListener('input', function(e) {
            e.target.value = e.target.value.replace(/\D/g, '');
        });

        // Form validation and loading state
        document.querySelector('form').addEventListener('submit', function(e) {
            const cardNumber = document.getElementById('card_number').value.replace(/\s/g, '');
            const cardExpiry = document.getElementById('card_expiry').value;
            const cardCvv = document.getElementById('card_cvv').value;

            if (cardNumber.length < 13 || cardNumber.length > 19) {
                alert('Please enter a valid card number');
                e.preventDefault();
                return;
            }

            if (!/^\d{2}\/\d{2}$/.test(cardExpiry)) {
                alert('Please enter a valid expiry date (MM/YY)');
                e.preventDefault();
                return;
            }

            if (cardCvv.length < 3 || cardCvv.length > 4) {
                alert('Please enter a valid CVV');
                e.preventDefault();
                return;
            }

            // Show loading state
            const submitBtn = e.target.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin me-2"></i>Processing Payment...';
            submitBtn.disabled = true;
        });
    </script>
</body>
</html>
