<?php
// Simple analytics page for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Check if user is logged in and is a daycare provider
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'provider') {
    header("Location: ../login.php");
    exit();
}

require_once '../config/database.php';

// Get provider information
$stmt = $pdo->prepare("SELECT * FROM providers WHERE provider_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$provider = $stmt->fetch();

if (!$provider) {
    session_destroy();
    header("Location: ../login.php");
    exit();
}

// Get basic analytics data
$analytics = [];

// Total bookings and revenue
try {
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_bookings,
            SUM(total_cost) as total_revenue,
            AVG(total_cost) as avg_booking_value
        FROM bookings 
        WHERE provider_id = ? AND status != 'cancelled'
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $analytics['overview'] = $stmt->fetch();
} catch(Exception $e) {
    $analytics['overview'] = ['total_bookings' => 0, 'total_revenue' => 0, 'avg_booking_value' => 0];
    echo "<!-- Error in overview query: " . $e->getMessage() . " -->";
}

// Monthly bookings for the last 6 months
try {
    $stmt = $pdo->prepare("
        SELECT 
            DATE_FORMAT(booking_date, '%Y-%m') as month,
            COUNT(*) as bookings,
            SUM(total_cost) as revenue
        FROM bookings 
        WHERE provider_id = ? AND status != 'cancelled' 
            AND booking_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
        GROUP BY DATE_FORMAT(booking_date, '%Y-%m')
        ORDER BY month DESC
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $analytics['monthly'] = $stmt->fetchAll();
} catch(Exception $e) {
    $analytics['monthly'] = [];
    echo "<!-- Error in monthly query: " . $e->getMessage() . " -->";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Simple Analytics - Nestlings</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
        }
        .navbar-brand {
            font-weight: bold;
            color: #667eea !important;
        }
    </style>
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fa fa-home me-2"></i>Nestlings - Analytics
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">Dashboard</a>
                <a class="nav-link" href="../logout.php">Logout</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2 class="mb-4">📊 Analytics Dashboard</h2>
                <p class="text-muted">Welcome, <?php echo htmlspecialchars($provider['business_name']); ?>!</p>
            </div>
        </div>

        <!-- Overview Statistics -->
        <div class="row">
            <div class="col-md-4">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fa fa-calendar-check fa-2x text-primary"></i>
                        </div>
                        <div>
                            <div class="stats-number"><?php echo $analytics['overview']['total_bookings'] ?? 0; ?></div>
                            <div class="text-muted">Total Bookings</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fa fa-money-bill-wave fa-2x text-success"></i>
                        </div>
                        <div>
                            <div class="stats-number">LKR <?php echo number_format($analytics['overview']['total_revenue'] ?? 0, 2); ?></div>
                            <div class="text-muted">Total Revenue</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fa fa-chart-line fa-2x text-info"></i>
                        </div>
                        <div>
                            <div class="stats-number">LKR <?php echo number_format($analytics['overview']['avg_booking_value'] ?? 0, 2); ?></div>
                            <div class="text-muted">Average Booking</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Monthly Data -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="stats-card">
                    <h4 class="mb-3">📈 Monthly Performance</h4>
                    <?php if (empty($analytics['monthly'])): ?>
                        <p class="text-muted">No monthly data available yet.</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Month</th>
                                        <th>Bookings</th>
                                        <th>Revenue</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($analytics['monthly'] as $month): ?>
                                        <tr>
                                            <td><?php echo date('F Y', strtotime($month['month'] . '-01')); ?></td>
                                            <td><?php echo $month['bookings']; ?></td>
                                            <td>LKR <?php echo number_format($month['revenue'], 2); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Debug Info -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="stats-card">
                    <h5>🔍 Debug Information</h5>
                    <p><strong>Provider ID:</strong> <?php echo $_SESSION['user_id']; ?></p>
                    <p><strong>Business Name:</strong> <?php echo htmlspecialchars($provider['business_name']); ?></p>
                    <p><strong>Monthly Data Count:</strong> <?php echo count($analytics['monthly']); ?> months</p>
                    <p><strong>Page Load Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
                </div>
            </div>
        </div>

        <!-- Navigation Links -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="stats-card">
                    <h5>🔗 Quick Links</h5>
                    <a href="dashboard.php" class="btn btn-primary me-2">Dashboard</a>
                    <a href="analytics.php" class="btn btn-warning me-2">Full Analytics</a>
                    <a href="bookings.php" class="btn btn-info me-2">Bookings</a>
                    <a href="profile.php" class="btn btn-secondary">Profile</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        console.log('Simple analytics page loaded successfully!');
        console.log('Monthly data:', <?php echo json_encode($analytics['monthly']); ?>);
    </script>
</body>
</html>
