<?php
session_start();

echo "<h2>🔍 Session Debug Tool</h2>";

echo "<h3>Current Session Data:</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h3>Session Status:</h3>";
echo "<p>Session ID: " . session_id() . "</p>";
echo "<p>Session Status: " . session_status() . "</p>";

if (isset($_SESSION['user_id'])) {
    echo "<p>✅ User ID: " . $_SESSION['user_id'] . "</p>";
} else {
    echo "<p>❌ User ID not set</p>";
}

if (isset($_SESSION['user_type'])) {
    echo "<p>✅ User Type: " . $_SESSION['user_type'] . "</p>";
} else {
    echo "<p>❌ User Type not set</p>";
}

echo "<h3>Quick Actions:</h3>";
echo "<p><a href='login.php'>Go to Login Page</a></p>";
echo "<p><a href='Daycare/dashboard.php'>Go to Daycare Dashboard</a></p>";
echo "<p><a href='Daycare/analytics.php'>Go to Analytics Page</a></p>";
?>
