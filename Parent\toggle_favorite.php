<?php
session_start();
header('Content-Type: application/json');

// Check if user is logged in and is a parent
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'parent') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

require_once '../config/database.php';

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);
$provider_id = $input['provider_id'] ?? null;

if (!$provider_id) {
    echo json_encode(['success' => false, 'message' => 'Provider ID required']);
    exit();
}

try {
    // Check if already favorited
    $stmt = $pdo->prepare("SELECT favorite_id FROM favorites WHERE parent_id = ? AND provider_id = ?");
    $stmt->execute([$_SESSION['user_id'], $provider_id]);
    $favorite = $stmt->fetch();

    if ($favorite) {
        // Remove from favorites
        $stmt = $pdo->prepare("DELETE FROM favorites WHERE favorite_id = ?");
        $stmt->execute([$favorite['favorite_id']]);
        echo json_encode(['success' => true, 'action' => 'removed']);
    } else {
        // Add to favorites
        $stmt = $pdo->prepare("INSERT INTO favorites (parent_id, provider_id) VALUES (?, ?)");
        $stmt->execute([$_SESSION['user_id'], $provider_id]);
        echo json_encode(['success' => true, 'action' => 'added']);
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Database error']);
}
?>
