<?php
session_start();

// Check if user is logged in and is a parent
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'parent') {
    header("Location: ../login.php");
    exit();
}

require_once '../config/database.php';

// Get parent information
$stmt = $pdo->prepare("SELECT * FROM parents WHERE parent_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$parent = $stmt->fetch();

// Search parameters
$district = $_GET['district'] ?? '';
$service_type = $_GET['service_type'] ?? '';
$search_query = $_GET['search'] ?? '';

// Build search query
$where_conditions = ["p.verification_status = 'verified'", "p.is_active = 1"];
$params = [];

if (!empty($district)) {
    $where_conditions[] = "p.district = ?";
    $params[] = $district;
}

if (!empty($service_type)) {
    // Map the display name to database value
    $db_service_type = $service_type_mapping[$service_type] ?? $service_type;
    $where_conditions[] = "p.service_types LIKE ?";
    $params[] = "%$db_service_type%";
}

if (!empty($search_query)) {
    $where_conditions[] = "(p.business_name LIKE ? OR p.description LIKE ?)";
    $params[] = "%$search_query%";
    $params[] = "%$search_query%";
}

$where_clause = implode(' AND ', $where_conditions);

// Get providers with ratings
$sql = "
    SELECT p.*, 
           AVG(r.rating) as avg_rating, 
           COUNT(r.review_id) as review_count,
           (SELECT COUNT(*) FROM favorites f WHERE f.provider_id = p.provider_id AND f.parent_id = ?) as is_favorite
    FROM providers p 
    LEFT JOIN reviews r ON p.provider_id = r.provider_id 
    WHERE $where_clause
    GROUP BY p.provider_id 
    ORDER BY avg_rating DESC, review_count DESC
";

$stmt = $pdo->prepare($sql);
$stmt->execute(array_merge([$_SESSION['user_id']], $params));
$providers = $stmt->fetchAll();

// Sri Lankan districts
$districts = [
    'Ampara', 'Anuradhapura', 'Badulla', 'Batticaloa', 'Colombo', 'Galle', 'Gampaha', 
    'Hambantota', 'Jaffna', 'Kalutara', 'Kandy', 'Kegalle', 'Kilinochchi', 'Kurunegala', 
    'Mannar', 'Matale', 'Matara', 'Monaragala', 'Mullaitivu', 'Nuwara Eliya', 'Polonnaruwa', 
    'Puttalam', 'Ratnapura', 'Trincomalee', 'Vavuniya'
];

$service_types = [
    'Full Day Care', 'Half Day Care', 'After School Care', 'Weekend Care', 'Holiday Care',
    'Infant Care (0-2 years)', 'Toddler Care (2-4 years)', 'Preschool (4-6 years)'
];

// Service type mapping for database search
$service_type_mapping = [
    'Full Day Care' => 'full_day',
    'Half Day Care' => 'half_day',
    'After School Care' => 'after_school',
    'Weekend Care' => 'weekend',
    'Holiday Care' => 'holiday',
    'Infant Care (0-2 years)' => 'infant',
    'Toddler Care (2-4 years)' => 'toddler',
    'Preschool (4-6 years)' => 'preschool'
];
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>Find DayCare - Nestlings</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="" name="keywords">
    <meta content="" name="description">

    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600&family=Nunito:wght@600;700;800&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">
    <link href="../lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        .search-filters {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .provider-card {
            transition: all 0.3s ease;
            border-radius: 15px;
            overflow: hidden;
            border: none;
        }
        
        .provider-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }
        
        .rating-stars {
            color: #ffc107;
        }
        
        .favorite-btn {
            border: none;
            background: none;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }
        
        .favorite-btn.active {
            color: #dc3545;
        }
        
        .favorite-btn:not(.active) {
            color: #6c757d;
        }
        
        .favorite-btn:hover {
            transform: scale(1.2);
        }
        
        .user-dropdown {
            border-radius: 25px;
            padding: 8px 20px;
            background: linear-gradient(135deg, var(--primary) 0%, #6BB6FF 100%);
            border: none;
            color: white;
            font-weight: 500;
        }
        
        .user-dropdown:hover {
            background: linear-gradient(135deg, #6BB6FF 0%, var(--primary) 100%);
            color: white;
        }
    </style>
</head>

<body>
    <!-- Spinner Start -->
    <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>
    <!-- Spinner End -->

    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light shadow sticky-top p-0">
        <a href="../index.html" class="navbar-brand d-flex align-items-center px-4 px-lg-5">
            <h2 class="m-0 text-primary"><i class="fa fa-baby me-3"></i>Nestlings</h2>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="dashboard.php" class="nav-item nav-link">Home</a>
                <a href="search.php" class="nav-item nav-link active">Find DayCare</a>
                <a href="bookings.php" class="nav-item nav-link">My Bookings</a>
                <a href="favorites.php" class="nav-item nav-link">Favorites</a>
                <a href="reviews.php" class="nav-item nav-link">Reviews</a>
            </div>
            <div class="dropdown">
                <button class="btn user-dropdown dropdown-toggle d-flex align-items-center" type="button" data-bs-toggle="dropdown">
                    <i class="fa fa-user-circle me-2"></i>
                    <span><?php echo htmlspecialchars($parent['name']); ?></span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="profile.php"><i class="fa fa-user me-2"></i>My Profile</a></li>
                    <li><a class="dropdown-item" href="settings.php"><i class="fa fa-cog me-2"></i>Settings</a></li>
                    <li><a class="dropdown-item" href="help.php"><i class="fa fa-question-circle me-2"></i>Help</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="../logout.php"><i class="fa fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Header Start -->
    <div class="container-fluid bg-primary py-5 mb-5 page-header">
        <div class="container py-5">
            <div class="row justify-content-center">
                <div class="col-lg-10 text-center">
                    <h1 class="display-3 text-white animated slideInDown">Find DayCare Providers</h1>
                    <p class="text-white">Discover trusted, verified childcare providers in your area</p>
                </div>
            </div>
        </div>
    </div>
    <!-- Header End -->

    <!-- Search Filters Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <div class="search-filters">
                <form method="GET" action="">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">Search</label>
                            <input type="text" class="form-control" name="search" placeholder="Provider name or keyword" value="<?php echo htmlspecialchars($search_query); ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">District</label>
                            <select class="form-select" name="district">
                                <option value="">All Districts</option>
                                <?php foreach ($districts as $dist): ?>
                                    <option value="<?php echo $dist; ?>" <?php echo $district == $dist ? 'selected' : ''; ?>>
                                        <?php echo $dist; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Service Type</label>
                            <select class="form-select" name="service_type">
                                <option value="">All Services</option>
                                <?php foreach ($service_types as $service): ?>
                                    <option value="<?php echo $service; ?>" <?php echo $service_type == $service ? 'selected' : ''; ?>>
                                        <?php echo $service; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa fa-search me-2"></i>Search
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Results Count -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h5 class="mb-0">Found <?php echo count($providers); ?> providers</h5>
                <div class="d-flex align-items-center">
                    <span class="me-2">Sort by:</span>
                    <select class="form-select" style="width: auto;">
                        <option>Highest Rated</option>
                        <option>Most Reviews</option>
                        <option>Newest</option>
                    </select>
                </div>
            </div>

            <!-- Providers Grid -->
            <div class="row g-4">
                <?php if (empty($providers)): ?>
                    <div class="col-12 text-center py-5">
                        <i class="fa fa-search fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">No providers found</h4>
                        <p class="text-muted">Try adjusting your search criteria</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($providers as $provider): ?>
                    <div class="col-lg-6 col-xl-4">
                        <div class="provider-card card shadow h-100">
                            <div class="card-body p-4">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                            <i class="fa fa-building text-white"></i>
                                        </div>
                                        <div>
                                            <h5 class="card-title mb-1"><?php echo htmlspecialchars($provider['business_name']); ?></h5>
                                            <small class="text-muted">
                                                <i class="fa fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($provider['district']); ?>
                                            </small>
                                        </div>
                                    </div>
                                    <button class="favorite-btn <?php echo $provider['is_favorite'] ? 'active' : ''; ?>" 
                                            onclick="toggleFavorite(<?php echo $provider['provider_id']; ?>, this)">
                                        <i class="fa fa-heart"></i>
                                    </button>
                                </div>
                                
                                <div class="d-flex align-items-center mb-3">
                                    <div class="rating-stars me-2">
                                        <?php 
                                        $rating = round($provider['avg_rating'] ?? 0);
                                        for ($i = 1; $i <= 5; $i++): 
                                        ?>
                                            <i class="fa fa-star<?php echo $i <= $rating ? '' : '-o'; ?>"></i>
                                        <?php endfor; ?>
                                    </div>
                                    <span class="text-muted">(<?php echo $provider['review_count']; ?> reviews)</span>
                                </div>
                                
                                <p class="card-text text-muted mb-3">
                                    <?php echo htmlspecialchars(substr($provider['description'] ?? 'Quality childcare services', 0, 100)); ?>...
                                </p>
                                
                                <div class="mb-3">
                                    <small class="text-muted">
                                        <i class="fa fa-users me-1"></i>Capacity: <?php echo $provider['capacity']; ?> children
                                    </small>
                                </div>
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <a href="provider_details.php?id=<?php echo $provider['provider_id']; ?>" class="btn btn-outline-primary btn-sm">
                                        View Details
                                    </a>
                                    <a href="book_provider.php?id=<?php echo $provider['provider_id']; ?>" class="btn btn-primary btn-sm">
                                        Book Now
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <!-- Search Results End -->

    <!-- Footer Start -->
    <div class="container-fluid bg-dark text-light footer pt-5 mt-5 wow fadeIn" data-wow-delay="0.1s">
        <div class="container py-5">
            <div class="row g-5">
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3"><i class="fa fa-baby me-2"></i>Nestlings</h4>
                    <p>Connecting families with trusted childcare providers across Sri Lanka. Safe, verified, and reliable daycare solutions for your little ones.</p>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Quick Links</h4>
                    <a class="btn btn-link" href="../about.html">About Us</a>
                    <a class="btn btn-link" href="../contact.html">Contact Us</a>
                    <a class="btn btn-link" href="../services.html">Our Services</a>
                    <a class="btn btn-link" href="search.php">Find DayCare</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Support</h4>
                    <a class="btn btn-link" href="help.php">Help Center</a>
                    <a class="btn btn-link" href="">Safety Guidelines</a>
                    <a class="btn btn-link" href="">Provider Resources</a>
                    <a class="btn btn-link" href="">Parent Guide</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Contact Info</h4>
                    <p class="mb-2"><i class="fa fa-map-marker-alt me-3"></i>123 Main Street, Colombo, Sri Lanka</p>
                    <p class="mb-2"><i class="fa fa-phone-alt me-3"></i>+94 11 234 5678</p>
                    <p class="mb-2"><i class="fa fa-envelope me-3"></i><EMAIL></p>
                </div>
            </div>
        </div>
    </div>
    <!-- Footer End -->

    <!-- Back to Top -->
    <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/owlcarousel/owl.carousel.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>
    
    <script>
        function toggleFavorite(providerId, button) {
            fetch('toggle_favorite.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    provider_id: providerId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    button.classList.toggle('active');
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        }
    </script>
</body>

</html>
