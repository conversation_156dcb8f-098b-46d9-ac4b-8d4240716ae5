<?php
session_start();
require_once 'config/database.php';

// Set a test user session if not already set
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1; // Use first parent
    $_SESSION['user_type'] = 'parent';
}

// Get parent information
$stmt = $pdo->prepare("SELECT * FROM parents WHERE parent_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$parent = $stmt->fetch();

if (!$parent) {
    echo "No parent found. Please check your database.";
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Notifications - Nestlings</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary: #4A90E2;
            --secondary: #F39C12;
            --success: #27AE60;
            --danger: #E74C3C;
            --warning: #F39C12;
            --info: #3498DB;
            --light: #ECF0F1;
            --dark: #2C3E50;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .test-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            margin: 50px auto;
            max-width: 800px;
        }
        
        .navbar-test {
            background: white;
            padding: 15px 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .user-dropdown {
            border-radius: 25px;
            padding: 8px 20px;
            background: linear-gradient(135deg, var(--primary) 0%, #6BB6FF 100%);
            border: none;
            color: white;
            font-weight: 500;
        }
        
        .user-dropdown:hover {
            background: linear-gradient(135deg, #6BB6FF 0%, var(--primary) 100%);
            color: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">
            <i class="fa fa-bell text-primary me-3"></i>
            Notification Dropdown Test
        </h1>
        
        <div class="navbar-test d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-0 text-primary">
                    <i class="fa fa-baby me-2"></i>Nestlings
                </h4>
            </div>
            
            <div class="d-flex align-items-center gap-3">
                <?php include 'components/notifications.php'; ?>
                
                <div class="dropdown">
                    <button class="btn user-dropdown dropdown-toggle d-flex align-items-center" type="button" data-bs-toggle="dropdown">
                        <i class="fa fa-user-circle me-2"></i>
                        <span><?php echo htmlspecialchars($parent['name']); ?></span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#"><i class="fa fa-user me-2"></i>My Profile</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fa fa-cog me-2"></i>Settings</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#"><i class="fa fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="alert alert-info">
            <h5><i class="fa fa-info-circle me-2"></i>Test Instructions</h5>
            <ul class="mb-0">
                <li>Click the notification bell icon to test the dropdown</li>
                <li>Check if the dropdown appears in the correct position</li>
                <li>Verify that the dropdown is fully visible and not cut off</li>
                <li>Test the user dropdown as well to ensure both work properly</li>
            </ul>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fa fa-check-circle text-success me-2"></i>Fixed Issues</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><i class="fa fa-check text-success me-2"></i>Reviews page loading issue</li>
                            <li><i class="fa fa-check text-success me-2"></i>Variable name mismatch ($reviews vs $bookings)</li>
                            <li><i class="fa fa-check text-success me-2"></i>Review comment field display</li>
                            <li><i class="fa fa-check text-success me-2"></i>Notification dropdown positioning</li>
                            <li><i class="fa fa-check text-success me-2"></i>Notification dropdown styling</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fa fa-link text-primary me-2"></i>Quick Links</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="Parent/reviews.php" class="btn btn-primary">
                                <i class="fa fa-star me-2"></i>View Reviews Page
                            </a>
                            <a href="Parent/dashboard.php" class="btn btn-success">
                                <i class="fa fa-home me-2"></i>Go to Dashboard
                            </a>
                            <a href="add_test_reviews.php" class="btn btn-info">
                                <i class="fa fa-plus me-2"></i>Add Test Reviews
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
