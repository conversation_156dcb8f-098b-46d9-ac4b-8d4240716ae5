<?php
require_once 'config/database.php';

echo "<h2>Add Test Reviews</h2>";

try {
    // First, let's check if we have any completed bookings
    $completed_bookings = $pdo->query("
        SELECT b.*, p.business_name, par.name as parent_name 
        FROM bookings b 
        JOIN providers p ON b.provider_id = p.provider_id 
        JOIN parents par ON b.parent_id = par.parent_id 
        WHERE b.status = 'completed' 
        LIMIT 5
    ")->fetchAll();
    
    if (empty($completed_bookings)) {
        echo "<p style='color: orange;'>No completed bookings found. Let's create some completed bookings first...</p>";
        
        // Update some existing bookings to completed status
        $update_stmt = $pdo->prepare("
            UPDATE bookings 
            SET status = 'completed' 
            WHERE booking_id IN (
                SELECT booking_id FROM (
                    SELECT booking_id FROM bookings 
                    WHERE status = 'confirmed' 
                    ORDER BY booking_date ASC 
                    LIMIT 3
                ) as temp
            )
        ");
        $update_stmt->execute();
        
        echo "<p style='color: green;'>✓ Updated " . $update_stmt->rowCount() . " bookings to completed status</p>";
        
        // Refresh the completed bookings list
        $completed_bookings = $pdo->query("
            SELECT b.*, p.business_name, par.name as parent_name 
            FROM bookings b 
            JOIN providers p ON b.provider_id = p.provider_id 
            JOIN parents par ON b.parent_id = par.parent_id 
            WHERE b.status = 'completed' 
            LIMIT 5
        ")->fetchAll();
    }
    
    echo "<h3>Completed Bookings Available for Review:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>Booking ID</th><th>Parent</th><th>Provider</th><th>Child</th><th>Date</th><th>Status</th>";
    echo "</tr>";
    
    foreach ($completed_bookings as $booking) {
        echo "<tr>";
        echo "<td>#{$booking['booking_id']}</td>";
        echo "<td>{$booking['parent_name']}</td>";
        echo "<td>{$booking['business_name']}</td>";
        echo "<td>{$booking['child_name']}</td>";
        echo "<td>" . date('M d, Y', strtotime($booking['booking_date'])) . "</td>";
        echo "<td><span style='color: green;'>" . ucfirst($booking['status']) . "</span></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Add some sample reviews
    echo "<h3>Adding Sample Reviews...</h3>";
    
    $sample_reviews = [
        [
            'rating' => 5,
            'comment' => 'Excellent service! My child loved the activities and the staff was very caring and professional. Highly recommended!'
        ],
        [
            'rating' => 4,
            'comment' => 'Great daycare with good facilities. The staff is friendly and my child felt comfortable. Will definitely book again.'
        ],
        [
            'rating' => 5,
            'comment' => 'Outstanding care for my little one. The environment is safe, clean, and engaging. Thank you for the wonderful service!'
        ]
    ];
    
    $review_count = 0;
    foreach ($completed_bookings as $index => $booking) {
        if ($index >= count($sample_reviews)) break;
        
        // Check if review already exists
        $existing_review = $pdo->prepare("SELECT review_id FROM reviews WHERE booking_id = ? AND parent_id = ?");
        $existing_review->execute([$booking['booking_id'], $booking['parent_id']]);
        
        if ($existing_review->rowCount() == 0) {
            $review_data = $sample_reviews[$index];
            
            $insert_review = $pdo->prepare("
                INSERT INTO reviews (booking_id, parent_id, provider_id, rating, comment) 
                VALUES (?, ?, ?, ?, ?)
            ");
            
            $insert_review->execute([
                $booking['booking_id'],
                $booking['parent_id'],
                $booking['provider_id'],
                $review_data['rating'],
                $review_data['comment']
            ]);
            
            echo "<p style='color: green;'>✓ Added review for booking #{$booking['booking_id']} - {$review_data['rating']} stars</p>";
            $review_count++;
            
            // Update provider's average rating
            $rating_update = $pdo->prepare("
                UPDATE providers 
                SET average_rating = (
                    SELECT AVG(rating) FROM reviews WHERE provider_id = ?
                ),
                total_reviews = (
                    SELECT COUNT(*) FROM reviews WHERE provider_id = ?
                )
                WHERE provider_id = ?
            ");
            $rating_update->execute([$booking['provider_id'], $booking['provider_id'], $booking['provider_id']]);
            
        } else {
            echo "<p style='color: blue;'>ℹ️ Review already exists for booking #{$booking['booking_id']}</p>";
        }
    }
    
    echo "<h3>Summary</h3>";
    echo "<p style='color: green; font-weight: bold;'>✅ Added $review_count new reviews successfully!</p>";
    
    // Show current reviews
    echo "<h3>Current Reviews in Database:</h3>";
    $all_reviews = $pdo->query("
        SELECT r.*, b.child_name, p.business_name, par.name as parent_name 
        FROM reviews r 
        JOIN bookings b ON r.booking_id = b.booking_id 
        JOIN providers p ON r.provider_id = p.provider_id 
        JOIN parents par ON r.parent_id = par.parent_id 
        ORDER BY r.created_at DESC 
        LIMIT 10
    ")->fetchAll();
    
    if (!empty($all_reviews)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>Review ID</th><th>Parent</th><th>Provider</th><th>Rating</th><th>Comment</th><th>Date</th>";
        echo "</tr>";
        
        foreach ($all_reviews as $review) {
            echo "<tr>";
            echo "<td>#{$review['review_id']}</td>";
            echo "<td>{$review['parent_name']}</td>";
            echo "<td>{$review['business_name']}</td>";
            echo "<td>";
            for ($i = 1; $i <= 5; $i++) {
                echo $i <= $review['rating'] ? '⭐' : '☆';
            }
            echo " ({$review['rating']}/5)</td>";
            echo "<td>" . substr($review['comment'], 0, 50) . "...</td>";
            echo "<td>" . date('M d, Y', strtotime($review['created_at'])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<p><a href='Parent/reviews.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Reviews Page</a></p>";
    echo "<p><a href='Parent/dashboard.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Dashboard</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
