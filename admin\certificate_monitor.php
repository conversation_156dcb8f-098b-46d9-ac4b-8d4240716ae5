<?php
// Certificate Monitoring System for Admins
// This script helps admins monitor certificate status and expiry dates

session_start();
require_once '../config/database.php';

// Check if user is admin
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'admin') {
    header("Location: ../login.php");
    exit();
}

// Get certificate statistics
try {
    // Total providers with certificates
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM providers WHERE mandatory_certificate_type IS NOT NULL");
    $total_with_certs = $stmt->fetch()['total'];
    
    // Expired certificates
    $stmt = $pdo->query("SELECT COUNT(*) as expired FROM providers WHERE mandatory_certificate_expiry < CURDATE()");
    $expired_certs = $stmt->fetch()['expired'];
    
    // Expiring soon (within 30 days)
    $stmt = $pdo->query("SELECT COUNT(*) as expiring FROM providers WHERE mandatory_certificate_expiry BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)");
    $expiring_soon = $stmt->fetch()['expiring'];
    
    // NCPA vs MOH breakdown
    $stmt = $pdo->query("SELECT mandatory_certificate_type, COUNT(*) as count FROM providers WHERE mandatory_certificate_type IS NOT NULL GROUP BY mandatory_certificate_type");
    $cert_breakdown = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    
} catch(PDOException $e) {
    $error = "Database error: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate Monitor - Nestlings Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .certificate-card {
            border-left: 4px solid #007bff;
            transition: transform 0.2s;
        }
        .certificate-card:hover {
            transform: translateY(-2px);
        }
        .expired { border-left-color: #dc3545; }
        .expiring { border-left-color: #ffc107; }
        .valid { border-left-color: #28a745; }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 bg-dark text-white p-3">
                <h5><i class="fas fa-shield-alt"></i> Admin Panel</h5>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link text-white" href="dashboard.php">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white active" href="certificate_monitor.php">
                            <i class="fas fa-certificate"></i> Certificates
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white" href="../logout.php">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-10 p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-certificate text-primary"></i> Certificate Monitoring</h2>
                    <button class="btn btn-primary" onclick="window.print()">
                        <i class="fas fa-print"></i> Print Report
                    </button>
                </div>
                
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo $total_with_certs; ?></h4>
                                        <p class="mb-0">Total Certificates</p>
                                    </div>
                                    <i class="fas fa-certificate fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo $expired_certs; ?></h4>
                                        <p class="mb-0">Expired</p>
                                    </div>
                                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo $expiring_soon; ?></h4>
                                        <p class="mb-0">Expiring Soon</p>
                                    </div>
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo isset($cert_breakdown['ncpa']) ? $cert_breakdown['ncpa'] : 0; ?> / <?php echo isset($cert_breakdown['moh']) ? $cert_breakdown['moh'] : 0; ?></h4>
                                        <p class="mb-0">NCPA / MOH</p>
                                    </div>
                                    <i class="fas fa-chart-pie fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Certificate Status Table -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> Certificate Status Overview</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Provider</th>
                                        <th>Certificate Type</th>
                                        <th>Certificate Number</th>
                                        <th>Expiry Date</th>
                                        <th>Status</th>
                                        <th>Days Until Expiry</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    try {
                                        $stmt = $pdo->query("
                                            SELECT 
                                                p.provider_id,
                                                p.business_name,
                                                p.email,
                                                p.mandatory_certificate_type,
                                                p.mandatory_certificate_number,
                                                p.mandatory_certificate_expiry,
                                                DATEDIFF(p.mandatory_certificate_expiry, CURDATE()) as days_until_expiry,
                                                CASE 
                                                    WHEN p.mandatory_certificate_expiry < CURDATE() THEN 'Expired'
                                                    WHEN p.mandatory_certificate_expiry < DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN 'Expiring Soon'
                                                    ELSE 'Valid'
                                                END as status
                                            FROM providers p
                                            WHERE p.mandatory_certificate_type IS NOT NULL
                                            ORDER BY p.mandatory_certificate_expiry ASC
                                        ");
                                        
                                        while ($row = $stmt->fetch()) {
                                            $status_class = '';
                                            $status_icon = '';
                                            
                                            switch ($row['status']) {
                                                case 'Expired':
                                                    $status_class = 'badge bg-danger';
                                                    $status_icon = 'fas fa-times-circle';
                                                    break;
                                                case 'Expiring Soon':
                                                    $status_class = 'badge bg-warning';
                                                    $status_icon = 'fas fa-exclamation-triangle';
                                                    break;
                                                case 'Valid':
                                                    $status_class = 'badge bg-success';
                                                    $status_icon = 'fas fa-check-circle';
                                                    break;
                                            }
                                            
                                            echo "<tr>";
                                            echo "<td><strong>" . htmlspecialchars($row['business_name']) . "</strong><br><small class='text-muted'>" . htmlspecialchars($row['email']) . "</small></td>";
                                            echo "<td><span class='badge bg-" . ($row['mandatory_certificate_type'] == 'ncpa' ? 'primary' : 'info') . "'>" . strtoupper($row['mandatory_certificate_type']) . "</span></td>";
                                            echo "<td>" . htmlspecialchars($row['mandatory_certificate_number']) . "</td>";
                                            echo "<td>" . date('M d, Y', strtotime($row['mandatory_certificate_expiry'])) . "</td>";
                                            echo "<td><span class='$status_class'><i class='$status_icon'></i> " . $row['status'] . "</span></td>";
                                            echo "<td>" . $row['days_until_expiry'] . " days</td>";
                                            echo "<td>";
                                            echo "<button class='btn btn-sm btn-outline-primary me-1' onclick='viewProvider(" . $row['provider_id'] . ")'><i class='fas fa-eye'></i></button>";
                                            if ($row['status'] !== 'Valid') {
                                                echo "<button class='btn btn-sm btn-outline-warning' onclick='sendReminder(" . $row['provider_id'] . ")'><i class='fas fa-bell'></i></button>";
                                            }
                                            echo "</td>";
                                            echo "</tr>";
                                        }
                                    } catch(PDOException $e) {
                                        echo "<tr><td colspan='7' class='text-center text-danger'>Error loading data: " . $e->getMessage() . "</td></tr>";
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Certificate Requirements Info -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h6><i class="fas fa-info-circle"></i> NCPA Certificate Requirements</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success"></i> National Child Protection Authority registration</li>
                                    <li><i class="fas fa-check text-success"></i> Background checks for all staff</li>
                                    <li><i class="fas fa-check text-success"></i> Child protection policy documentation</li>
                                    <li><i class="fas fa-check text-success"></i> Annual renewal required</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h6><i class="fas fa-info-circle"></i> MOH Certificate Requirements</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success"></i> Ministry of Health facility license</li>
                                    <li><i class="fas fa-check text-success"></i> Health and safety compliance</li>
                                    <li><i class="fas fa-check text-success"></i> Food handling certification (if applicable)</li>
                                    <li><i class="fas fa-check text-success"></i> Regular health inspections</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewProvider(providerId) {
            // Redirect to provider details page
            window.location.href = 'provider_details.php?id=' + providerId;
        }
        
        function sendReminder(providerId) {
            if (confirm('Send certificate expiry reminder to this provider?')) {
                // AJAX call to send reminder
                fetch('send_certificate_reminder.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({provider_id: providerId})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Reminder sent successfully!');
                    } else {
                        alert('Failed to send reminder: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('Error sending reminder: ' + error);
                });
            }
        }
    </script>
</body>
</html>
