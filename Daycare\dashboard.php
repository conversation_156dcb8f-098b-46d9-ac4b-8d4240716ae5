<?php
session_start();

// Check if user is logged in and is a provider
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'provider') {
    header("Location: ../login.php");
    exit();
}

require_once '../config/database.php';

// Get provider information
$stmt = $pdo->prepare("SELECT * FROM providers WHERE provider_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$provider = $stmt->fetch();

if (!$provider) {
    session_destroy();
    header("Location: ../login.php");
    exit();
}

// Get comprehensive dashboard statistics
$stats = [];

// Basic booking statistics
$stmt = $pdo->prepare("SELECT COUNT(*) as total FROM bookings WHERE provider_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$stats['total_bookings'] = $stmt->fetchColumn();

$stmt = $pdo->prepare("SELECT COUNT(*) as pending FROM bookings WHERE provider_id = ? AND status = 'pending'");
$stmt->execute([$_SESSION['user_id']]);
$stats['pending_bookings'] = $stmt->fetchColumn();

$stmt = $pdo->prepare("SELECT COUNT(*) as confirmed FROM bookings WHERE provider_id = ? AND status = 'confirmed'");
$stmt->execute([$_SESSION['user_id']]);
$stats['confirmed_bookings'] = $stmt->fetchColumn();

$stmt = $pdo->prepare("SELECT COUNT(*) as completed FROM bookings WHERE provider_id = ? AND status = 'completed'");
$stmt->execute([$_SESSION['user_id']]);
$stats['completed_bookings'] = $stmt->fetchColumn();

// Monthly statistics
$stmt = $pdo->prepare("SELECT COUNT(*) as monthly FROM bookings WHERE provider_id = ? AND MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())");
$stmt->execute([$_SESSION['user_id']]);
$stats['monthly_bookings'] = $stmt->fetchColumn();

// Revenue statistics
$stmt = $pdo->prepare("SELECT SUM(total_cost) as total_revenue FROM bookings WHERE provider_id = ? AND status IN ('confirmed', 'completed')");
$stmt->execute([$_SESSION['user_id']]);
$stats['total_revenue'] = $stmt->fetchColumn() ?? 0;

$stmt = $pdo->prepare("SELECT SUM(total_cost) as monthly_revenue FROM bookings WHERE provider_id = ? AND status IN ('confirmed', 'completed') AND MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())");
$stmt->execute([$_SESSION['user_id']]);
$stats['monthly_revenue'] = $stmt->fetchColumn() ?? 0;

// Rating and review statistics
$stmt = $pdo->prepare("SELECT AVG(rating) as avg_rating, COUNT(*) as review_count FROM reviews WHERE provider_id = ? AND is_active = 1");
$stmt->execute([$_SESSION['user_id']]);
$rating_data = $stmt->fetch();
$stats['avg_rating'] = round($rating_data['avg_rating'] ?? 0, 1);
$stats['review_count'] = $rating_data['review_count'];

// Capacity utilization
$current_capacity = $provider['capacity'];
$stmt = $pdo->prepare("SELECT COUNT(DISTINCT parent_id) as active_children FROM bookings WHERE provider_id = ? AND status = 'confirmed' AND booking_date >= CURDATE()");
$stmt->execute([$_SESSION['user_id']]);
$active_children = $stmt->fetchColumn();
$stats['capacity_utilization'] = $current_capacity > 0 ? round(($active_children / $current_capacity) * 100, 1) : 0;

// Certificate status
$cert_status = 'No Certificate';
$cert_expiry_days = null;
if ($provider['mandatory_certificate_expiry']) {
    $expiry_date = new DateTime($provider['mandatory_certificate_expiry']);
    $today = new DateTime();
    $cert_expiry_days = $today->diff($expiry_date)->days;
    $is_expired = $expiry_date < $today;

    if ($is_expired) {
        $cert_status = 'Expired';
    } elseif ($cert_expiry_days <= 30) {
        $cert_status = 'Expiring Soon';
    } else {
        $cert_status = 'Valid';
    }
}
$stats['certificate_status'] = $cert_status;
$stats['certificate_expiry_days'] = $cert_expiry_days;

// Weekly booking trend (last 7 days)
$weekly_bookings = [];
for ($i = 6; $i >= 0; $i--) {
    $date = date('Y-m-d', strtotime("-$i days"));
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM bookings WHERE provider_id = ? AND DATE(created_at) = ?");
    $stmt->execute([$_SESSION['user_id'], $date]);
    $weekly_bookings[] = [
        'date' => date('M j', strtotime($date)),
        'count' => $stmt->fetchColumn()
    ];
}

// Monthly revenue trend (last 6 months)
$monthly_revenue = [];
for ($i = 5; $i >= 0; $i--) {
    $month = date('Y-m', strtotime("-$i months"));
    $stmt = $pdo->prepare("SELECT SUM(total_cost) FROM bookings WHERE provider_id = ? AND status IN ('confirmed', 'completed') AND DATE_FORMAT(created_at, '%Y-%m') = ?");
    $stmt->execute([$_SESSION['user_id'], $month]);
    $monthly_revenue[] = [
        'month' => date('M Y', strtotime($month . '-01')),
        'revenue' => $stmt->fetchColumn() ?? 0
    ];
}

// Service type performance
$stmt = $pdo->prepare("
    SELECT
        service_type,
        COUNT(*) as booking_count,
        SUM(total_cost) as total_revenue
    FROM bookings
    WHERE provider_id = ?
    AND status IN ('confirmed', 'completed')
    GROUP BY service_type
    ORDER BY booking_count DESC
    LIMIT 5
");
$stmt->execute([$_SESSION['user_id']]);
$service_performance = $stmt->fetchAll();

// Recent bookings with enhanced details
$stmt = $pdo->prepare("
    SELECT b.*, p.name as parent_name, p.phone as parent_phone, p.email as parent_email
    FROM bookings b
    JOIN parents p ON b.parent_id = p.parent_id
    WHERE b.provider_id = ?
    ORDER BY b.created_at DESC
    LIMIT 5
");
$stmt->execute([$_SESSION['user_id']]);
$recent_bookings = $stmt->fetchAll();

// Recent reviews with enhanced details
$stmt = $pdo->prepare("
    SELECT r.*, p.name as parent_name, p.email as parent_email
    FROM reviews r
    LEFT JOIN parents p ON r.parent_id = p.parent_id
    WHERE r.provider_id = ? AND r.is_active = 1
    ORDER BY r.created_at DESC
    LIMIT 5
");
$stmt->execute([$_SESSION['user_id']]);
$recent_reviews = $stmt->fetchAll();

// Upcoming bookings (next 7 days)
$stmt = $pdo->prepare("
    SELECT b.*, p.name as parent_name, p.phone as parent_phone
    FROM bookings b
    JOIN parents p ON b.parent_id = p.parent_id
    WHERE b.provider_id = ? AND b.status = 'confirmed'
    AND b.booking_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
    ORDER BY b.booking_date ASC, b.start_time ASC
    LIMIT 10
");
$stmt->execute([$_SESSION['user_id']]);
$upcoming_bookings = $stmt->fetchAll();

// Rating distribution
$stmt = $pdo->prepare("
    SELECT rating, COUNT(*) as count
    FROM reviews
    WHERE provider_id = ? AND is_active = 1
    GROUP BY rating
    ORDER BY rating DESC
");
$stmt->execute([$_SESSION['user_id']]);
$rating_distribution = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

// Top parents by bookings
$stmt = $pdo->prepare("
    SELECT p.name, p.email, COUNT(*) as booking_count, SUM(b.total_cost) as total_spent
    FROM bookings b
    JOIN parents p ON b.parent_id = p.parent_id
    WHERE b.provider_id = ? AND b.status IN ('confirmed', 'completed')
    GROUP BY b.parent_id
    ORDER BY booking_count DESC
    LIMIT 5
");
$stmt->execute([$_SESSION['user_id']]);
$top_parents = $stmt->fetchAll();

// Document status
$stmt = $pdo->prepare("
    SELECT document_type, verification_status, COUNT(*) as count
    FROM provider_documents
    WHERE provider_id = ?
    GROUP BY document_type, verification_status
");
$stmt->execute([$_SESSION['user_id']]);
$document_status = $stmt->fetchAll();

// Recent activities (bookings, reviews, document uploads)
$stmt = $pdo->prepare("
    (SELECT 'booking' as type, booking_id as id, parent_id, status as activity_status, created_at, 'New booking received' as description
     FROM bookings WHERE provider_id = ?)
    UNION ALL
    (SELECT 'review' as type, review_id as id, parent_id, 'active' as activity_status, created_at, CONCAT('New ', rating, '-star review') as description
     FROM reviews WHERE provider_id = ? AND is_active = 1)
    UNION ALL
    (SELECT 'document' as type, document_id as id, NULL as parent_id, verification_status as activity_status, uploaded_at as created_at, CONCAT('Document uploaded: ', document_type) as description
     FROM provider_documents WHERE provider_id = ?)
    ORDER BY created_at DESC
    LIMIT 10
");
$stmt->execute([$_SESSION['user_id'], $_SESSION['user_id'], $_SESSION['user_id']]);
$recent_activities = $stmt->fetchAll();

// Performance metrics comparison (this month vs last month)
$current_month = date('Y-m');
$last_month = date('Y-m', strtotime('-1 month'));

$stmt = $pdo->prepare("
    SELECT
        SUM(CASE WHEN DATE_FORMAT(created_at, '%Y-%m') = ? THEN 1 ELSE 0 END) as current_month_bookings,
        SUM(CASE WHEN DATE_FORMAT(created_at, '%Y-%m') = ? THEN 1 ELSE 0 END) as last_month_bookings,
        SUM(CASE WHEN DATE_FORMAT(created_at, '%Y-%m') = ? AND status IN ('confirmed', 'completed') THEN total_cost ELSE 0 END) as current_month_revenue,
        SUM(CASE WHEN DATE_FORMAT(created_at, '%Y-%m') = ? AND status IN ('confirmed', 'completed') THEN total_cost ELSE 0 END) as last_month_revenue
    FROM bookings
    WHERE provider_id = ?
");
$stmt->execute([$current_month, $last_month, $current_month, $last_month, $_SESSION['user_id']]);
$performance_comparison = $stmt->fetch();

// Calculate percentage changes
$booking_change = 0;
$revenue_change = 0;

if ($performance_comparison['last_month_bookings'] > 0) {
    $booking_change = round((($performance_comparison['current_month_bookings'] - $performance_comparison['last_month_bookings']) / $performance_comparison['last_month_bookings']) * 100, 1);
}

if ($performance_comparison['last_month_revenue'] > 0) {
    $revenue_change = round((($performance_comparison['current_month_revenue'] - $performance_comparison['last_month_revenue']) / $performance_comparison['last_month_revenue']) * 100, 1);
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>Provider Dashboard - Nestlings</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="" name="keywords">
    <meta content="" name="description">

    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600&family=Nunito:wght@600;700;800&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">
    <link href="../lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- DataTables -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">

    <!-- Advanced Icons -->
    <link href="https://cdn.jsdelivr.net/npm/feather-icons@4.28.0/dist/feather.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Nunito', sans-serif;
        }

        .dashboard-header {
            background: var(--primary-gradient);
            color: white;
            padding: 50px 0;
            margin-bottom: 40px;
            position: relative;
            overflow: hidden;
        }

        .dashboard-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .stats-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.08);
            border: none;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            height: 100%;
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }

        .stats-card:hover::before {
            transform: scaleX(1);
        }

        .stats-icon {
            width: 70px;
            height: 70px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            color: white;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
        }

        .stats-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.3) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .stats-card:hover .stats-icon::before {
            opacity: 1;
        }

        .stats-number {
            font-size: 3rem;
            font-weight: 800;
            color: #2c3e50;
            margin-bottom: 8px;
            line-height: 1;
        }

        .stats-label {
            color: #7f8c8d;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.9rem;
            letter-spacing: 1px;
        }

        .stats-change {
            font-size: 0.85rem;
            font-weight: 600;
            margin-top: 8px;
        }

        .stats-change.positive {
            color: #27ae60;
        }

        .stats-change.negative {
            color: #e74c3c;
        }

        .content-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            border: none;
            margin-bottom: 30px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .content-card:hover {
            box-shadow: 0 15px 40px rgba(0,0,0,0.12);
        }

        .content-card .card-header {
            background: var(--primary-gradient);
            color: white;
            border-radius: 15px 15px 0 0;
            border: none;
            padding: 20px 25px;
        }
        
        .user-dropdown {
            border-radius: 25px;
            padding: 8px 20px;
            background: linear-gradient(135deg, var(--primary) 0%, #6BB6FF 100%);
            border: none;
            color: white;
            font-weight: 500;
        }
        
        .user-dropdown:hover {
            background: linear-gradient(135deg, #6BB6FF 0%, var(--primary) 100%);
            color: white;
        }
        
        .verification-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .verification-pending {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .verification-verified {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .verification-rejected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* Advanced Dashboard Styles */
        .metric-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border-left: 4px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--primary-gradient);
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }

        .metric-card:hover::before {
            transform: scaleY(1);
        }

        .metric-card:hover {
            transform: translateX(5px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.12);
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }

        .chart-container.small {
            height: 200px;
        }

        .activity-item {
            padding: 15px;
            border-radius: 12px;
            margin-bottom: 12px;
            background: #f8f9fa;
            border-left: 4px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .activity-item:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }

        .activity-item.booking {
            border-left-color: #007bff;
        }

        .activity-item.review {
            border-left-color: #28a745;
        }

        .activity-item.document {
            border-left-color: #ffc107;
        }

        .progress-ring {
            width: 120px;
            height: 120px;
            margin: 0 auto;
            position: relative;
            overflow: hidden;
        }

        .progress-ring circle {
            fill: transparent;
            stroke-width: 8;
            stroke-linecap: round;
            transform: rotate(-90deg);
            transform-origin: 50% 50%;
        }

        .progress-ring .background {
            stroke: #e9ecef;
        }

        .progress-ring .progress {
            stroke: url(#gradient);
            stroke-dasharray: 314;
            stroke-dashoffset: 314;
            animation: progress 2s ease-in-out forwards;
        }

        @keyframes progress {
            to {
                stroke-dashoffset: calc(314 - (314 * var(--progress)) / 100);
            }
        }

        .table-modern {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .table-modern thead th {
            background: var(--primary-gradient);
            color: white;
            border: none;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.85rem;
            letter-spacing: 0.5px;
        }

        .table-modern tbody tr {
            transition: all 0.3s ease;
        }

        .table-modern tbody tr:hover {
            background: #f8f9fa;
            transform: scale(1.01);
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-confirmed {
            background: #d1ecf1;
            color: #0c5460;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .status-cancelled {
            background: #f8d7da;
            color: #721c24;
        }

        .quick-action-btn {
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            border: none;
            position: relative;
            overflow: hidden;
            z-index: 10;
        }

        .quick-action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .quick-action-btn:hover::before {
            left: 100%;
        }

        .notification-dot {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 12px;
            height: 12px;
            background: #e74c3c;
            border-radius: 50%;
            border: 2px solid white;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.7;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* Activity Filters */
        .activity-filters .btn-group {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }

        .filter-btn {
            border: none;
            padding: 8px 16px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .filter-btn.active {
            background: var(--primary-gradient);
            color: white;
            box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
        }

        .filter-btn:hover:not(.active) {
            background: rgba(74, 144, 226, 0.1);
            color: #4A90E2;
        }

        /* Activity Items */
        .activity-item {
            padding: 16px 20px;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.3s ease;
            position: relative;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-item:hover {
            background: rgba(74, 144, 226, 0.05);
            transform: translateX(5px);
        }

        .activity-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: #e9ecef;
            transition: background 0.3s ease;
        }

        .activity-item.booking::before {
            background: #28a745;
        }

        .activity-item.review::before {
            background: #ffc107;
        }

        .activity-item.document::before {
            background: #17a2b8;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-confirmed {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }

        .status-pending {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
        }

        .status-active {
            background: rgba(23, 162, 184, 0.1);
            color: #17a2b8;
        }

        .status-verified {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }

        .status-pending_verification {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
        }
        
        .rating-stars {
            color: #ffc107;
        }
        
        .booking-item {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .booking-item:hover {
            background: #f8f9fa;
        }
        
        .booking-item:last-child {
            border-bottom: none;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-confirmed {
            background: #d4edda;
            color: #155724;
        }
        
        .status-completed {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .status-cancelled {
            background: #f8d7da;
            color: #721c24;
        }
        
        .quick-action-btn {
            border-radius: 10px;
            padding: 12px 20px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            margin: 5px;
        }
        
        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.15);
        }

        /* Stats Change Indicators */
        .stats-change {
            font-size: 0.75rem;
            margin-top: 8px;
            font-weight: 600;
        }

        .stats-change.positive {
            color: #28a745;
        }

        .stats-change.negative {
            color: #dc3545;
        }

        /* DataTables Styling */
        .dataTables_wrapper .dataTables_length,
        .dataTables_wrapper .dataTables_filter,
        .dataTables_wrapper .dataTables_info,
        .dataTables_wrapper .dataTables_paginate {
            margin-bottom: 1rem;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button {
            border-radius: 8px;
            margin: 0 2px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .stats-card {
                margin-bottom: 1rem;
            }

            .chart-container {
                height: 250px;
            }

            .quick-action-btn {
                width: 100%;
                margin-bottom: 0.5rem;
            }

            .dashboard-header h1 {
                font-size: 1.5rem;
            }

            .metric-card {
                padding: 16px;
            }
        }
    </style>
</head>

<body>
    <!-- Spinner Start -->
    <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>
    <!-- Spinner End -->

    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light shadow sticky-top p-0">
        <a href="../index.html" class="navbar-brand d-flex align-items-center px-4 px-lg-5">
            <h2 class="m-0 text-primary"><i class="fa fa-baby me-3"></i>Nestlings</h2>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="dashboard.php" class="nav-item nav-link active">Dashboard</a>
                <a href="bookings.php" class="nav-item nav-link">Bookings</a>
                <a href="profile.php" class="nav-item nav-link">Profile</a>
                <a href="pricing.php" class="nav-item nav-link">Pricing</a>
                <a href="analytics.php" class="nav-item nav-link">Analytics</a>
                <a href="management.php" class="nav-item nav-link">Management</a>
                <a href="payments.php" class="nav-item nav-link">Payments</a>
            </div>
            <div class="d-flex align-items-center gap-3">
                <?php include '../components/notifications.php'; ?>
                <div class="dropdown">
                    <button class="btn btn-primary dropdown-toggle me-4" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fa fa-user me-2"></i>
                        <span><?php echo htmlspecialchars($provider['business_name']); ?></span>
                    </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="profile.php"><i class="fa fa-building me-2"></i>Business Profile</a></li>
                    <li><a class="dropdown-item" href="settings.php"><i class="fa fa-cog me-2"></i>Settings</a></li>
                    <li><a class="dropdown-item" href="help.php"><i class="fa fa-question-circle me-2"></i>Help</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="../logout.php"><i class="fa fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
                </div>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Dashboard Header Start -->
    <div class="dashboard-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-5 mb-3">Welcome back, <?php echo htmlspecialchars($provider['contact_person']); ?>!</h1>
                    <p class="lead mb-0">Manage your daycare business and connect with families</p>
                </div>
                <div class="col-lg-4 text-lg-end">
                    <div class="verification-badge verification-<?php echo $provider['verification_status']; ?>">
                        <i class="fa fa-<?php echo $provider['verification_status'] === 'verified' ? 'check-circle' : ($provider['verification_status'] === 'rejected' ? 'times-circle' : 'clock'); ?> me-2"></i>
                        <?php echo ucfirst($provider['verification_status']); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Dashboard Header End -->

    <!-- Dashboard Content Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <!-- Quick Actions Bar -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="content-card">
                        <div class="card-body p-3">
                            <div class="d-flex flex-wrap gap-2">
                                <button class="quick-action-btn btn btn-primary" onclick="location.href='bookings.php'">
                                    <i class="fa fa-calendar-plus me-2"></i>New Booking
                                </button>
                                <button class="quick-action-btn btn btn-success" onclick="location.href='profile.php'">
                                    <i class="fa fa-edit me-2"></i>Update Profile
                                </button>
                                <button class="quick-action-btn btn btn-info" onclick="location.href='analytics.php'">
                                    <i class="fa fa-chart-bar me-2"></i>View Analytics
                                </button>
                                <button class="quick-action-btn btn btn-secondary" onclick="exportData()">
                                    <i class="fa fa-download me-2"></i>Export Data
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Statistics Cards -->
            <div class="row g-4 mb-5">
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon" style="background: var(--primary-gradient);">
                            <i class="fa fa-calendar-check"></i>
                        </div>
                        <div class="stats-number"><?php echo $stats['total_bookings']; ?></div>
                        <div class="stats-label">Total Bookings</div>
                        <div class="stats-change <?php echo $booking_change >= 0 ? 'positive' : 'negative'; ?>">
                            <i class="fa fa-arrow-<?php echo $booking_change >= 0 ? 'up' : 'down'; ?> me-1"></i>
                            <?php echo abs($booking_change); ?>% vs last month
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon" style="background: var(--warning-gradient);">
                            <i class="fa fa-clock"></i>
                            <?php if ($stats['pending_bookings'] > 0): ?>
                                <span class="notification-dot"></span>
                            <?php endif; ?>
                        </div>
                        <div class="stats-number"><?php echo $stats['pending_bookings']; ?></div>
                        <div class="stats-label">Pending Requests</div>
                        <div class="stats-change">
                            <i class="fa fa-exclamation-triangle me-1"></i>
                            Requires attention
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon" style="background: var(--success-gradient);">
                            <i class="fa fa-dollar-sign"></i>
                        </div>
                        <div class="stats-number">LKR <?php echo number_format($stats['monthly_revenue']); ?></div>
                        <div class="stats-label">Monthly Revenue</div>
                        <div class="stats-change <?php echo $revenue_change >= 0 ? 'positive' : 'negative'; ?>">
                            <i class="fa fa-arrow-<?php echo $revenue_change >= 0 ? 'up' : 'down'; ?> me-1"></i>
                            <?php echo abs($revenue_change); ?>% vs last month
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon" style="background: var(--info-gradient);">
                            <i class="fa fa-users"></i>
                        </div>
                        <div class="stats-number"><?php echo $stats['capacity_utilization']; ?>%</div>
                        <div class="stats-label">Capacity Used</div>
                        <div class="stats-change">
                            <i class="fa fa-info-circle me-1"></i>
                            <?php echo $active_children; ?>/<?php echo $current_capacity; ?> children
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Overview Row -->
            <div class="row g-4 mb-5">
                <!-- Revenue Chart -->
                <div class="col-lg-8">
                    <div class="content-card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fa fa-chart-line me-2"></i>Revenue Trend (Last 6 Months)</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="revenueChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Capacity Utilization -->
                <div class="col-lg-4">
                    <div class="content-card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fa fa-chart-pie me-2"></i>Capacity Overview</h5>
                        </div>
                        <div class="card-body text-center">
                            <div class="progress-ring position-relative" style="--progress: <?php echo $stats['capacity_utilization']; ?>">
                                <svg width="120" height="120">
                                    <defs>
                                        <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                            <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                                        </linearGradient>
                                    </defs>
                                    <circle class="background" cx="60" cy="60" r="50"></circle>
                                    <circle class="progress" cx="60" cy="60" r="50"></circle>
                                </svg>
                                <div class="position-absolute top-50 start-50 translate-middle" style="z-index: 1;">
                                    <h3 class="mb-0"><?php echo $stats['capacity_utilization']; ?>%</h3>
                                    <small class="text-muted">Utilized</small>
                                </div>
                            </div>
                            <div class="mt-3">
                                <div class="d-flex justify-content-between">
                                    <span>Active Children:</span>
                                    <strong><?php echo $active_children; ?></strong>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>Total Capacity:</span>
                                    <strong><?php echo $current_capacity; ?></strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analytics Row -->
            <div class="row g-4 mb-5">
                <!-- Booking Trend -->
                <div class="col-lg-6">
                    <div class="content-card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fa fa-calendar-alt me-2"></i>Weekly Booking Trend</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container small">
                                <canvas id="weeklyChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Rating Distribution -->
                <div class="col-lg-6">
                    <div class="content-card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fa fa-star me-2"></i>Rating Distribution</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container small">
                                <canvas id="ratingChart"></canvas>
                            </div>
                            <div class="text-center mt-3">
                                <h4 class="text-warning"><?php echo $stats['avg_rating']; ?> <i class="fa fa-star"></i></h4>
                                <p class="text-muted">Average Rating (<?php echo $stats['review_count']; ?> reviews)</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Certificate Status Alert -->
            <?php if ($stats['certificate_status'] !== 'Valid'): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <div class="alert alert-<?php echo $stats['certificate_status'] === 'Expired' ? 'danger' : 'warning'; ?> alert-dismissible fade show" role="alert">
                        <i class="fa fa-<?php echo $stats['certificate_status'] === 'Expired' ? 'exclamation-triangle' : 'clock'; ?> me-2"></i>
                        <strong>Certificate <?php echo $stats['certificate_status']; ?>!</strong>
                        <?php if ($stats['certificate_status'] === 'Expired'): ?>
                            Your <?php echo strtoupper($provider['mandatory_certificate_type']); ?> certificate has expired. Please renew immediately.
                        <?php else: ?>
                            Your <?php echo strtoupper($provider['mandatory_certificate_type']); ?> certificate expires in <?php echo $stats['certificate_expiry_days']; ?> days.
                        <?php endif; ?>
                        <a href="documents.php" class="btn btn-sm btn-outline-<?php echo $stats['certificate_status'] === 'Expired' ? 'danger' : 'warning'; ?> ms-2">
                            Update Certificate
                        </a>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            <!-- Main Dashboard Content -->
            <div class="row g-4">
                <!-- Recent Activities -->
                <div class="col-lg-8">
                    <div class="content-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fa fa-clock me-2"></i>Recent Activities</h5>
                            <div class="activity-filters">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-primary btn-sm filter-btn active" onclick="filterActivities('all', this)">
                                        <i class="fa fa-list me-1"></i>All
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm filter-btn" onclick="filterActivities('booking', this)">
                                        <i class="fa fa-calendar me-1"></i>Bookings
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm filter-btn" onclick="filterActivities('review', this)">
                                        <i class="fa fa-star me-1"></i>Reviews
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm filter-btn" onclick="filterActivities('document', this)">
                                        <i class="fa fa-file me-1"></i>Documents
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0" style="max-height: 400px; overflow-y: auto;">
                            <?php if (empty($recent_activities)): ?>
                                <div class="text-center py-5">
                                    <i class="fa fa-clock fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No recent activities</h5>
                                    <p class="text-muted">Activities will appear here as you manage your daycare</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($recent_activities as $activity): ?>
                                <div class="activity-item <?php echo $activity['type']; ?>">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($activity['description']); ?></h6>
                                            <small class="text-muted">
                                                <i class="fa fa-clock me-1"></i>
                                                <?php echo date('M d, Y g:i A', strtotime($activity['created_at'])); ?>
                                            </small>
                                        </div>
                                        <div class="text-end">
                                            <span class="status-badge status-<?php echo $activity['activity_status']; ?>">
                                                <?php echo ucfirst($activity['activity_status']); ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Upcoming Bookings & Quick Stats -->
                <div class="col-lg-4">
                    <!-- Upcoming Bookings -->
                    <div class="content-card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><i class="fa fa-calendar-check me-2"></i>Upcoming (7 Days)</h6>
                            <a href="bookings.php" class="btn btn-light btn-sm">View All</a>
                        </div>
                        <div class="card-body p-0" style="max-height: 200px; overflow-y: auto;">
                            <?php if (empty($upcoming_bookings)): ?>
                                <div class="text-center py-4">
                                    <i class="fa fa-calendar-times fa-2x text-muted mb-2"></i>
                                    <p class="text-muted mb-0">No upcoming bookings</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($upcoming_bookings as $booking): ?>
                                <div class="booking-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($booking['child_name']); ?></h6>
                                            <small class="text-muted">
                                                <i class="fa fa-calendar me-1"></i>
                                                <?php echo date('M d', strtotime($booking['booking_date'])); ?>
                                                <i class="fa fa-clock ms-2 me-1"></i>
                                                <?php echo date('g:i A', strtotime($booking['start_time'])); ?>
                                            </small>
                                        </div>
                                        <div class="text-end">
                                            <small class="text-primary fw-bold">
                                                LKR <?php echo number_format($booking['total_cost']); ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Top Parents -->
                    <div class="content-card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fa fa-users me-2"></i>Top Parents</h6>
                        </div>
                        <div class="card-body p-0">
                            <?php if (empty($top_parents)): ?>
                                <div class="text-center py-4">
                                    <i class="fa fa-users fa-2x text-muted mb-2"></i>
                                    <p class="text-muted mb-0">No parent data yet</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($top_parents as $parent): ?>
                                <div class="booking-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($parent['name']); ?></h6>
                                            <small class="text-muted">
                                                <i class="fa fa-calendar me-1"></i><?php echo $parent['booking_count']; ?> bookings
                                            </small>
                                        </div>
                                        <div class="text-end">
                                            <small class="text-success fw-bold">
                                                LKR <?php echo number_format($parent['total_spent']); ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <!-- Dashboard Content End -->

    <!-- Footer Start -->
    <div class="container-fluid bg-dark text-light footer pt-5 mt-5 wow fadeIn" data-wow-delay="0.1s">
        <div class="container py-5">
            <div class="row g-5">
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3"><i class="fa fa-baby me-2"></i>Nestlings</h4>
                    <p>Connecting families with trusted childcare providers across Sri Lanka. Safe, verified, and reliable daycare solutions for your little ones.</p>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Provider Resources</h4>
                    <a class="btn btn-link" href="help.php">Help Center</a>
                    <a class="btn btn-link" href="">Safety Guidelines</a>
                    <a class="btn btn-link" href="">Best Practices</a>
                    <a class="btn btn-link" href="">Training Materials</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Quick Links</h4>
                    <a class="btn btn-link" href="profile.php">My Profile</a>
                    <a class="btn btn-link" href="bookings.php">Manage Bookings</a>
                    <a class="btn btn-link" href="analytics.php">Analytics</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Contact Support</h4>
                    <p class="mb-2"><i class="fa fa-map-marker-alt me-3"></i>123 Main Street, Colombo, Sri Lanka</p>
                    <p class="mb-2"><i class="fa fa-phone-alt me-3"></i>+94 11 234 5678</p>
                    <p class="mb-2"><i class="fa fa-envelope me-3"></i><EMAIL></p>
                </div>
            </div>
        </div>
    </div>
    <!-- Footer End -->

    <!-- Back to Top -->
    <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/owlcarousel/owl.carousel.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>

    <!-- DataTables -->
    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <!-- Advanced Dashboard JavaScript -->
    <script>
        // Chart.js Configuration
        Chart.defaults.font.family = 'Nunito';
        Chart.defaults.color = '#858796';

        // Revenue Chart
        const revenueCtx = document.getElementById('revenueChart');
        if (revenueCtx) {
            new Chart(revenueCtx, {
                type: 'line',
                data: {
                    labels: <?php echo json_encode(array_column($monthly_revenue, 'month')); ?>,
                    datasets: [{
                        label: 'Revenue (LKR)',
                        data: <?php echo json_encode(array_column($monthly_revenue, 'revenue')); ?>,
                        borderColor: 'rgb(102, 126, 234)',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: 'rgb(102, 126, 234)',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 6,
                        pointHoverRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return 'LKR ' + value.toLocaleString();
                                }
                            },
                            grid: {
                                color: 'rgba(0,0,0,0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    elements: {
                        point: {
                            hoverBackgroundColor: 'rgb(102, 126, 234)'
                        }
                    }
                }
            });
        }

        // Weekly Booking Chart
        const weeklyCtx = document.getElementById('weeklyChart');
        if (weeklyCtx) {
            new Chart(weeklyCtx, {
                type: 'bar',
                data: {
                    labels: <?php echo json_encode(array_column($weekly_bookings, 'date')); ?>,
                    datasets: [{
                        label: 'Bookings',
                        data: <?php echo json_encode(array_column($weekly_bookings, 'count')); ?>,
                        backgroundColor: 'rgba(102, 126, 234, 0.8)',
                        borderColor: 'rgb(102, 126, 234)',
                        borderWidth: 1,
                        borderRadius: 8,
                        borderSkipped: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            },
                            grid: {
                                color: 'rgba(0,0,0,0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // Rating Distribution Chart
        const ratingCtx = document.getElementById('ratingChart');
        if (ratingCtx) {
            const ratingData = <?php echo json_encode($rating_distribution); ?>;
            const labels = ['5 Stars', '4 Stars', '3 Stars', '2 Stars', '1 Star'];
            const data = [
                ratingData[5] || 0,
                ratingData[4] || 0,
                ratingData[3] || 0,
                ratingData[2] || 0,
                ratingData[1] || 0
            ];

            new Chart(ratingCtx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: [
                            '#28a745',
                            '#17a2b8',
                            '#ffc107',
                            '#fd7e14',
                            '#dc3545'
                        ],
                        borderWidth: 0,
                        hoverOffset: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        }
                    }
                }
            });
        }

        // Activity Filter Function
        function filterActivities(type, button) {
            // Update active button
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            button.classList.add('active');

            // Filter activities
            const activities = document.querySelectorAll('.activity-item');
            let visibleCount = 0;

            activities.forEach(activity => {
                if (type === 'all' || activity.classList.contains(type)) {
                    activity.style.display = 'block';
                    activity.style.animation = 'fadeInUp 0.3s ease forwards';
                    visibleCount++;
                } else {
                    activity.style.display = 'none';
                }
            });

            // Show message if no activities found
            const container = document.querySelector('.activity-item').parentElement;
            let noResultsMsg = container.querySelector('.no-results-message');

            if (visibleCount === 0 && type !== 'all') {
                if (!noResultsMsg) {
                    noResultsMsg = document.createElement('div');
                    noResultsMsg.className = 'no-results-message text-center py-4';
                    noResultsMsg.innerHTML = `
                        <i class="fa fa-search fa-2x text-muted mb-2"></i>
                        <h6 class="text-muted">No ${type} activities found</h6>
                        <p class="text-muted small">Try selecting a different filter</p>
                    `;
                    container.appendChild(noResultsMsg);
                }
                noResultsMsg.style.display = 'block';
            } else if (noResultsMsg) {
                noResultsMsg.style.display = 'none';
            }
        }

        // Add fade in animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);

        // Export Data Function
        function exportData() {
            // Create CSV data
            const csvData = [
                ['Metric', 'Value'],
                ['Total Bookings', '<?php echo $stats['total_bookings']; ?>'],
                ['Pending Bookings', '<?php echo $stats['pending_bookings']; ?>'],
                ['Monthly Revenue', '<?php echo $stats['monthly_revenue']; ?>'],
                ['Average Rating', '<?php echo $stats['avg_rating']; ?>'],
                ['Capacity Utilization', '<?php echo $stats['capacity_utilization']; ?>%'],
                ['Certificate Status', '<?php echo $stats['certificate_status']; ?>']
            ];

            // Convert to CSV string
            const csvString = csvData.map(row => row.join(',')).join('\n');

            // Create download link
            const blob = new Blob([csvString], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'daycare_dashboard_data.csv';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        // Real-time updates (every 30 seconds)
        setInterval(function() {
            // Update pending bookings count
            fetch('get_pending_count.php')
                .then(response => response.json())
                .then(data => {
                    const pendingElement = document.querySelector('.stats-card .stats-number');
                    if (pendingElement && data.pending_count !== undefined) {
                        // Add animation if count changed
                        if (pendingElement.textContent !== data.pending_count.toString()) {
                            pendingElement.style.transform = 'scale(1.1)';
                            setTimeout(() => {
                                pendingElement.style.transform = 'scale(1)';
                            }, 200);
                        }
                        pendingElement.textContent = data.pending_count;
                    }
                })
                .catch(error => console.log('Update failed:', error));
        }, 30000);

        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Auto-refresh page every 5 minutes
        setTimeout(function() {
            location.reload();
        }, 300000);
    </script>
</body>

</html>
