<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Check if user is logged in and is a daycare provider
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'provider') {
    header("Location: ../login.php");
    exit();
}

require_once '../config/database.php';

// Get provider information
$stmt = $pdo->prepare("SELECT * FROM providers WHERE provider_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$provider = $stmt->fetch();

if (!$provider) {
    session_destroy();
    header("Location: ../login.php");
    exit();
}

// Get analytics data
$analytics = [];

// Total bookings and revenue
$stmt = $pdo->prepare("
    SELECT 
        COUNT(*) as total_bookings,
        SUM(total_cost) as total_revenue,
        AVG(total_cost) as avg_booking_value
    FROM bookings 
    WHERE provider_id = ? AND status != 'cancelled'
");
$stmt->execute([$_SESSION['user_id']]);
$analytics['overview'] = $stmt->fetch();

// Monthly bookings for the last 6 months
$stmt = $pdo->prepare("
    SELECT 
        DATE_FORMAT(booking_date, '%Y-%m') as month,
        COUNT(*) as bookings,
        SUM(total_cost) as revenue
    FROM bookings 
    WHERE provider_id = ? AND status != 'cancelled' 
        AND booking_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
    GROUP BY DATE_FORMAT(booking_date, '%Y-%m')
    ORDER BY month DESC
");
$stmt->execute([$_SESSION['user_id']]);
$analytics['monthly'] = $stmt->fetchAll();

// Service type popularity
$stmt = $pdo->prepare("
    SELECT 
        service_type,
        COUNT(*) as bookings,
        SUM(total_cost) as revenue
    FROM bookings 
    WHERE provider_id = ? AND status != 'cancelled'
    GROUP BY service_type
    ORDER BY bookings DESC
");
$stmt->execute([$_SESSION['user_id']]);
$analytics['services'] = $stmt->fetchAll();

// Reviews and ratings
$stmt = $pdo->prepare("
    SELECT 
        AVG(rating) as avg_rating,
        COUNT(*) as total_reviews,
        SUM(CASE WHEN rating = 5 THEN 1 ELSE 0 END) as five_star,
        SUM(CASE WHEN rating = 4 THEN 1 ELSE 0 END) as four_star,
        SUM(CASE WHEN rating = 3 THEN 1 ELSE 0 END) as three_star,
        SUM(CASE WHEN rating = 2 THEN 1 ELSE 0 END) as two_star,
        SUM(CASE WHEN rating = 1 THEN 1 ELSE 0 END) as one_star
    FROM reviews 
    WHERE provider_id = ? AND is_active = 1
");
$stmt->execute([$_SESSION['user_id']]);
$analytics['ratings'] = $stmt->fetch();

// Recent performance (last 30 days vs previous 30 days)
$stmt = $pdo->prepare("
    SELECT 
        SUM(CASE WHEN booking_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as recent_bookings,
        SUM(CASE WHEN booking_date >= DATE_SUB(CURDATE(), INTERVAL 60 DAY) AND booking_date < DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as previous_bookings,
        SUM(CASE WHEN booking_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN total_cost ELSE 0 END) as recent_revenue,
        SUM(CASE WHEN booking_date >= DATE_SUB(CURDATE(), INTERVAL 60 DAY) AND booking_date < DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN total_cost ELSE 0 END) as previous_revenue
    FROM bookings 
    WHERE provider_id = ? AND status != 'cancelled'
");
$stmt->execute([$_SESSION['user_id']]);
$analytics['performance'] = $stmt->fetch();

// Calculate percentage changes
$booking_change = 0;
$revenue_change = 0;
if ($analytics['performance']['previous_bookings'] > 0) {
    $booking_change = (($analytics['performance']['recent_bookings'] - $analytics['performance']['previous_bookings']) / $analytics['performance']['previous_bookings']) * 100;
}
if ($analytics['performance']['previous_revenue'] > 0) {
    $revenue_change = (($analytics['performance']['recent_revenue'] - $analytics['performance']['previous_revenue']) / $analytics['performance']['previous_revenue']) * 100;
}

// Top customers (parents with most bookings)
$stmt = $pdo->prepare("
    SELECT 
        p.name as parent_name,
        COUNT(b.booking_id) as total_bookings,
        SUM(b.total_cost) as total_spent,
        MAX(b.booking_date) as last_booking
    FROM bookings b
    JOIN parents p ON b.parent_id = p.parent_id
    WHERE b.provider_id = ? AND b.status != 'cancelled'
    GROUP BY b.parent_id, p.name
    ORDER BY total_bookings DESC
    LIMIT 5
");
$stmt->execute([$_SESSION['user_id']]);
$analytics['top_customers'] = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>Analytics Dashboard - Nestlings</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="" name="keywords">
    <meta content="" name="description">

    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600&family=Nunito:wght@600;700;800&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">
    <link href="../lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .analytics-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
            transition: all 0.3s ease;
            height: 100%;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-bottom: 15px;
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .stats-label {
            color: #7f8c8d;
            font-weight: 500;
            text-transform: uppercase;
            font-size: 0.85rem;
            letter-spacing: 0.5px;
        }
        
        .stats-change {
            font-size: 0.9rem;
            font-weight: 600;
            margin-top: 10px;
        }
        
        .change-positive {
            color: #27ae60;
        }
        
        .change-negative {
            color: #e74c3c;
        }
        
        .content-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            border: none;
            margin-bottom: 30px;
        }
        
        .content-card .card-header {
            background: linear-gradient(135deg, var(--primary) 0%, #6BB6FF 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            border: none;
            padding: 20px 25px;
        }
        
        .user-dropdown {
            border-radius: 25px;
            padding: 8px 20px;
            background: linear-gradient(135deg, var(--primary) 0%, #6BB6FF 100%);
            border: none;
            color: white;
            font-weight: 500;
        }
        
        .user-dropdown:hover {
            background: linear-gradient(135deg, #6BB6FF 0%, var(--primary) 100%);
            color: white;
        }
        
        .rating-bar {
            background: #e9ecef;
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            margin: 5px 0;
        }
        
        .rating-fill {
            background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
            height: 100%;
            transition: width 0.3s ease;
        }
        
        .service-item {
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        
        .service-item:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .customer-item {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .customer-item:hover {
            background: #f8f9fa;
        }
        
        .customer-item:last-child {
            border-bottom: none;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
    </style>
</head>

<body>
    <!-- Spinner Start -->
    <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>
    <!-- Spinner End -->

    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light shadow sticky-top p-0">
        <a href="../index.html" class="navbar-brand d-flex align-items-center px-4 px-lg-5">
            <h2 class="m-0 text-primary"><i class="fa fa-baby me-3"></i>Nestlings</h2>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="dashboard.php" class="nav-item nav-link">Dashboard</a>
                <a href="bookings.php" class="nav-item nav-link">Bookings</a>
                <a href="profile.php" class="nav-item nav-link">Profile</a>
                <a href="analytics.php" class="nav-item nav-link active">Analytics</a>
                <a href="management.php" class="nav-item nav-link">Management</a>
                <a href="payments.php" class="nav-item nav-link">Payments</a>
            </div>
            <div class="dropdown">
                <button class="btn btn-primary dropdown-toggle me-4" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fa fa-user me-2"></i>
                    <span><?php echo htmlspecialchars($provider['business_name']); ?></span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="profile.php"><i class="fa fa-building me-2"></i>Business Profile</a></li>
                    <li><a class="dropdown-item" href="settings.php"><i class="fa fa-cog me-2"></i>Settings</a></li>
                    <li><a class="dropdown-item" href="help.php"><i class="fa fa-question-circle me-2"></i>Help</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="../logout.php"><i class="fa fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Analytics Header Start -->
    <div class="analytics-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-5 mb-3">Analytics Dashboard</h1>
                    <p class="lead mb-0">Track your business performance and insights</p>
                </div>
                <div class="col-lg-4 text-lg-end">
                    <a href="dashboard.php" class="btn btn-light btn-lg">
                        <i class="fa fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- Analytics Header End -->

    <!-- Analytics Content Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <!-- Overview Statistics -->
            <div class="row g-4 mb-5">
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <i class="fa fa-calendar-check"></i>
                        </div>
                        <div class="stats-number"><?php echo $analytics['overview']['total_bookings'] ?? 0; ?></div>
                        <div class="stats-label">Total Bookings</div>
                        <div class="stats-change <?php echo $booking_change >= 0 ? 'change-positive' : 'change-negative'; ?>">
                            <i class="fa fa-arrow-<?php echo $booking_change >= 0 ? 'up' : 'down'; ?> me-1"></i>
                            <?php echo abs(round($booking_change, 1)); ?>% vs last month
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                            <i class="fa fa-money-bill-wave"></i>
                        </div>
                        <div class="stats-number">LKR <?php echo number_format($analytics['overview']['total_revenue'] ?? 0); ?></div>
                        <div class="stats-label">Total Revenue</div>
                        <div class="stats-change <?php echo $revenue_change >= 0 ? 'change-positive' : 'change-negative'; ?>">
                            <i class="fa fa-arrow-<?php echo $revenue_change >= 0 ? 'up' : 'down'; ?> me-1"></i>
                            <?php echo abs(round($revenue_change, 1)); ?>% vs last month
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                            <i class="fa fa-chart-line"></i>
                        </div>
                        <div class="stats-number">LKR <?php echo number_format($analytics['overview']['avg_booking_value'] ?? 0); ?></div>
                        <div class="stats-label">Avg Booking Value</div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                            <i class="fa fa-star"></i>
                        </div>
                        <div class="stats-number"><?php echo round($analytics['ratings']['avg_rating'] ?? 0, 1); ?></div>
                        <div class="stats-label">Average Rating</div>
                        <div class="stats-change">
                            <?php echo $analytics['ratings']['total_reviews'] ?? 0; ?> reviews
                        </div>
                    </div>
                </div>
            </div>

            <div class="row g-4">
                <!-- Monthly Performance Chart -->
                <div class="col-lg-8">
                    <div class="content-card card">
                        <div class="card-header">
                            <h4 class="mb-0"><i class="fa fa-chart-line me-2"></i>Monthly Performance</h4>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="monthlyChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Rating Distribution -->
                <div class="col-lg-4">
                    <div class="content-card card">
                        <div class="card-header">
                            <h4 class="mb-0"><i class="fa fa-star me-2"></i>Rating Distribution</h4>
                        </div>
                        <div class="card-body">
                            <?php if ($analytics['ratings']['total_reviews'] > 0): ?>
                                <?php for ($i = 5; $i >= 1; $i--): ?>
                                    <?php 
                                    $count = $analytics['ratings'][strtolower(number_to_words($i)) . '_star'] ?? 0;
                                    $percentage = ($count / $analytics['ratings']['total_reviews']) * 100;
                                    ?>
                                    <div class="d-flex align-items-center mb-3">
                                        <span class="me-2"><?php echo $i; ?> <i class="fa fa-star text-warning"></i></span>
                                        <div class="rating-bar flex-grow-1 me-2">
                                            <div class="rating-fill" style="width: <?php echo $percentage; ?>%"></div>
                                        </div>
                                        <span class="text-muted"><?php echo $count; ?></span>
                                    </div>
                                <?php endfor; ?>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fa fa-star fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">No reviews yet</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Service Performance -->
                <div class="col-lg-6">
                    <div class="content-card card">
                        <div class="card-header">
                            <h4 class="mb-0"><i class="fa fa-cogs me-2"></i>Service Performance</h4>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($analytics['services'])): ?>
                                <?php foreach ($analytics['services'] as $service): ?>
                                    <div class="service-item">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1"><?php echo htmlspecialchars($service['service_type']); ?></h6>
                                                <small class="text-muted"><?php echo $service['bookings']; ?> bookings</small>
                                            </div>
                                            <div class="text-end">
                                                <strong>LKR <?php echo number_format($service['revenue']); ?></strong>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fa fa-cogs fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">No service data available</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Top Customers -->
                <div class="col-lg-6">
                    <div class="content-card card">
                        <div class="card-header">
                            <h4 class="mb-0"><i class="fa fa-users me-2"></i>Top Customers</h4>
                        </div>
                        <div class="card-body p-0">
                            <?php if (!empty($analytics['top_customers'])): ?>
                                <?php foreach ($analytics['top_customers'] as $customer): ?>
                                    <div class="customer-item">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1"><?php echo htmlspecialchars($customer['parent_name']); ?></h6>
                                                <small class="text-muted">
                                                    <?php echo $customer['total_bookings']; ?> bookings • 
                                                    Last: <?php echo date('M j, Y', strtotime($customer['last_booking'])); ?>
                                                </small>
                                            </div>
                                            <div class="text-end">
                                                <strong>LKR <?php echo number_format($customer['total_spent']); ?></strong>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fa fa-users fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">No customer data available</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Analytics Content End -->

    <!-- Footer Start -->
    <div class="container-fluid bg-dark text-light footer pt-5 mt-5 wow fadeIn" data-wow-delay="0.1s">
        <div class="container py-5">
            <div class="row g-5">
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3"><i class="fa fa-baby me-2"></i>Nestlings</h4>
                    <p>Connecting families with trusted childcare providers across Sri Lanka. Safe, verified, and reliable daycare solutions for your little ones.</p>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Provider Resources</h4>
                    <a class="btn btn-link" href="help.php">Help Center</a>
                    <a class="btn btn-link" href="">Safety Guidelines</a>
                    <a class="btn btn-link" href="">Best Practices</a>
                    <a class="btn btn-link" href="">Training Materials</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Quick Links</h4>
                    <a class="btn btn-link" href="profile.php">My Profile</a>
                    <a class="btn btn-link" href="bookings.php">Manage Bookings</a>
                    <a class="btn btn-link" href="analytics.php">Analytics</a>
                    <a class="btn btn-link" href="documents.php">Documents</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Contact Support</h4>
                    <p class="mb-2"><i class="fa fa-map-marker-alt me-3"></i>123 Main Street, Colombo, Sri Lanka</p>
                    <p class="mb-2"><i class="fa fa-phone-alt me-3"></i>+94 11 234 5678</p>
                    <p class="mb-2"><i class="fa fa-envelope me-3"></i><EMAIL></p>
                </div>
            </div>
        </div>
    </div>
    <!-- Footer End -->

    <!-- Back to Top -->
    <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/owlcarousel/owl.carousel.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>

    <!-- Chart.js Script -->
    <script>
        // Hide spinner immediately and when page loads
        function hideSpinner() {
            const spinner = document.getElementById('spinner');
            if (spinner) {
                spinner.classList.remove('show');
                spinner.style.display = 'none';
                console.log('Spinner hidden');
            }
        }

        // Multiple methods to ensure spinner is hidden
        hideSpinner(); // Immediate

        document.addEventListener('DOMContentLoaded', hideSpinner);
        window.addEventListener('load', hideSpinner);

        // jQuery backup method
        if (typeof $ !== 'undefined') {
            $(document).ready(function() {
                hideSpinner();
            });
        }

        // Final fallback
        setTimeout(hideSpinner, 500);

        // Monthly Performance Chart
        const monthlyData = <?php echo json_encode(array_reverse($analytics['monthly'] ?? [])); ?>;
        console.log('Monthly data:', monthlyData); // Debug log

        const months = monthlyData.map(item => {
            const date = new Date(item.month + '-01');
            return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
        });
        const bookings = monthlyData.map(item => parseInt(item.bookings) || 0);
        const revenue = monthlyData.map(item => parseFloat(item.revenue) || 0);

        const chartElement = document.getElementById('monthlyChart');
        if (chartElement) {
            const ctx = chartElement.getContext('2d');
            new Chart(ctx, {
            type: 'line',
            data: {
                labels: months,
                datasets: [{
                    label: 'Bookings',
                    data: bookings,
                    borderColor: '#4A90E2',
                    backgroundColor: 'rgba(74, 144, 226, 0.1)',
                    tension: 0.4,
                    yAxisID: 'y'
                }, {
                    label: 'Revenue (LKR)',
                    data: revenue,
                    borderColor: '#E74C3C',
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    tension: 0.4,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Month'
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Bookings'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Revenue (LKR)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                    }
                }
            }
            });
        } else {
            console.error('Chart element not found');
        }

        // Helper function to convert numbers to words (for rating distribution)
        function number_to_words(num) {
            const words = ['zero', 'one', 'two', 'three', 'four', 'five'];
            return words[num] || num.toString();
        }
    </script>
</body>

</html>
