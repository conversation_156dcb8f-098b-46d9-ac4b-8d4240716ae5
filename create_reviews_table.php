<?php
require_once 'config/database.php';

try {
    // Check if reviews table exists
    $check_table = $pdo->query("SHOW TABLES LIKE 'reviews'");
    
    if ($check_table->rowCount() == 0) {
        // Create reviews table
        $create_reviews_table = "
        CREATE TABLE reviews (
            review_id INT AUTO_INCREMENT PRIMARY KEY,
            booking_id INT NOT NULL,
            parent_id INT NOT NULL,
            provider_id INT NOT NULL,
            rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
            comment TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (booking_id) REFERENCES bookings(booking_id) ON DELETE CASCADE,
            FOREIGN KEY (parent_id) REFERENCES parents(parent_id) ON DELETE CASCADE,
            FOREIGN KEY (provider_id) REFERENCES providers(provider_id) ON DELETE CASCADE,
            UNIQUE KEY unique_booking_review (booking_id, parent_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($create_reviews_table);
        echo "<div class='alert alert-success'>✅ Reviews table created successfully!</div>";
    } else {
        echo "<div class='alert alert-info'>ℹ️ Reviews table already exists.</div>";
    }
    
    // Check if reviews table has all required columns
    $columns_check = $pdo->query("SHOW COLUMNS FROM reviews");
    $existing_columns = [];
    while ($column = $columns_check->fetch()) {
        $existing_columns[] = $column['Field'];
    }
    
    $required_columns = ['review_id', 'booking_id', 'parent_id', 'provider_id', 'rating', 'comment', 'created_at', 'updated_at'];
    $missing_columns = array_diff($required_columns, $existing_columns);
    
    if (!empty($missing_columns)) {
        echo "<div class='alert alert-warning'>⚠️ Missing columns: " . implode(', ', $missing_columns) . "</div>";
        
        // Add missing columns
        foreach ($missing_columns as $column) {
            switch ($column) {
                case 'updated_at':
                    $pdo->exec("ALTER TABLE reviews ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
                    echo "<div class='alert alert-success'>✅ Added updated_at column</div>";
                    break;
                case 'comment':
                    $pdo->exec("ALTER TABLE reviews ADD COLUMN comment TEXT");
                    echo "<div class='alert alert-success'>✅ Added comment column</div>";
                    break;
            }
        }
    } else {
        echo "<div class='alert alert-success'>✅ All required columns exist in reviews table.</div>";
    }
    
    echo "<div class='alert alert-success'><strong>Reviews system is ready!</strong></div>";
    echo "<div class='alert alert-info'>
        <h5>Next Steps:</h5>
        <ul>
            <li>Parents can now review daycare providers who confirmed their bookings</li>
            <li>Reviews will appear on provider profile pages</li>
            <li>Only confirmed bookings can be reviewed</li>
            <li>Parents can edit their existing reviews</li>
        </ul>
    </div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ Error: " . $e->getMessage() . "</div>";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Reviews Table - Nestlings</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title mb-0">
                            <i class="fa fa-star me-2"></i>Reviews Table Migration
                        </h3>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">This script creates the reviews table and ensures all required columns exist.</p>
                        
                        <div class="mt-4">
                            <a href="Parent/reviews.php" class="btn btn-primary">
                                <i class="fa fa-star me-2"></i>Go to Reviews Page
                            </a>
                            <a href="Parent/dashboard.php" class="btn btn-outline-primary">
                                <i class="fa fa-home me-2"></i>Parent Dashboard
                            </a>
                            <a href="daycare/dashboard.php" class="btn btn-outline-secondary">
                                <i class="fa fa-building me-2"></i>Provider Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
