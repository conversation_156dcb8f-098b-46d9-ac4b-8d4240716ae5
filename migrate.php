<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Migration - Nestlings</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .migration-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="mb-0">Database Migration</h3>
                        <small class="text-muted">Adding contact numbers and pricing system</small>
                    </div>
                    <div class="card-body">
                        <?php
                        if (isset($_POST['run_migration'])) {
                            echo '<div class="migration-log">';
                            
                            try {
                                require_once 'config/database.php';
                                
                                echo "<span class='info'>🚀 Starting database migrations...</span>\n\n";
                                
                                // Migration 1: Add contact numbers to bookings
                                echo "<span class='info'>1. Adding contact number fields to bookings table...</span>\n";
                                try {
                                    $pdo->exec("ALTER TABLE bookings 
                                                ADD COLUMN contact_number_1 VARCHAR(20) NULL AFTER special_requirements,
                                                ADD COLUMN contact_number_2 VARCHAR(20) NULL AFTER contact_number_1");
                                    echo "<span class='success'>✓ Contact number columns added</span>\n";
                                } catch (Exception $e) {
                                    if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                                        echo "<span class='success'>✓ Contact number fields already exist</span>\n";
                                    } else {
                                        echo "<span class='error'>✗ Error: " . $e->getMessage() . "</span>\n";
                                    }
                                }
                                
                                // Update existing bookings
                                try {
                                    $result = $pdo->exec("UPDATE bookings b
                                                        JOIN parents p ON b.parent_id = p.parent_id
                                                        SET b.contact_number_1 = p.phone
                                                        WHERE b.contact_number_1 IS NULL");
                                    echo "<span class='success'>✓ Updated {$result} existing bookings with contact numbers</span>\n";
                                } catch (Exception $e) {
                                    echo "<span class='error'>✗ Error updating existing bookings: " . $e->getMessage() . "</span>\n";
                                }
                                
                                // Add index
                                try {
                                    $pdo->exec("CREATE INDEX idx_bookings_contact ON bookings(contact_number_1)");
                                    echo "<span class='success'>✓ Contact number index created</span>\n";
                                } catch (Exception $e) {
                                    if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                                        echo "<span class='success'>✓ Contact number index already exists</span>\n";
                                    } else {
                                        echo "<span class='error'>✗ Index error: " . $e->getMessage() . "</span>\n";
                                    }
                                }
                                
                                echo "\n";
                                
                                // Migration 2: Create pricing system
                                echo "<span class='info'>2. Creating provider pricing system...</span>\n";
                                try {
                                    $pdo->exec("CREATE TABLE IF NOT EXISTS provider_pricing (
                                        pricing_id INT AUTO_INCREMENT PRIMARY KEY,
                                        provider_id INT NOT NULL,
                                        service_type VARCHAR(100) NOT NULL,
                                        hourly_rate DECIMAL(10,2) NOT NULL DEFAULT 0.00,
                                        daily_rate DECIMAL(10,2) NULL,
                                        weekly_rate DECIMAL(10,2) NULL,
                                        monthly_rate DECIMAL(10,2) NULL,
                                        minimum_hours INT DEFAULT 1,
                                        maximum_hours INT DEFAULT 24,
                                        description TEXT,
                                        is_active BOOLEAN DEFAULT TRUE,
                                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                        FOREIGN KEY (provider_id) REFERENCES providers(provider_id) ON DELETE CASCADE,
                                        UNIQUE KEY unique_provider_service (provider_id, service_type)
                                    )");
                                    echo "<span class='success'>✓ Provider pricing table created</span>\n";
                                } catch (Exception $e) {
                                    echo "<span class='error'>✗ Error creating pricing table: " . $e->getMessage() . "</span>\n";
                                }
                                
                                // Check if we need to insert default pricing
                                $count_stmt = $pdo->query("SELECT COUNT(*) FROM provider_pricing");
                                $count = $count_stmt->fetchColumn();
                                
                                if ($count == 0) {
                                    echo "<span class='info'>3. Inserting default pricing for existing providers...</span>\n";
                                    
                                    // Get all providers
                                    $providers_stmt = $pdo->query("SELECT provider_id FROM providers");
                                    $providers = $providers_stmt->fetchAll();
                                    
                                    $service_types = [
                                        ['type' => 'full_day', 'hourly' => 500, 'daily' => 3500, 'desc' => 'Full day care service including meals, activities, and supervision'],
                                        ['type' => 'half_day', 'hourly' => 600, 'daily' => 2500, 'desc' => 'Half day care service (up to 6 hours) including snacks and activities'],
                                        ['type' => 'after_school', 'hourly' => 400, 'daily' => 1500, 'desc' => 'After school care service including homework assistance and snacks'],
                                        ['type' => 'weekend', 'hourly' => 700, 'daily' => 4000, 'desc' => 'Weekend care service with special activities and outings'],
                                        ['type' => 'overnight', 'hourly' => 800, 'daily' => 5000, 'desc' => 'Overnight care service including dinner, breakfast, and supervision']
                                    ];
                                    
                                    $insert_stmt = $pdo->prepare("
                                        INSERT INTO provider_pricing (provider_id, service_type, hourly_rate, daily_rate, description)
                                        VALUES (?, ?, ?, ?, ?)
                                    ");
                                    
                                    $total_inserted = 0;
                                    foreach ($providers as $provider) {
                                        foreach ($service_types as $service) {
                                            try {
                                                $insert_stmt->execute([
                                                    $provider['provider_id'],
                                                    $service['type'],
                                                    $service['hourly'],
                                                    $service['daily'],
                                                    $service['desc']
                                                ]);
                                                $total_inserted++;
                                            } catch (Exception $e) {
                                                echo "<span class='error'>✗ Error inserting pricing for provider {$provider['provider_id']}: " . $e->getMessage() . "</span>\n";
                                            }
                                        }
                                    }
                                    
                                    echo "<span class='success'>✓ Inserted {$total_inserted} pricing records for " . count($providers) . " providers</span>\n";
                                } else {
                                    echo "<span class='success'>✓ Pricing data already exists ({$count} records)</span>\n";
                                }
                                
                                // Add indexes
                                try {
                                    $pdo->exec("CREATE INDEX IF NOT EXISTS idx_provider_pricing_provider ON provider_pricing(provider_id)");
                                    $pdo->exec("CREATE INDEX IF NOT EXISTS idx_provider_pricing_service ON provider_pricing(service_type)");
                                    $pdo->exec("CREATE INDEX IF NOT EXISTS idx_provider_pricing_active ON provider_pricing(is_active)");
                                    echo "<span class='success'>✓ Pricing table indexes created</span>\n";
                                } catch (Exception $e) {
                                    echo "<span class='error'>✗ Index error: " . $e->getMessage() . "</span>\n";
                                }
                                
                                echo "\n<span class='success'>🎉 Migration completed successfully!</span>\n";
                                echo "\n<span class='info'>Summary:</span>\n";
                                echo "- Contact number fields added to bookings table\n";
                                echo "- Provider pricing system created\n";
                                echo "- Default pricing data inserted\n";
                                echo "- Database indexes optimized\n";

                                // 3. Create payments table and add payment tracking
                                echo "<h4>3. Setting up Payment System...</h4>\n";

                                // Create payments table
                                try {
                                    $pdo->exec("CREATE TABLE IF NOT EXISTS payments (
                                        payment_id INT AUTO_INCREMENT PRIMARY KEY,
                                        booking_id INT NOT NULL,
                                        parent_id INT NOT NULL,
                                        provider_id INT NOT NULL,
                                        amount DECIMAL(10,2) NOT NULL,
                                        payment_type ENUM('partial', 'full') NOT NULL DEFAULT 'full',
                                        payment_method VARCHAR(50) DEFAULT 'online',
                                        payment_status ENUM('pending', 'completed', 'failed', 'refunded') NOT NULL DEFAULT 'pending',
                                        transaction_id VARCHAR(100) NULL,
                                        card_last_four VARCHAR(4) NULL,
                                        card_holder_name VARCHAR(100) NULL,
                                        payment_date TIMESTAMP NULL,
                                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                        FOREIGN KEY (booking_id) REFERENCES bookings(booking_id) ON DELETE CASCADE,
                                        FOREIGN KEY (parent_id) REFERENCES parents(parent_id) ON DELETE CASCADE,
                                        FOREIGN KEY (provider_id) REFERENCES providers(provider_id) ON DELETE CASCADE,
                                        INDEX idx_payments_booking (booking_id),
                                        INDEX idx_payments_parent (parent_id),
                                        INDEX idx_payments_provider (provider_id),
                                        INDEX idx_payments_status (payment_status)
                                    )");
                                    echo "<span class='success'>✓ Payments table created</span>\n";
                                } catch (Exception $e) {
                                    if (strpos($e->getMessage(), 'already exists') !== false) {
                                        echo "<span class='success'>✓ Payments table already exists</span>\n";
                                    } else {
                                        echo "<span class='error'>✗ Error creating payments table: " . $e->getMessage() . "</span>\n";
                                    }
                                }

                                // Add payment tracking columns to bookings one by one
                                try {
                                    $pdo->exec("ALTER TABLE bookings ADD COLUMN payment_status ENUM('unpaid', 'partial', 'paid') DEFAULT 'unpaid' AFTER total_cost");
                                    echo "<span class='success'>✓ Payment status column added to bookings</span>\n";
                                } catch (Exception $e) {
                                    if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                                        echo "<span class='success'>✓ Payment status column already exists</span>\n";
                                    } else {
                                        echo "<span class='error'>✗ Error adding payment_status: " . $e->getMessage() . "</span>\n";
                                    }
                                }

                                try {
                                    $pdo->exec("ALTER TABLE bookings ADD COLUMN paid_amount DECIMAL(10,2) DEFAULT 0.00 AFTER payment_status");
                                    echo "<span class='success'>✓ Paid amount column added to bookings</span>\n";
                                } catch (Exception $e) {
                                    if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                                        echo "<span class='success'>✓ Paid amount column already exists</span>\n";
                                    } else {
                                        echo "<span class='error'>✗ Error adding paid_amount: " . $e->getMessage() . "</span>\n";
                                    }
                                }

                                try {
                                    $pdo->exec("ALTER TABLE bookings ADD COLUMN remaining_amount DECIMAL(10,2) DEFAULT 0.00 AFTER paid_amount");
                                    echo "<span class='success'>✓ Remaining amount column added to bookings</span>\n";
                                } catch (Exception $e) {
                                    if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                                        echo "<span class='success'>✓ Remaining amount column already exists</span>\n";
                                    } else {
                                        echo "<span class='error'>✗ Error adding remaining_amount: " . $e->getMessage() . "</span>\n";
                                    }
                                }

                                // Update existing bookings - only if columns exist
                                try {
                                    // Check if remaining_amount column exists before updating
                                    $check_column = $pdo->query("SHOW COLUMNS FROM bookings LIKE 'remaining_amount'");
                                    if ($check_column->rowCount() > 0) {
                                        $result = $pdo->exec("UPDATE bookings SET remaining_amount = total_cost WHERE (remaining_amount IS NULL OR remaining_amount = 0) AND total_cost > 0");
                                        echo "<span class='success'>✓ Updated {$result} existing bookings with remaining amounts</span>\n";
                                    } else {
                                        echo "<span class='error'>✗ Remaining amount column not found, skipping update</span>\n";
                                    }
                                } catch (Exception $e) {
                                    echo "<span class='error'>✗ Error updating existing bookings: " . $e->getMessage() . "</span>\n";
                                }

                                // Add payment status index
                                try {
                                    $pdo->exec("CREATE INDEX idx_bookings_payment_status ON bookings(payment_status)");
                                    echo "<span class='success'>✓ Payment status index created</span>\n";
                                } catch (Exception $e) {
                                    if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                                        echo "<span class='success'>✓ Payment status index already exists</span>\n";
                                    } else {
                                        echo "<span class='error'>✗ Index error: " . $e->getMessage() . "</span>\n";
                                    }
                                }

                                echo "\n<span class='info'>You can now:</span>\n";
                                echo "- Providers can manage pricing at /Daycare/pricing.php\n";
                                echo "- Parents will see pricing when booking services\n";
                                echo "- Contact numbers are collected during booking\n";
                                echo "- Total costs are calculated automatically\n";
                                echo "- Parents can make partial or full payments\n";
                                echo "- Payment status is tracked for all bookings\n";
                                
                            } catch (Exception $e) {
                                echo "<span class='error'>✗ Database connection error: " . $e->getMessage() . "</span>\n";
                                echo "<span class='info'>Please check:</span>\n";
                                echo "- XAMPP MySQL service is running\n";
                                echo "- Database 'nestlings_db' exists\n";
                                echo "- Database credentials in config/database.php are correct\n";
                            }
                            
                            echo '</div>';
                            echo '<div class="mt-3">';
                            echo '<a href="Parent/dashboard.php" class="btn btn-primary me-2">Go to Parent Dashboard</a>';
                            echo '<a href="Daycare/dashboard.php" class="btn btn-success me-2">Go to Provider Dashboard</a>';
                            echo '<a href="Daycare/pricing.php" class="btn btn-info">Manage Pricing</a>';
                            echo '</div>';
                        } else {
                        ?>
                            <div class="alert alert-info">
                                <h5>🔧 Database Migration Required</h5>
                                <p>This migration will add the following features:</p>
                                <ul>
                                    <li><strong>Contact Numbers:</strong> Add two contact number fields to booking forms</li>
                                    <li><strong>Pricing System:</strong> Allow providers to set custom pricing for their services</li>
                                    <li><strong>Cost Calculation:</strong> Automatically calculate booking costs based on duration and service type</li>
                                    <li><strong>Payment System:</strong> Add payment tracking and partial/full payment options for bookings</li>
                                </ul>
                                <p class="mb-0"><strong>Note:</strong> Make sure XAMPP MySQL service is running before proceeding.</p>
                            </div>
                            
                            <form method="POST">
                                <button type="submit" name="run_migration" class="btn btn-primary btn-lg">
                                    🚀 Run Migration
                                </button>
                            </form>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
