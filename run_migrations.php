<?php
// <PERSON><PERSON><PERSON> to run database migrations
require_once 'config/database.php';

echo "Running database migrations...\n\n";

// Migration 1: Add contact numbers to bookings
echo "1. Adding contact number fields to bookings table...\n";
try {
    $pdo->exec("ALTER TABLE bookings 
                ADD COLUMN contact_number_1 VARCHAR(20) NULL AFTER special_requirements,
                ADD COLUMN contact_number_2 VARCHAR(20) NULL AFTER contact_number_1");
    
    // Update existing bookings to use parent's phone number as contact_number_1
    $pdo->exec("UPDATE bookings b
                JOIN parents p ON b.parent_id = p.parent_id
                SET b.contact_number_1 = p.phone
                WHERE b.contact_number_1 IS NULL");
    
    // Add index for better performance
    $pdo->exec("CREATE INDEX idx_bookings_contact ON bookings(contact_number_1)");
    
    echo "✓ Contact number fields added successfully\n\n";
} catch (Exception $e) {
    if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
        echo "✓ Contact number fields already exist\n\n";
    } else {
        echo "✗ Error: " . $e->getMessage() . "\n\n";
    }
}

// Migration 2: Create pricing system
echo "2. Creating provider pricing system...\n";
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS provider_pricing (
        pricing_id INT AUTO_INCREMENT PRIMARY KEY,
        provider_id INT NOT NULL,
        service_type VARCHAR(100) NOT NULL,
        hourly_rate DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        daily_rate DECIMAL(10,2) NULL,
        weekly_rate DECIMAL(10,2) NULL,
        monthly_rate DECIMAL(10,2) NULL,
        minimum_hours INT DEFAULT 1,
        maximum_hours INT DEFAULT 24,
        description TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (provider_id) REFERENCES providers(provider_id) ON DELETE CASCADE,
        UNIQUE KEY unique_provider_service (provider_id, service_type)
    )");
    
    echo "✓ Provider pricing table created\n";
    
    // Check if we need to insert default pricing
    $count_stmt = $pdo->query("SELECT COUNT(*) FROM provider_pricing");
    $count = $count_stmt->fetchColumn();
    
    if ($count == 0) {
        echo "3. Inserting default pricing for existing providers...\n";
        
        // Get all providers
        $providers_stmt = $pdo->query("SELECT provider_id FROM providers");
        $providers = $providers_stmt->fetchAll();
        
        $service_types = [
            ['type' => 'full_day', 'hourly' => 500, 'daily' => 3500, 'desc' => 'Full day care service including meals, activities, and supervision'],
            ['type' => 'half_day', 'hourly' => 600, 'daily' => 2500, 'desc' => 'Half day care service (up to 6 hours) including snacks and activities'],
            ['type' => 'after_school', 'hourly' => 400, 'daily' => 1500, 'desc' => 'After school care service including homework assistance and snacks'],
            ['type' => 'weekend', 'hourly' => 700, 'daily' => 4000, 'desc' => 'Weekend care service with special activities and outings'],
            ['type' => 'overnight', 'hourly' => 800, 'daily' => 5000, 'desc' => 'Overnight care service including dinner, breakfast, and supervision']
        ];
        
        $insert_stmt = $pdo->prepare("
            INSERT INTO provider_pricing (provider_id, service_type, hourly_rate, daily_rate, description)
            VALUES (?, ?, ?, ?, ?)
        ");
        
        foreach ($providers as $provider) {
            foreach ($service_types as $service) {
                $insert_stmt->execute([
                    $provider['provider_id'],
                    $service['type'],
                    $service['hourly'],
                    $service['daily'],
                    $service['desc']
                ]);
            }
        }
        
        echo "✓ Default pricing inserted for " . count($providers) . " providers\n";
    } else {
        echo "✓ Pricing data already exists\n";
    }
    
    // Add indexes
    try {
        $pdo->exec("CREATE INDEX idx_provider_pricing_provider ON provider_pricing(provider_id)");
        $pdo->exec("CREATE INDEX idx_provider_pricing_service ON provider_pricing(service_type)");
        $pdo->exec("CREATE INDEX idx_provider_pricing_active ON provider_pricing(is_active)");
        echo "✓ Indexes created\n";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "✓ Indexes already exist\n";
        } else {
            echo "✗ Index error: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n";
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n\n";
}

echo "Migration completed!\n";
echo "Summary:\n";
echo "- Contact number fields added to bookings table\n";
echo "- Provider pricing system created\n";
echo "- Default pricing data inserted\n";
echo "- Database indexes optimized\n";
?>
