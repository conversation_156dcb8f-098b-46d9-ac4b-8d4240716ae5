<?php
session_start();

// Check if user is logged in and is a parent
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'parent') {
    header("Location: ../login.php");
    exit();
}

require_once '../config/database.php';

// Get parent information
$stmt = $pdo->prepare("SELECT * FROM parents WHERE parent_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$parent = $stmt->fetch();

// Get payment history
$payment_stmt = $pdo->prepare("
    SELECT p.*, b.booking_date, b.child_name, b.service_type, b.total_cost,
           pr.business_name, pr.district
    FROM payments p
    JOIN bookings b ON p.booking_id = b.booking_id
    JOIN providers pr ON p.provider_id = pr.provider_id
    WHERE p.parent_id = ?
    ORDER BY p.payment_date DESC
");
$payment_stmt->execute([$_SESSION['user_id']]);
$payments = $payment_stmt->fetchAll();

// Get outstanding payments (bookings with remaining amounts)
$outstanding_stmt = $pdo->prepare("
    SELECT b.*, p.business_name, p.district, p.phone as provider_phone
    FROM bookings b
    JOIN providers p ON b.provider_id = p.provider_id
    WHERE b.parent_id = ? AND b.remaining_amount > 0 AND b.status = 'confirmed'
    ORDER BY b.booking_date ASC
");
$outstanding_stmt->execute([$_SESSION['user_id']]);
$outstanding_bookings = $outstanding_stmt->fetchAll();

// Calculate payment statistics
$total_paid = 0;
$total_outstanding = 0;
foreach ($payments as $payment) {
    $total_paid += $payment['amount'];
}
foreach ($outstanding_bookings as $booking) {
    $total_outstanding += $booking['remaining_amount'];
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>My Payments - Nestlings</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="" name="keywords">
    <meta content="" name="description">

    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600&family=Nunito:wght@600;700;800&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">
    <link href="../lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">

    <style>
        .payment-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        .payment-card:hover {
            transform: translateY(-5px);
        }
        .payment-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        .status-completed { background: #d4edda; color: #155724; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-failed { background: #f8d7da; color: #721c24; }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }
        .outstanding-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .nav-pills .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>

<body>
    <!-- Spinner Start -->
    <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>
    <!-- Spinner End -->

    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light shadow sticky-top p-0">
        <a href="../index.html" class="navbar-brand d-flex align-items-center px-4 px-lg-5">
            <h2 class="m-0 text-primary"><i class="fa fa-baby me-3"></i>Nestlings</h2>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="dashboard.php" class="nav-item nav-link">Home</a>
                <a href="search.php" class="nav-item nav-link">Find DayCare</a>
                <a href="bookings.php" class="nav-item nav-link">My Bookings</a>
                <a href="payments.php" class="nav-item nav-link active">Payments</a>
                <a href="favorites.php" class="nav-item nav-link">Favorites</a>
                <a href="profile.php" class="nav-item nav-link">Profile</a>
                <a href="../logout.php" class="nav-item nav-link">Logout</a>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Page Header Start -->
    <div class="container-fluid bg-primary py-5 mb-5 page-header">
        <div class="container py-5">
            <div class="row justify-content-center">
                <div class="col-lg-10 text-center">
                    <h1 class="display-3 text-white animated slideInDown">My Payments</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item"><a class="text-white" href="dashboard.php">Home</a></li>
                            <li class="breadcrumb-item text-white active" aria-current="page">Payments</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
    <!-- Page Header End -->

    <!-- Payment Statistics -->
    <div class="container-xxl py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <div class="stats-card">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-0">LKR <?php echo number_format($total_paid, 2); ?></h3>
                                <p class="mb-0">Total Paid</p>
                            </div>
                            <i class="fa fa-check-circle fa-3x opacity-50"></i>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="outstanding-card">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-0">LKR <?php echo number_format($total_outstanding, 2); ?></h3>
                                <p class="mb-0">Outstanding</p>
                            </div>
                            <i class="fa fa-clock fa-3x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Content -->
    <div class="container-xxl py-5">
        <div class="container">
            <!-- Navigation Tabs -->
            <ul class="nav nav-pills mb-4 justify-content-center" id="paymentTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="outstanding-tab" data-bs-toggle="pill" data-bs-target="#outstanding" type="button" role="tab">
                        Outstanding Payments (<?php echo count($outstanding_bookings); ?>)
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="history-tab" data-bs-toggle="pill" data-bs-target="#history" type="button" role="tab">
                        Payment History (<?php echo count($payments); ?>)
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="paymentTabContent">
                <!-- Outstanding Payments Tab -->
                <div class="tab-pane fade show active" id="outstanding" role="tabpanel">
                    <?php if (empty($outstanding_bookings)): ?>
                        <div class="text-center py-5">
                            <i class="fa fa-check-circle fa-3x text-success mb-3"></i>
                            <h4 class="text-muted">No Outstanding Payments</h4>
                            <p class="text-muted">All your bookings are fully paid!</p>
                        </div>
                    <?php else: ?>
                        <div class="row">
                            <?php foreach ($outstanding_bookings as $booking): ?>
                            <div class="col-lg-6">
                                <div class="payment-card">
                                    <div class="card-body p-4">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <h5 class="card-title mb-0"><?php echo htmlspecialchars($booking['business_name']); ?></h5>
                                            <span class="badge bg-warning">Outstanding</span>
                                        </div>
                                        
                                        <div class="row g-3 mb-3">
                                            <div class="col-6">
                                                <small class="text-muted d-block">Child Name</small>
                                                <strong><?php echo htmlspecialchars($booking['child_name']); ?></strong>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted d-block">Service</small>
                                                <strong><?php echo ucfirst(str_replace('_', ' ', $booking['service_type'])); ?></strong>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted d-block">Date</small>
                                                <strong><?php echo date('M d, Y', strtotime($booking['booking_date'])); ?></strong>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted d-block">Outstanding Amount</small>
                                                <strong class="text-danger">LKR <?php echo number_format($booking['remaining_amount'], 2); ?></strong>
                                            </div>
                                        </div>
                                        
                                        <div class="d-grid gap-2">
                                            <a href="payment_confirmation.php?booking_id=<?php echo $booking['booking_id']; ?>" class="btn btn-warning">
                                                <i class="fa fa-credit-card me-2"></i>Make Payment
                                            </a>
                                            <a href="booking_details.php?id=<?php echo $booking['booking_id']; ?>" class="btn btn-outline-primary btn-sm">
                                                <i class="fa fa-eye me-2"></i>View Details
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Payment History Tab -->
                <div class="tab-pane fade" id="history" role="tabpanel">
                    <?php if (empty($payments)): ?>
                        <div class="text-center py-5">
                            <i class="fa fa-credit-card fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">No Payment History</h4>
                            <p class="text-muted">You haven't made any payments yet</p>
                        </div>
                    <?php else: ?>
                        <div class="row">
                            <?php foreach ($payments as $payment): ?>
                            <div class="col-lg-6">
                                <div class="payment-card">
                                    <div class="card-body p-4">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <h5 class="card-title mb-0"><?php echo htmlspecialchars($payment['business_name']); ?></h5>
                                            <span class="payment-status status-<?php echo $payment['payment_status']; ?>">
                                                <?php echo ucfirst($payment['payment_status']); ?>
                                            </span>
                                        </div>
                                        
                                        <div class="row g-3 mb-3">
                                            <div class="col-6">
                                                <small class="text-muted d-block">Payment Date</small>
                                                <strong><?php echo date('M d, Y', strtotime($payment['payment_date'])); ?></strong>
                                                <br><small class="text-muted"><?php echo date('g:i A', strtotime($payment['payment_date'])); ?></small>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted d-block">Amount</small>
                                                <strong class="text-success">LKR <?php echo number_format($payment['amount'], 2); ?></strong>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted d-block">Payment Type</small>
                                                <strong><?php echo ucfirst($payment['payment_type']); ?> Payment</strong>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted d-block">Transaction ID</small>
                                                <code class="small"><?php echo htmlspecialchars($payment['transaction_id']); ?></code>
                                            </div>
                                            <?php if (!empty($payment['card_last_four'])): ?>
                                            <div class="col-12">
                                                <small class="text-muted d-block">Payment Method</small>
                                                <div class="d-flex align-items-center">
                                                    <i class="fa fa-credit-card me-2 text-primary"></i>
                                                    <span>**** <?php echo $payment['card_last_four']; ?></span>
                                                    <?php if (!empty($payment['card_holder_name'])): ?>
                                                    <small class="text-muted ms-2">(<?php echo htmlspecialchars($payment['card_holder_name']); ?>)</small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="d-grid">
                                            <a href="booking_details.php?id=<?php echo $payment['booking_id']; ?>" class="btn btn-outline-primary btn-sm">
                                                <i class="fa fa-eye me-2"></i>View Booking Details
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/owlcarousel/owl.carousel.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>

    <script>
        // Hide spinner when page loads
        window.addEventListener('load', function() {
            document.getElementById('spinner').classList.remove('show');
        });
    </script>
</body>

</html>
