<?php
// Debug script to check database and login issues
require_once 'config/database.php';

echo "<h2>🔍 Nestlings Login Debug Tool</h2>";

// Test database connection
try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p>✅ Database connection successful!</p>";
} catch(PDOException $e) {
    echo "<p>❌ Database connection failed: " . $e->getMessage() . "</p>";
    exit();
}

// Check if providers table exists and has data
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM providers");
    $result = $stmt->fetch();
    echo "<p>📊 Total providers in database: " . $result['count'] . "</p>";
} catch(PDOException $e) {
    echo "<p>❌ Error checking providers table: " . $e->getMessage() . "</p>";
}

// Check specific provider
$test_email = '<EMAIL>';
try {
    $stmt = $pdo->prepare("SELECT provider_id, business_name, email, password FROM providers WHERE email = ?");
    $stmt->execute([$test_email]);
    $provider = $stmt->fetch();
    
    if ($provider) {
        echo "<p>✅ Provider found:</p>";
        echo "<ul>";
        echo "<li><strong>ID:</strong> " . $provider['provider_id'] . "</li>";
        echo "<li><strong>Business:</strong> " . $provider['business_name'] . "</li>";
        echo "<li><strong>Email:</strong> " . $provider['email'] . "</li>";
        echo "<li><strong>Password Hash:</strong> " . substr($provider['password'], 0, 20) . "...</li>";
        echo "</ul>";
        
        // Test password verification
        $test_password = 'password123';
        if (password_verify($test_password, $provider['password'])) {
            echo "<p>✅ Password verification successful for 'password123'</p>";
        } else {
            echo "<p>❌ Password verification failed for 'password123'</p>";
            
            // Try with 'password' (the original hash)
            if (password_verify('password', $provider['password'])) {
                echo "<p>✅ Password verification successful for 'password'</p>";
            } else {
                echo "<p>❌ Password verification failed for 'password' too</p>";
            }
        }
    } else {
        echo "<p>❌ Provider not found with email: $test_email</p>";
    }
} catch(PDOException $e) {
    echo "<p>❌ Error checking provider: " . $e->getMessage() . "</p>";
}

// List all providers
try {
    $stmt = $pdo->query("SELECT provider_id, business_name, email FROM providers LIMIT 5");
    $providers = $stmt->fetchAll();
    
    if ($providers) {
        echo "<h3>📋 First 5 Providers in Database:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Business Name</th><th>Email</th></tr>";
        foreach ($providers as $provider) {
            echo "<tr>";
            echo "<td>" . $provider['provider_id'] . "</td>";
            echo "<td>" . $provider['business_name'] . "</td>";
            echo "<td>" . $provider['email'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch(PDOException $e) {
    echo "<p>❌ Error listing providers: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><strong>💡 Troubleshooting Steps:</strong></p>";
echo "<ol>";
echo "<li>Make sure you imported <code>database_schema_fixed.sql</code></li>";
echo "<li>Check if the database name in <code>config/database.php</code> is correct</li>";
echo "<li>Try using password <code>password</code> instead of <code>password123</code></li>";
echo "<li>If no providers found, re-import the database schema</li>";
echo "</ol>";
?>
