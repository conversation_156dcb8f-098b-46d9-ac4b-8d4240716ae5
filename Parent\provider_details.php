<?php
session_start();

// Check if user is logged in and is a parent
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'parent') {
    header("Location: ../login.php");
    exit();
}

require_once '../config/database.php';

$provider_id = $_GET['id'] ?? null;
if (!$provider_id) {
    header("Location: search.php");
    exit();
}

// Get parent information
$stmt = $pdo->prepare("SELECT * FROM parents WHERE parent_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$parent = $stmt->fetch();

// Get provider details with ratings
$stmt = $pdo->prepare("
    SELECT p.*, 
           AVG(r.rating) as avg_rating, 
           COUNT(r.review_id) as review_count,
           (SELECT COUNT(*) FROM favorites f WHERE f.provider_id = p.provider_id AND f.parent_id = ?) as is_favorite
    FROM providers p 
    LEFT JOIN reviews r ON p.provider_id = r.provider_id 
    WHERE p.provider_id = ? AND p.verification_status = 'verified' AND p.is_active = 1
    GROUP BY p.provider_id
");
$stmt->execute([$_SESSION['user_id'], $provider_id]);
$provider = $stmt->fetch();

if (!$provider) {
    header("Location: search.php");
    exit();
}

// Get recent reviews
$stmt = $pdo->prepare("
    SELECT r.*, p.name as parent_name
    FROM reviews r
    LEFT JOIN parents p ON r.parent_id = p.parent_id
    WHERE r.provider_id = ?
    ORDER BY r.created_at DESC
    LIMIT 5
");
$stmt->execute([$provider_id]);
$reviews = $stmt->fetchAll();

// Get provider availability
$stmt = $pdo->prepare("
    SELECT * FROM provider_availability
    WHERE provider_id = ? AND is_available = 1
    ORDER BY FIELD(day_of_week, 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday')
");
$stmt->execute([$provider_id]);
$availability = $stmt->fetchAll();

// Get gallery images
$stmt = $pdo->prepare("
    SELECT * FROM provider_portfolio
    WHERE provider_id = ? AND is_active = 1
    ORDER BY display_order ASC, created_at DESC
");
$stmt->execute([$provider_id]);
$gallery_images = $stmt->fetchAll();

// Get pricing information
$stmt = $pdo->prepare("
    SELECT * FROM provider_pricing
    WHERE provider_id = ? AND is_active = 1
    ORDER BY service_type
");
$stmt->execute([$provider_id]);
$pricing_data = $stmt->fetchAll();

// Parse service types
$service_types = explode(',', $provider['service_types']);
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title><?php echo htmlspecialchars($provider['business_name']); ?> - Nestlings</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="" name="keywords">
    <meta content="" name="description">

    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600&family=Nunito:wght@600;700;800&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">
    <link href="../lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        .provider-header {
            background: linear-gradient(135deg, var(--primary) 0%, #6BB6FF 100%);
            color: white;
            padding: 60px 0;
        }
        
        .rating-stars {
            color: #ffc107;
        }
        
        .service-badge {
            background: rgba(74, 144, 226, 0.1);
            color: var(--primary);
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            margin: 2px;
            display: inline-block;
        }
        
        .info-card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .pricing-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 2px solid #e9ecef !important;
            transition: all 0.3s ease;
            position: relative;
        }

        .pricing-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            border-color: var(--primary) !important;
        }

        .pricing-header h6 {
            font-size: 1.1rem;
        }

        .pricing-rates {
            border-top: 1px solid #e9ecef;
            border-bottom: 1px solid #e9ecef;
            padding: 15px 0;
        }
        
        .favorite-btn {
            border: none;
            background: none;
            font-size: 1.5rem;
            transition: all 0.3s ease;
        }
        
        .favorite-btn.active {
            color: #dc3545;
        }
        
        .favorite-btn:not(.active) {
            color: #6c757d;
        }
        
        .favorite-btn:hover {
            transform: scale(1.2);
        }
        
        .user-dropdown {
            border-radius: 25px;
            padding: 8px 20px;
            background: linear-gradient(135deg, var(--primary) 0%, #6BB6FF 100%);
            border: none;
            color: white;
            font-weight: 500;
        }
        
        .user-dropdown:hover {
            background: linear-gradient(135deg, #6BB6FF 0%, var(--primary) 100%);
            color: white;
        }
        
        .review-card {
            border-radius: 10px;
            border: 1px solid #e9ecef;
            margin-bottom: 15px;
        }
        
        .availability-day {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 8px;
        }

        .gallery-item:hover .gallery-overlay {
            opacity: 1 !important;
        }

        .gallery-image {
            transition: transform 0.3s ease;
        }

        .gallery-item:hover .gallery-image {
            transform: scale(1.05);
        }

        .modal-dialog.modal-lg {
            max-width: 800px;
        }

        .modal-body img {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
        }
    </style>
</head>

<body>
    <!-- Spinner Start -->
    <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>
    <!-- Spinner End -->

    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light shadow sticky-top p-0">
        <a href="../index.html" class="navbar-brand d-flex align-items-center px-4 px-lg-5">
            <h2 class="m-0 text-primary"><i class="fa fa-baby me-3"></i>Nestlings</h2>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="dashboard.php" class="nav-item nav-link">Home</a>
                <a href="search.php" class="nav-item nav-link">Find DayCare</a>
                <a href="bookings.php" class="nav-item nav-link">My Bookings</a>
                <a href="payments.php" class="nav-item nav-link">Payments</a>
                <a href="favorites.php" class="nav-item nav-link">Favorites</a>
                <a href="reviews.php" class="nav-item nav-link">Reviews</a>
            </div>
            <div class="dropdown">
                <button class="btn user-dropdown dropdown-toggle d-flex align-items-center" type="button" data-bs-toggle="dropdown">
                    <i class="fa fa-user-circle me-2"></i>
                    <span><?php echo htmlspecialchars($parent['name']); ?></span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="profile.php"><i class="fa fa-user me-2"></i>My Profile</a></li>
                    <li><a class="dropdown-item" href="settings.php"><i class="fa fa-cog me-2"></i>Settings</a></li>
                    <li><a class="dropdown-item" href="help.php"><i class="fa fa-question-circle me-2"></i>Help</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="../logout.php"><i class="fa fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Provider Header Start -->
    <div class="provider-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <div class="d-flex align-items-center mb-3">
                        <div class="bg-white rounded-circle d-flex align-items-center justify-content-center me-4" style="width: 80px; height: 80px;">
                            <i class="fa fa-building fa-2x text-primary"></i>
                        </div>
                        <div>
                            <h1 class="display-5 mb-2"><?php echo htmlspecialchars($provider['business_name']); ?></h1>
                            <p class="mb-0">
                                <i class="fa fa-map-marker-alt me-2"></i><?php echo htmlspecialchars($provider['district']); ?>
                                <span class="ms-3">
                                    <i class="fa fa-user me-2"></i><?php echo htmlspecialchars($provider['contact_person']); ?>
                                </span>
                            </p>
                        </div>
                    </div>
                    
                    <div class="d-flex align-items-center">
                        <div class="rating-stars me-3">
                            <?php 
                            $rating = round($provider['avg_rating'] ?? 0);
                            for ($i = 1; $i <= 5; $i++): 
                            ?>
                                <i class="fa fa-star<?php echo $i <= $rating ? '' : '-o'; ?>"></i>
                            <?php endfor; ?>
                        </div>
                        <span class="me-3"><?php echo number_format($provider['avg_rating'] ?? 0, 1); ?> (<?php echo $provider['review_count']; ?> reviews)</span>
                        <span class="badge bg-success">Verified</span>
                    </div>
                </div>
                
                <div class="col-lg-4 text-lg-end">
                    <div class="d-flex flex-column align-items-lg-end">
                        <button class="favorite-btn mb-3 <?php echo $provider['is_favorite'] ? 'active' : ''; ?>" 
                                onclick="toggleFavorite(<?php echo $provider['provider_id']; ?>, this)">
                            <i class="fa fa-heart"></i>
                        </button>
                        
                        <div class="d-grid gap-2 d-md-flex">
                            <a href="book_provider.php?id=<?php echo $provider['provider_id']; ?>" class="btn btn-light btn-lg">
                                <i class="fa fa-calendar-plus me-2"></i>Book Now
                            </a>
                            <a href="tel:<?php echo $provider['phone']; ?>" class="btn btn-outline-light btn-lg">
                                <i class="fa fa-phone me-2"></i>Call
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Provider Header End -->

    <!-- Provider Details Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <div class="row g-5">
                <div class="col-lg-8">
                    <!-- About Section -->
                    <div class="info-card card mb-4">
                        <div class="card-body p-4">
                            <h4 class="mb-3">About <?php echo htmlspecialchars($provider['business_name']); ?></h4>
                            <p class="text-muted"><?php echo nl2br(htmlspecialchars($provider['description'] ?? 'Quality childcare services for your little ones.')); ?></p>
                        </div>
                    </div>

                    <!-- Pricing Section -->
                    <?php if (!empty($pricing_data)): ?>
                    <div class="info-card card mb-4">
                        <div class="card-body p-4">
                            <h4 class="mb-4"><i class="fa fa-tag me-2 text-primary"></i>Service Pricing</h4>
                            <div class="row g-4">
                                <?php
                                $service_names = [
                                    'full_day' => 'Full Day Care',
                                    'half_day' => 'Half Day Care',
                                    'after_school' => 'After School Care',
                                    'weekend' => 'Weekend Care',
                                    'overnight' => 'Overnight Care'
                                ];

                                foreach ($pricing_data as $pricing):
                                ?>
                                    <div class="col-md-6 col-lg-4">
                                        <div class="pricing-card h-100 border rounded-3 p-3 position-relative overflow-hidden">
                                            <div class="pricing-header mb-3">
                                                <h6 class="text-primary mb-1 fw-bold">
                                                    <?php echo $service_names[$pricing['service_type']] ?? ucfirst(str_replace('_', ' ', $pricing['service_type'])); ?>
                                                </h6>
                                                <small class="text-muted"><?php echo ucfirst($pricing['service_type']); ?></small>
                                            </div>

                                            <div class="pricing-rates mb-3">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <span class="text-muted small">Hourly Rate:</span>
                                                    <span class="fw-bold text-primary">LKR <?php echo number_format($pricing['hourly_rate'], 0); ?></span>
                                                </div>

                                                <?php if ($pricing['daily_rate'] && $pricing['daily_rate'] > 0): ?>
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <span class="text-muted small">Daily Rate:</span>
                                                    <span class="fw-bold text-success">LKR <?php echo number_format($pricing['daily_rate'], 0); ?></span>
                                                </div>
                                                <?php endif; ?>

                                                <div class="d-flex justify-content-between align-items-center">
                                                    <span class="text-muted small">Duration:</span>
                                                    <span class="small"><?php echo $pricing['minimum_hours']; ?>-<?php echo $pricing['maximum_hours']; ?> hours</span>
                                                </div>
                                            </div>

                                            <?php if (!empty($pricing['description'])): ?>
                                            <div class="pricing-description">
                                                <p class="small text-muted mb-0"><?php echo htmlspecialchars($pricing['description']); ?></p>
                                            </div>
                                            <?php endif; ?>

                                            <!-- Decorative element -->
                                            <div class="position-absolute top-0 end-0 p-2">
                                                <i class="fa fa-star text-warning opacity-25"></i>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <div class="alert alert-info mt-4 mb-0">
                                <div class="d-flex align-items-start">
                                    <i class="fa fa-info-circle me-2 mt-1"></i>
                                    <div>
                                        <strong>Pricing Information:</strong>
                                        <ul class="mb-0 mt-2">
                                            <li>Final cost will be calculated based on service type and actual duration</li>
                                            <li>Daily rates apply for bookings of 8+ hours when available</li>
                                            <li>All prices are in Sri Lankan Rupees (LKR)</li>
                                            <li>Contact the provider for custom packages or extended care</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Services Section -->
                    <div class="info-card card mb-4">
                        <div class="card-body p-4">
                            <h4 class="mb-3">Services Offered</h4>
                            <div>
                                <?php foreach ($service_types as $service): ?>
                                    <span class="service-badge"><?php echo trim(htmlspecialchars($service)); ?></span>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Photo Gallery Section -->
                    <?php if (!empty($gallery_images)): ?>
                    <div class="info-card card mb-4">
                        <div class="card-body p-4">
                            <h4 class="mb-3">Facility Photos</h4>
                            <div class="row g-3">
                                <?php foreach ($gallery_images as $index => $image): ?>
                                    <div class="col-lg-4 col-md-6">
                                        <div class="gallery-item position-relative">
                                            <img src="../<?php echo htmlspecialchars($image['image_path']); ?>"
                                                 class="img-fluid rounded gallery-image"
                                                 alt="<?php echo htmlspecialchars($image['caption'] ?: 'Facility photo'); ?>"
                                                 style="height: 200px; width: 100%; object-fit: cover; cursor: pointer;"
                                                 data-bs-toggle="modal"
                                                 data-bs-target="#galleryModal"
                                                 data-image="../<?php echo htmlspecialchars($image['image_path']); ?>"
                                                 data-caption="<?php echo htmlspecialchars($image['caption'] ?: 'Facility photo'); ?>">
                                            <div class="gallery-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center"
                                                 style="background: rgba(0,0,0,0.5); opacity: 0; transition: opacity 0.3s ease; border-radius: 0.375rem;">
                                                <i class="fa fa-search-plus text-white fa-2x"></i>
                                            </div>
                                        </div>
                                        <?php if (!empty($image['caption'])): ?>
                                            <p class="text-muted small mt-2 mb-0"><?php echo htmlspecialchars($image['caption']); ?></p>
                                        <?php endif; ?>
                                    </div>
                                    <?php if ($index >= 5) break; // Show only first 6 images ?>
                                <?php endforeach; ?>
                            </div>
                            <?php if (count($gallery_images) > 6): ?>
                                <div class="text-center mt-3">
                                    <button class="btn btn-outline-primary btn-sm" onclick="showAllPhotos()">
                                        <i class="fa fa-images me-2"></i>View All <?php echo count($gallery_images); ?> Photos
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Reviews Section -->
                    <div class="info-card card">
                        <div class="card-body p-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h4 class="mb-0">Reviews (<?php echo $provider['review_count']; ?>)</h4>
                                <a href="reviews.php?provider_id=<?php echo $provider['provider_id']; ?>" class="btn btn-outline-primary btn-sm">View All</a>
                            </div>
                            
                            <?php if (empty($reviews)): ?>
                                <p class="text-muted">No reviews yet. Be the first to review!</p>
                            <?php else: ?>
                                <?php foreach ($reviews as $review): ?>
                                <div class="review-card p-3 mb-3 border rounded">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <div>
                                            <strong><?php echo htmlspecialchars($review['parent_name'] ?? 'Anonymous'); ?></strong>
                                            <div class="rating-stars text-warning">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="fa fa-star<?php echo $i <= $review['rating'] ? '' : '-o'; ?>"></i>
                                                <?php endfor; ?>
                                                <span class="ms-2 text-muted"><?php echo $review['rating']; ?>/5</span>
                                            </div>
                                        </div>
                                        <small class="text-muted"><?php echo date('M d, Y', strtotime($review['created_at'])); ?></small>
                                    </div>
                                    <?php if (!empty($review['comment'])): ?>
                                        <p class="mb-0 text-muted"><?php echo nl2br(htmlspecialchars($review['comment'])); ?></p>
                                    <?php endif; ?>
                                </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Contact Info -->
                    <div class="info-card card mb-4">
                        <div class="card-body p-4">
                            <h5 class="mb-3">Contact Information</h5>
                            <div class="mb-3">
                                <i class="fa fa-phone text-primary me-2"></i>
                                <a href="tel:<?php echo $provider['phone']; ?>"><?php echo htmlspecialchars($provider['phone']); ?></a>
                            </div>
                            <div class="mb-3">
                                <i class="fa fa-envelope text-primary me-2"></i>
                                <a href="mailto:<?php echo $provider['email']; ?>"><?php echo htmlspecialchars($provider['email']); ?></a>
                            </div>
                            <div class="mb-0">
                                <i class="fa fa-map-marker-alt text-primary me-2"></i>
                                <?php echo nl2br(htmlspecialchars($provider['address'])); ?>
                            </div>
                        </div>
                    </div>

                    <!-- Capacity Info -->
                    <div class="info-card card mb-4">
                        <div class="card-body p-4">
                            <h5 class="mb-3">Capacity</h5>
                            <div class="text-center">
                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 60px; height: 60px;">
                                    <i class="fa fa-users text-white"></i>
                                </div>
                                <h3 class="text-primary"><?php echo $provider['capacity']; ?></h3>
                                <p class="mb-0 text-muted">Children</p>
                            </div>
                        </div>
                    </div>

                    <!-- Availability -->
                    <?php if (!empty($availability)): ?>
                    <div class="info-card card">
                        <div class="card-body p-4">
                            <h5 class="mb-3">Availability</h5>
                            <?php foreach ($availability as $day): ?>
                            <div class="availability-day">
                                <div class="d-flex justify-content-between">
                                    <strong><?php echo $day['day_of_week']; ?></strong>
                                    <span>
                                        <?php echo date('g:i A', strtotime($day['start_time'])); ?> - 
                                        <?php echo date('g:i A', strtotime($day['end_time'])); ?>
                                    </span>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <!-- Provider Details End -->

    <!-- Footer Start -->
    <div class="container-fluid bg-dark text-light footer pt-5 mt-5 wow fadeIn" data-wow-delay="0.1s">
        <div class="container py-5">
            <div class="row g-5">
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3"><i class="fa fa-baby me-2"></i>Nestlings</h4>
                    <p>Connecting families with trusted childcare providers across Sri Lanka. Safe, verified, and reliable daycare solutions for your little ones.</p>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Quick Links</h4>
                    <a class="btn btn-link" href="../about.html">About Us</a>
                    <a class="btn btn-link" href="../contact.html">Contact Us</a>
                    <a class="btn btn-link" href="../services.html">Our Services</a>
                    <a class="btn btn-link" href="search.php">Find DayCare</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Support</h4>
                    <a class="btn btn-link" href="help.php">Help Center</a>
                    <a class="btn btn-link" href="">Safety Guidelines</a>
                    <a class="btn btn-link" href="">Provider Resources</a>
                    <a class="btn btn-link" href="">Parent Guide</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Contact Info</h4>
                    <p class="mb-2"><i class="fa fa-map-marker-alt me-3"></i>123 Main Street, Colombo, Sri Lanka</p>
                    <p class="mb-2"><i class="fa fa-phone-alt me-3"></i>+94 11 234 5678</p>
                    <p class="mb-2"><i class="fa fa-envelope me-3"></i><EMAIL></p>
                </div>
            </div>
        </div>
    </div>
    <!-- Footer End -->

    <!-- Back to Top -->
    <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>

    <!-- Gallery Modal -->
    <div class="modal fade" id="galleryModal" tabindex="-1" aria-labelledby="galleryModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="galleryModalLabel">Facility Photo</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" alt="Facility photo" class="img-fluid">
                    <p id="modalCaption" class="mt-3 text-muted"></p>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/owlcarousel/owl.carousel.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>
    
    <script>
        function toggleFavorite(providerId, button) {
            fetch('toggle_favorite.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    provider_id: providerId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    button.classList.toggle('active');
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        }

        // Gallery modal functionality
        document.addEventListener('DOMContentLoaded', function() {
            const galleryModal = document.getElementById('galleryModal');
            const modalImage = document.getElementById('modalImage');
            const modalCaption = document.getElementById('modalCaption');

            // Handle gallery image clicks
            document.querySelectorAll('[data-bs-target="#galleryModal"]').forEach(function(element) {
                element.addEventListener('click', function() {
                    const imageSrc = this.getAttribute('data-image');
                    const caption = this.getAttribute('data-caption');

                    modalImage.src = imageSrc;
                    modalCaption.textContent = caption || 'Facility photo';
                });
            });
        });

        function showAllPhotos() {
            // Show all hidden gallery items
            document.querySelectorAll('.gallery-item').forEach(function(item, index) {
                if (index >= 6) {
                    item.style.display = 'block';
                }
            });

            // Hide the "View All" button
            event.target.style.display = 'none';
        }
    </script>
</body>

</html>
