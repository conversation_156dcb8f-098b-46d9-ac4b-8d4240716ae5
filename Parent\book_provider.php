<?php
session_start();

// Check if user is logged in and is a parent
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'parent') {
    header("Location: ../login.php");
    exit();
}

require_once '../config/database.php';

// Get provider ID
$provider_id = $_GET['id'] ?? 0;

if (!$provider_id) {
    header("Location: search.php");
    exit();
}

// Get parent information
$stmt = $pdo->prepare("SELECT * FROM parents WHERE parent_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$parent = $stmt->fetch();

if (!$parent) {
    header("Location: ../login.php");
    exit();
}

// Get provider details
$stmt = $pdo->prepare("
    SELECT p.*, 
           AVG(r.rating) as avg_rating, 
           COUNT(r.review_id) as review_count
    FROM providers p 
    LEFT JOIN reviews r ON p.provider_id = r.provider_id 
    WHERE p.provider_id = ? AND p.verification_status = 'verified' AND p.is_active = 1
    GROUP BY p.provider_id
");
$stmt->execute([$provider_id]);
$provider = $stmt->fetch();

if (!$provider) {
    header("Location: search.php");
    exit();
}

// Handle booking submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $service_type = $_POST['service_type'] ?? '';
    $start_date = $_POST['start_date'] ?? '';
    $end_date = $_POST['end_date'] ?? '';
    $start_time = $_POST['start_time'] ?? '';
    $end_time = $_POST['end_time'] ?? '';
    $child_name = $_POST['child_name'] ?? '';
    $child_age = $_POST['child_age'] ?? '';
    $special_requirements = $_POST['special_requirements'] ?? '';
    $contact_number_1 = $_POST['contact_number_1'] ?? '';
    $contact_number_2 = $_POST['contact_number_2'] ?? '';
    
    $errors = [];
    
    // Validation
    if (empty($service_type)) $errors[] = "Service type is required";
    if (empty($start_date)) $errors[] = "Start date is required";
    if (empty($start_time)) $errors[] = "Start time is required";
    if (empty($end_time)) $errors[] = "End time is required";
    if (empty($child_name)) $errors[] = "Child name is required";
    if (empty($child_age)) $errors[] = "Child age is required";
    if (empty($contact_number_1)) $errors[] = "At least one contact number is required";
    
    // Validate dates
    if (!empty($start_date) && strtotime($start_date) < strtotime('today')) {
        $errors[] = "Start date cannot be in the past";
    }
    
    if (!empty($end_date) && !empty($start_date) && strtotime($end_date) < strtotime($start_date)) {
        $errors[] = "End date cannot be before start date";
    }

    // Validate times
    if (!empty($start_time) && !empty($end_time) && strtotime($end_time) <= strtotime($start_time)) {
        $errors[] = "End time must be after start time";
    }
    
    if (empty($errors)) {
        try {
            // Calculate duration in hours
            $duration = 8; // Default duration
            if (!empty($start_time) && !empty($end_time)) {
                $start = new DateTime($start_time);
                $end = new DateTime($end_time);
                $interval = $start->diff($end);
                $duration = $interval->h + ($interval->i / 60); // Convert to decimal hours
            }

            // Get pricing for this service type
            $pricing_stmt = $pdo->prepare("
                SELECT * FROM provider_pricing
                WHERE provider_id = ? AND service_type = ? AND is_active = TRUE
            ");
            $pricing_stmt->execute([$provider_id, $service_type]);
            $pricing = $pricing_stmt->fetch();

            // Calculate total cost
            $total_cost = 0;
            if ($pricing) {
                // Use daily rate if duration is 8+ hours and daily rate exists
                if ($duration >= 8 && $pricing['daily_rate'] > 0) {
                    $total_cost = $pricing['daily_rate'];
                } else {
                    // Use hourly rate
                    $total_cost = $pricing['hourly_rate'] * $duration;
                }
            } else {
                // Default pricing if no specific pricing found
                $default_rates = [
                    'full_day' => 500,
                    'half_day' => 600,
                    'after_school' => 400,
                    'weekend' => 700,
                    'overnight' => 800
                ];
                $hourly_rate = $default_rates[$service_type] ?? 500;
                $total_cost = $hourly_rate * $duration;
            }

            // Use start_date as booking_date, set default times if not provided
            $booking_date = $start_date;
            $booking_start_time = $start_time ?: '08:00:00';
            $booking_end_time = $end_time ?: '17:00:00';

            // Insert booking
            $stmt = $pdo->prepare("
                INSERT INTO bookings (parent_id, provider_id, service_type, booking_date, start_time, end_time,
                                    duration, child_name, child_age, special_requirements, contact_number_1, contact_number_2,
                                    total_cost, payment_status, paid_amount, remaining_amount, status, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'unpaid', 0.00, ?, 'pending', NOW())
            ");

            $stmt->execute([
                $_SESSION['user_id'],
                $provider_id,
                $service_type,
                $booking_date,
                $booking_start_time,
                $booking_end_time,
                $duration,
                $child_name,
                $child_age,
                $special_requirements,
                $contact_number_1,
                $contact_number_2,
                $total_cost,
                $total_cost  // remaining_amount = total_cost initially
            ]);
            
            $booking_id = $pdo->lastInsertId();

            // Create notification for provider
            $notification_stmt = $pdo->prepare("
                INSERT INTO notifications (user_type, user_id, title, message, type, created_at)
                VALUES ('provider', ?, 'New Booking Request', ?, 'booking', NOW())
            ");
            $notification_message = "New booking request from " . htmlspecialchars($parent['name']) . " for " . htmlspecialchars($child_name) . " on " . date('M d, Y', strtotime($booking_date));
            $notification_stmt->execute([$provider_id, $notification_message]);

            // Redirect to booking submitted confirmation
            header("Location: booking_submitted.php?booking_id=" . $booking_id);
            exit();
            
        } catch (Exception $e) {
            $errors[] = "Failed to create booking. Please try again.";
        }
    }
}

// Parse service types
$service_types = explode(',', $provider['service_types']);
$service_type_display = [
    'full_day' => 'Full Day Care',
    'half_day' => 'Half Day Care',
    'after_school' => 'After School Care',
    'weekend' => 'Weekend Care',
    'holiday' => 'Holiday Care',
    'infant' => 'Infant Care (0-2 years)',
    'toddler' => 'Toddler Care (2-4 years)',
    'preschool' => 'Preschool (4-6 years)'
];
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>Book Provider - Nestlings</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="" name="keywords">
    <meta content="" name="description">

    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600&family=Nunito:wght@600;700;800&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">
    <link href="../lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        .booking-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
        }
        
        .provider-summary {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-top: -50px;
            position: relative;
            z-index: 10;
        }
        
        .booking-form {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-book {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 15px 40px;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-book:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .provider-info {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .provider-avatar {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
        }
        
        .rating-stars {
            color: #ffc107;
        }
    </style>
</head>

<body>
    <!-- Spinner Start -->
    <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>
    <!-- Spinner End -->

    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light shadow sticky-top p-0">
        <a href="../index.html" class="navbar-brand d-flex align-items-center px-4 px-lg-5">
            <h2 class="m-0 text-primary"><i class="fa fa-baby me-3"></i>Nestlings</h2>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="dashboard.php" class="nav-item nav-link">Home</a>
                <a href="search.php" class="nav-item nav-link">Find DayCare</a>
                <a href="bookings.php" class="nav-item nav-link">My Bookings</a>
                <a href="favorites.php" class="nav-item nav-link">Favorites</a>
                <a href="reviews.php" class="nav-item nav-link">Reviews</a>
            </div>
            <div class="dropdown">
                <a href="#" class="btn btn-primary py-4 px-lg-5 d-none d-lg-block dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fa fa-user me-2"></i><?php echo htmlspecialchars($_SESSION['user_name']); ?>
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="profile.php"><i class="fa fa-user me-2"></i>Profile</a></li>
                    <li><a class="dropdown-item" href="settings.php"><i class="fa fa-cog me-2"></i>Settings</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="../logout.php"><i class="fa fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Header Start -->
    <div class="booking-header">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8 text-center">
                    <h1 class="display-4 mb-3">Book Childcare Service</h1>
                    <p class="fs-5">Schedule your childcare booking with trusted providers</p>
                </div>
            </div>
        </div>
    </div>
    <!-- Header End -->

    <!-- Booking Content Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <!-- Provider Summary -->
            <div class="provider-summary mb-5">
                <div class="provider-info">
                    <div class="provider-avatar">
                        <i class="fa fa-building fa-2x text-white"></i>
                    </div>
                    <div>
                        <h3 class="mb-1"><?php echo htmlspecialchars($provider['business_name']); ?></h3>
                        <p class="text-muted mb-1">
                            <i class="fa fa-map-marker-alt me-2"></i><?php echo htmlspecialchars($provider['district']); ?>
                        </p>
                        <div class="rating-stars">
                            <?php 
                            $rating = $provider['avg_rating'] ?: 0;
                            for ($i = 1; $i <= 5; $i++): 
                            ?>
                                <i class="fa fa-star<?php echo $i <= $rating ? '' : '-o'; ?>"></i>
                            <?php endfor; ?>
                            <span class="ms-2 text-muted">(<?php echo $provider['review_count']; ?> reviews)</span>
                        </div>
                    </div>
                </div>
                <p class="text-muted mb-0"><?php echo htmlspecialchars($provider['description'] ?? 'Quality childcare services for your little ones.'); ?></p>
            </div>

            <div class="row g-5">
                <div class="col-lg-8">
                    <!-- Pricing Information -->
                    <div class="pricing-info bg-light p-4 rounded mb-4">
                        <h5 class="mb-3"><i class="fa fa-tag me-2"></i>Service Pricing</h5>
                        <div class="row">
                            <?php
                            // Get pricing information for display
                            $pricing_display_stmt = $pdo->prepare("
                                SELECT * FROM provider_pricing
                                WHERE provider_id = ? AND is_active = TRUE
                                ORDER BY service_type
                            ");
                            $pricing_display_stmt->execute([$provider_id]);
                            $all_pricing = $pricing_display_stmt->fetchAll();

                            $service_names = [
                                'full_day' => 'Full Day Care',
                                'half_day' => 'Half Day Care',
                                'after_school' => 'After School Care',
                                'weekend' => 'Weekend Care',
                                'overnight' => 'Overnight Care'
                            ];
                            ?>

                            <?php foreach ($all_pricing as $price): ?>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="pricing-item border rounded p-3 h-100">
                                        <h6 class="text-primary mb-2"><?php echo $service_names[$price['service_type']] ?? ucfirst($price['service_type']); ?></h6>
                                        <div class="pricing-rates">
                                            <div class="mb-1">
                                                <strong>LKR <?php echo number_format($price['hourly_rate'], 0); ?></strong>
                                                <small class="text-muted">/hour</small>
                                            </div>
                                            <?php if ($price['daily_rate']): ?>
                                                <div class="mb-1">
                                                    <strong>LKR <?php echo number_format($price['daily_rate'], 0); ?></strong>
                                                    <small class="text-muted">/day</small>
                                                </div>
                                            <?php endif; ?>
                                            <small class="text-muted d-block">
                                                <?php echo $price['minimum_hours']; ?>-<?php echo $price['maximum_hours']; ?> hours
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="alert alert-info mb-0">
                            <i class="fa fa-info-circle me-2"></i>
                            <strong>Note:</strong> Final cost will be calculated automatically based on your selected service type and duration.
                            For 8+ hour bookings, daily rates apply when available.
                        </div>
                    </div>

                    <!-- Booking Form -->
                    <div class="booking-form">
                        <h4 class="mb-4">Booking Details</h4>
                        
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <form method="POST">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Service Type *</label>
                                        <select class="form-select" name="service_type" required>
                                            <option value="">Select Service Type</option>
                                            <?php foreach ($service_types as $service): ?>
                                                <option value="<?php echo trim($service); ?>" <?php echo ($_POST['service_type'] ?? '') == trim($service) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($service_type_display[trim($service)] ?? trim($service)); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Start Date *</label>
                                        <input type="date" class="form-control" name="start_date" value="<?php echo htmlspecialchars($_POST['start_date'] ?? ''); ?>" min="<?php echo date('Y-m-d'); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">End Date (Optional)</label>
                                        <input type="date" class="form-control" name="end_date" value="<?php echo htmlspecialchars($_POST['end_date'] ?? ''); ?>" min="<?php echo date('Y-m-d'); ?>">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label">Start Time *</label>
                                        <input type="time" class="form-control" name="start_time" value="<?php echo htmlspecialchars($_POST['start_time'] ?? '08:00'); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label">End Time *</label>
                                        <input type="time" class="form-control" name="end_time" value="<?php echo htmlspecialchars($_POST['end_time'] ?? '17:00'); ?>" required>
                                    </div>
                                </div>
                            </div>

                            <h5 class="mt-4 mb-3">Child Information</h5>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Child's Name *</label>
                                        <input type="text" class="form-control" name="child_name" value="<?php echo htmlspecialchars($_POST['child_name'] ?? ''); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Child's Age *</label>
                                        <select class="form-select" name="child_age" required>
                                            <option value="">Select Age</option>
                                            <?php for ($i = 0; $i <= 12; $i++): ?>
                                                <option value="<?php echo $i; ?>" <?php echo ($_POST['child_age'] ?? '') == $i ? 'selected' : ''; ?>>
                                                    <?php echo $i; ?> year<?php echo $i != 1 ? 's' : ''; ?> old
                                                </option>
                                            <?php endfor; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Primary Contact Number *</label>
                                        <input type="tel" class="form-control" name="contact_number_1" value="<?php echo htmlspecialchars($_POST['contact_number_1'] ?? $parent['phone']); ?>" placeholder="+94 XX XXX XXXX" required>
                                        <small class="text-muted">Main contact number for emergencies</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Secondary Contact Number</label>
                                        <input type="tel" class="form-control" name="contact_number_2" value="<?php echo htmlspecialchars($_POST['contact_number_2'] ?? ''); ?>" placeholder="+94 XX XXX XXXX">
                                        <small class="text-muted">Alternative contact (optional)</small>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <label class="form-label">Special Requirements</label>
                                        <textarea class="form-control" name="special_requirements" rows="4" placeholder="Any special needs, allergies, or requirements for your child..."><?php echo htmlspecialchars($_POST['special_requirements'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between mt-4">
                                <a href="provider_details.php?id=<?php echo $provider['provider_id']; ?>" class="btn btn-outline-secondary">
                                    <i class="fa fa-arrow-left me-2"></i>Back to Details
                                </a>
                                <button type="submit" class="btn btn-book">
                                    <i class="fa fa-calendar-check me-2"></i>Book Now
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Booking Summary -->
                    <div class="booking-form">
                        <h5 class="mb-3">Booking Summary</h5>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Provider:</span>
                            <strong><?php echo htmlspecialchars($provider['business_name']); ?></strong>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Location:</span>
                            <span><?php echo htmlspecialchars($provider['district']); ?></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Capacity:</span>
                            <span><?php echo $provider['capacity']; ?> children</span>
                        </div>
                        <div class="d-flex justify-content-between mb-3">
                            <span>Rating:</span>
                            <span>
                                <?php echo number_format($provider['avg_rating'] ?: 0, 1); ?>/5.0 
                                <small class="text-muted">(<?php echo $provider['review_count']; ?>)</small>
                            </span>
                        </div>
                        
                        <hr>
                        
                        <h6 class="mb-2">Available Services:</h6>
                        <div class="mb-3">
                            <?php foreach ($service_types as $service): ?>
                                <span class="badge bg-primary me-1 mb-1">
                                    <?php echo htmlspecialchars($service_type_display[trim($service)] ?? trim($service)); ?>
                                </span>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="alert alert-info">
                            <small>
                                <i class="fa fa-info-circle me-2"></i>
                                Your booking will be pending until confirmed by the provider. You will receive a notification once confirmed.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Booking Content End -->

    <!-- Footer Start -->
    <div class="container-fluid bg-dark text-light footer pt-5 mt-5 wow fadeIn" data-wow-delay="0.1s">
        <div class="container py-5">
            <div class="row g-5">
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3"><i class="fa fa-baby me-2"></i>Nestlings</h4>
                    <p>Connecting families with trusted childcare providers across Sri Lanka. Safe, verified, and reliable daycare solutions for your little ones.</p>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Quick Links</h4>
                    <a class="btn btn-link" href="../about.html">About Us</a>
                    <a class="btn btn-link" href="../contact.html">Contact Us</a>
                    <a class="btn btn-link" href="../services.html">Our Services</a>
                    <a class="btn btn-link" href="search.php">Find DayCare</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Contact</h4>
                    <p class="mb-2"><i class="fa fa-map-marker-alt me-3"></i>123 Street, Colombo, Sri Lanka</p>
                    <p class="mb-2"><i class="fa fa-phone-alt me-3"></i>+94 11 234 5678</p>
                    <p class="mb-2"><i class="fa fa-envelope me-3"></i><EMAIL></p>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Follow Us</h4>
                    <div class="d-flex pt-2">
                        <a class="btn btn-outline-light btn-social" href=""><i class="fab fa-twitter"></i></a>
                        <a class="btn btn-outline-light btn-social" href=""><i class="fab fa-facebook-f"></i></a>
                        <a class="btn btn-outline-light btn-social" href=""><i class="fab fa-youtube"></i></a>
                        <a class="btn btn-outline-light btn-social" href=""><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
            </div>
        </div>
        <div class="container">
            <div class="copyright">
                <div class="row">
                    <div class="col-md-6 text-center text-md-start mb-3 mb-md-0">
                        &copy; <a class="border-bottom" href="#">Nestlings</a>, All Right Reserved. 
                        Designed By <a class="border-bottom" href="#">Nestlings Team</a>
                    </div>
                    <div class="col-md-6 text-center text-md-end">
                        <div class="footer-menu">
                            <a href="#">Privacy Policy</a>
                            <a href="#">Terms of Service</a>
                            <a href="#">Cookie Policy</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Footer End -->

    <!-- Back to Top -->
    <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/owlcarousel/owl.carousel.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>
</body>

</html>
