<?php
session_start();

// Check if user is logged in and is a parent
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'parent') {
    header("Location: ../login.php");
    exit();
}

require_once '../config/database.php';

$booking_id = $_GET['id'] ?? 0;

// Get booking details with provider information
$stmt = $pdo->prepare("
    SELECT b.*, p.business_name, p.district, p.phone as provider_phone, p.email as provider_email,
           p.address, p.description, p.capacity
    FROM bookings b
    JOIN providers p ON b.provider_id = p.provider_id
    WHERE b.booking_id = ? AND b.parent_id = ?
");
$stmt->execute([$booking_id, $_SESSION['user_id']]);
$booking = $stmt->fetch();

if (!$booking) {
    header("Location: bookings.php");
    exit();
}

// Get parent information
$stmt = $pdo->prepare("SELECT * FROM parents WHERE parent_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$parent = $stmt->fetch();

// Get payment history for this booking
$payment_stmt = $pdo->prepare("
    SELECT * FROM payments 
    WHERE booking_id = ? 
    ORDER BY payment_date DESC
");
$payment_stmt->execute([$booking_id]);
$payments = $payment_stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>Booking Details - Nestlings</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="" name="keywords">
    <meta content="" name="description">

    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600&family=Nunito:wght@600;700;800&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">
    <link href="../lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">

    <style>
        .detail-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 0 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .detail-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px 15px 0 0;
        }
        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.8rem;
        }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-confirmed { background: #d1ecf1; color: #0c5460; }
        .status-completed { background: #d4edda; color: #155724; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
        .info-item {
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }
        .info-item:last-child {
            border-bottom: none;
        }
        .payment-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
        }
        .payment-status {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        .status-completed { background: #d4edda; color: #155724; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-failed { background: #f8d7da; color: #721c24; }
    </style>
</head>

<body>
    <!-- Spinner Start -->
    <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>
    <!-- Spinner End -->

    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light shadow sticky-top p-0">
        <a href="../index.html" class="navbar-brand d-flex align-items-center px-4 px-lg-5">
            <h2 class="m-0 text-primary"><i class="fa fa-baby me-3"></i>Nestlings</h2>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="dashboard.php" class="nav-item nav-link">Home</a>
                <a href="search.php" class="nav-item nav-link">Find DayCare</a>
                <a href="bookings.php" class="nav-item nav-link">My Bookings</a>
                <a href="favorites.php" class="nav-item nav-link">Favorites</a>
                <a href="profile.php" class="nav-item nav-link">Profile</a>
                <a href="../logout.php" class="nav-item nav-link">Logout</a>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Page Header Start -->
    <div class="container-fluid bg-primary py-5 mb-5 page-header">
        <div class="container py-5">
            <div class="row justify-content-center">
                <div class="col-lg-10 text-center">
                    <h1 class="display-3 text-white animated slideInDown">Booking Details</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item"><a class="text-white" href="dashboard.php">Home</a></li>
                            <li class="breadcrumb-item"><a class="text-white" href="bookings.php">My Bookings</a></li>
                            <li class="breadcrumb-item text-white active" aria-current="page">Booking Details</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
    <!-- Page Header End -->

    <!-- Booking Details Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <!-- Main Booking Details -->
                    <div class="detail-card">
                        <div class="detail-header">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h3 class="mb-2"><?php echo htmlspecialchars($booking['business_name']); ?></h3>
                                    <p class="mb-0"><i class="fa fa-map-marker-alt me-2"></i><?php echo htmlspecialchars($booking['district']); ?></p>
                                </div>
                                <span class="status-badge status-<?php echo $booking['status']; ?>">
                                    <?php echo ucfirst($booking['status']); ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="p-4">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <strong>Booking ID:</strong>
                                        <span class="text-muted">#<?php echo str_pad($booking['booking_id'], 6, '0', STR_PAD_LEFT); ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>Child Name:</strong>
                                        <span><?php echo htmlspecialchars($booking['child_name']); ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>Child Age:</strong>
                                        <span><?php echo $booking['child_age']; ?> years old</span>
                                    </div>
                                    <div class="info-item">
                                        <strong>Service Type:</strong>
                                        <span class="badge bg-primary"><?php echo ucfirst(str_replace('_', ' ', $booking['service_type'])); ?></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <strong>Booking Date:</strong>
                                        <span><?php echo date('F d, Y', strtotime($booking['booking_date'])); ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>Time:</strong>
                                        <span><?php echo date('g:i A', strtotime($booking['start_time'])); ?> - <?php echo date('g:i A', strtotime($booking['end_time'])); ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>Total Cost:</strong>
                                        <span class="text-success fw-bold">LKR <?php echo number_format($booking['total_cost'], 2); ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>Created:</strong>
                                        <span><?php echo date('M d, Y g:i A', strtotime($booking['created_at'])); ?></span>
                                    </div>
                                </div>
                            </div>
                            
                            <?php if (!empty($booking['special_requirements'])): ?>
                            <div class="info-item">
                                <strong>Special Requirements:</strong>
                                <p class="mb-0 mt-2"><?php echo nl2br(htmlspecialchars($booking['special_requirements'])); ?></p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Payment History -->
                    <?php if (!empty($payments)): ?>
                    <div class="detail-card">
                        <div class="p-4">
                            <h5 class="mb-4"><i class="fa fa-credit-card me-2"></i>Payment History</h5>
                            <?php foreach ($payments as $payment): ?>
                            <div class="payment-card">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">Payment #<?php echo $payment['payment_id']; ?></h6>
                                        <p class="text-muted mb-2"><?php echo date('M d, Y g:i A', strtotime($payment['payment_date'])); ?></p>
                                        <p class="mb-0"><strong>Amount:</strong> LKR <?php echo number_format($payment['amount'], 2); ?></p>
                                        <p class="mb-0"><strong>Type:</strong> <?php echo ucfirst($payment['payment_type']); ?> Payment</p>
                                        <?php if (!empty($payment['card_last_four'])): ?>
                                        <p class="mb-0"><strong>Card:</strong> **** <?php echo $payment['card_last_four']; ?></p>
                                        <?php endif; ?>
                                    </div>
                                    <span class="payment-status status-<?php echo $payment['payment_status']; ?>">
                                        <?php echo ucfirst($payment['payment_status']); ?>
                                    </span>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <div class="col-lg-4">
                    <!-- Action Buttons -->
                    <div class="detail-card">
                        <div class="p-4">
                            <h5 class="mb-4">Actions</h5>
                            <div class="d-grid gap-3">
                                <?php if ($booking['status'] == 'pending'): ?>
                                <div class="alert alert-info">
                                    <i class="fa fa-clock me-2"></i>
                                    <strong>Waiting for Confirmation</strong><br>
                                    <small>The daycare provider will review and confirm your booking request.</small>
                                </div>
                                <?php elseif ($booking['status'] == 'confirmed' && isset($booking['payment_status']) && $booking['remaining_amount'] > 0): ?>
                                <a href="payment_confirmation.php?booking_id=<?php echo $booking['booking_id']; ?>" class="btn btn-warning">
                                    <i class="fa fa-credit-card me-2"></i>Make Payment
                                </a>
                                <?php elseif ($booking['status'] == 'confirmed' && $booking['remaining_amount'] <= 0): ?>
                                <div class="alert alert-success">
                                    <i class="fa fa-check me-2"></i>
                                    <strong>Payment Completed</strong><br>
                                    <small>Your booking is confirmed and payment is complete.</small>
                                </div>
                                <?php elseif ($booking['status'] == 'completed'): ?>
                                <a href="reviews.php?booking_id=<?php echo $booking['booking_id']; ?>" class="btn btn-warning">
                                    <i class="fa fa-star me-2"></i>Add Review
                                </a>
                                <?php elseif ($booking['status'] == 'cancelled'): ?>
                                <div class="alert alert-danger">
                                    <i class="fa fa-times me-2"></i>
                                    <strong>Booking Cancelled</strong><br>
                                    <small>This booking has been cancelled.</small>
                                </div>
                                <?php endif; ?>
                                
                                <a href="tel:<?php echo $booking['provider_phone']; ?>" class="btn btn-success">
                                    <i class="fa fa-phone me-2"></i>Call Provider
                                </a>
                                
                                <?php if ($booking['status'] == 'completed'): ?>
                                <a href="add_review.php?booking_id=<?php echo $booking['booking_id']; ?>" class="btn btn-warning">
                                    <i class="fa fa-star me-2"></i>Add Review
                                </a>
                                <?php endif; ?>
                                
                                <a href="bookings.php" class="btn btn-outline-primary">
                                    <i class="fa fa-arrow-left me-2"></i>Back to Bookings
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Provider Contact -->
                    <div class="detail-card">
                        <div class="p-4">
                            <h5 class="mb-4">Provider Contact</h5>
                            <div class="info-item">
                                <strong>Business:</strong>
                                <span><?php echo htmlspecialchars($booking['business_name']); ?></span>
                            </div>
                            <div class="info-item">
                                <strong>Phone:</strong>
                                <span><?php echo htmlspecialchars($booking['provider_phone']); ?></span>
                            </div>
                            <div class="info-item">
                                <strong>Email:</strong>
                                <span><?php echo htmlspecialchars($booking['provider_email']); ?></span>
                            </div>
                            <?php if (!empty($booking['address'])): ?>
                            <div class="info-item">
                                <strong>Address:</strong>
                                <span><?php echo htmlspecialchars($booking['address']); ?></span>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Booking Details End -->

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/owlcarousel/owl.carousel.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>

    <script>
        // Hide spinner when page loads
        window.addEventListener('load', function() {
            document.getElementById('spinner').classList.remove('show');
        });
    </script>
</body>

</html>
