<?php
require_once 'config/database.php';

echo "<h2>Test Payment Update for Booking #20</h2>";

try {
    $booking_id = 20;
    
    // First, check current status
    echo "<h3>Current Status:</h3>";
    $stmt = $pdo->prepare("SELECT booking_id, payment_status, paid_amount, remaining_amount, total_cost FROM bookings WHERE booking_id = ?");
    $stmt->execute([$booking_id]);
    $current = $stmt->fetch();
    
    if ($current) {
        echo "<p><strong>Booking ID:</strong> {$current['booking_id']}</p>";
        echo "<p><strong>Payment Status:</strong> {$current['payment_status']}</p>";
        echo "<p><strong>Paid Amount:</strong> LKR " . number_format($current['paid_amount'], 2) . "</p>";
        echo "<p><strong>Remaining Amount:</strong> LKR " . number_format($current['remaining_amount'], 2) . "</p>";
        echo "<p><strong>Total Cost:</strong> LKR " . number_format($current['total_cost'], 2) . "</p>";
    } else {
        echo "<p style='color: red;'>Booking not found!</p>";
        exit();
    }
    
    // Update payment status to paid
    echo "<h3>Updating Payment Status...</h3>";
    
    $update_stmt = $pdo->prepare("
        UPDATE bookings 
        SET payment_status = 'paid', 
            paid_amount = total_cost, 
            remaining_amount = 0 
        WHERE booking_id = ?
    ");
    
    $result = $update_stmt->execute([$booking_id]);
    
    if ($result) {
        echo "<p style='color: green;'>✓ Update executed successfully!</p>";
        echo "<p><strong>Affected rows:</strong> " . $update_stmt->rowCount() . "</p>";
        
        // Check updated status
        echo "<h3>Updated Status:</h3>";
        $stmt = $pdo->prepare("SELECT booking_id, payment_status, paid_amount, remaining_amount, total_cost FROM bookings WHERE booking_id = ?");
        $stmt->execute([$booking_id]);
        $updated = $stmt->fetch();
        
        if ($updated) {
            echo "<p><strong>Booking ID:</strong> {$updated['booking_id']}</p>";
            echo "<p><strong>Payment Status:</strong> <span style='color: green;'>{$updated['payment_status']}</span></p>";
            echo "<p><strong>Paid Amount:</strong> <span style='color: green;'>LKR " . number_format($updated['paid_amount'], 2) . "</span></p>";
            echo "<p><strong>Remaining Amount:</strong> <span style='color: green;'>LKR " . number_format($updated['remaining_amount'], 2) . "</span></p>";
            echo "<p><strong>Total Cost:</strong> LKR " . number_format($updated['total_cost'], 2) . "</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ Update failed!</p>";
    }
    
    echo "<hr>";
    echo "<p><a href='Parent/bookings.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Bookings Page</a></p>";
    echo "<p><a href='debug_booking_payment.php?booking_id=20' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Debug Booking</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
