-- Database tables for Management Center functionality

-- Staff Members Table
CREATE TABLE IF NOT EXISTS staff_members (
    staff_id INT AUTO_INCREMENT PRIMARY KEY,
    provider_id INT NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    position ENUM('director', 'teacher', 'assistant_teacher', 'caregiver', 'cook', 'cleaner', 'security', 'nurse', 'admin', 'other') NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20) NOT NULL,
    qualifications TEXT,
    hire_date DATE NOT NULL,
    salary DECIMAL(10,2),
    emergency_contact VARCHAR(100),
    emergency_phone VARCHAR(20),
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (provider_id) REFERENCES providers(provider_id) ON DELETE CASCADE,
    INDEX idx_provider_status (provider_id, status),
    INDEX idx_position (position),
    INDEX idx_hire_date (hire_date)
);

-- Enrolled Children Table
CREATE TABLE IF NOT EXISTS enrolled_children (
    child_id INT AUTO_INCREMENT PRIMARY KEY,
    provider_id INT NOT NULL,
    child_name VARCHAR(100) NOT NULL,
    date_of_birth DATE NOT NULL,
    parent_name VARCHAR(100) NOT NULL,
    parent_phone VARCHAR(20) NOT NULL,
    parent_email VARCHAR(100),
    emergency_contact VARCHAR(100),
    emergency_phone VARCHAR(20),
    medical_info TEXT,
    allergies TEXT,
    enrollment_date DATE NOT NULL,
    status ENUM('active', 'inactive', 'graduated') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (provider_id) REFERENCES providers(provider_id) ON DELETE CASCADE,
    INDEX idx_provider_status (provider_id, status),
    INDEX idx_enrollment_date (enrollment_date),
    INDEX idx_parent_phone (parent_phone)
);

-- Communications Table
CREATE TABLE IF NOT EXISTS communications (
    communication_id INT AUTO_INCREMENT PRIMARY KEY,
    provider_id INT NOT NULL,
    recipient_type ENUM('all_parents', 'all_staff', 'specific_parent', 'specific_staff') NOT NULL,
    recipient_id INT NULL, -- For specific parent or staff member
    message_title VARCHAR(200) NOT NULL,
    message_content TEXT NOT NULL,
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('draft', 'sent', 'delivered', 'read') DEFAULT 'sent',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (provider_id) REFERENCES providers(provider_id) ON DELETE CASCADE,
    INDEX idx_provider_sent (provider_id, sent_at),
    INDEX idx_recipient_type (recipient_type),
    INDEX idx_priority (priority),
    INDEX idx_status (status)
);

-- Attendance Records Table
CREATE TABLE IF NOT EXISTS attendance_records (
    attendance_id INT AUTO_INCREMENT PRIMARY KEY,
    child_id INT NOT NULL,
    provider_id INT NOT NULL,
    attendance_date DATE NOT NULL,
    check_in_time TIME,
    check_out_time TIME,
    status ENUM('present', 'absent', 'late', 'early_pickup') DEFAULT 'present',
    notes TEXT,
    recorded_by INT, -- staff member who recorded
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (child_id) REFERENCES enrolled_children(child_id) ON DELETE CASCADE,
    FOREIGN KEY (provider_id) REFERENCES providers(provider_id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES staff_members(staff_id) ON DELETE SET NULL,
    UNIQUE KEY unique_child_date (child_id, attendance_date),
    INDEX idx_provider_date (provider_id, attendance_date),
    INDEX idx_status (status)
);

-- Incident Reports Table
CREATE TABLE IF NOT EXISTS incident_reports (
    incident_id INT AUTO_INCREMENT PRIMARY KEY,
    provider_id INT NOT NULL,
    child_id INT,
    staff_id INT,
    incident_type ENUM('injury', 'illness', 'behavioral', 'accident', 'other') NOT NULL,
    incident_date DATE NOT NULL,
    incident_time TIME NOT NULL,
    description TEXT NOT NULL,
    action_taken TEXT,
    parent_notified BOOLEAN DEFAULT FALSE,
    notification_time TIMESTAMP NULL,
    severity ENUM('minor', 'moderate', 'serious') DEFAULT 'minor',
    status ENUM('open', 'resolved', 'follow_up_required') DEFAULT 'open',
    reported_by INT NOT NULL, -- staff member who reported
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (provider_id) REFERENCES providers(provider_id) ON DELETE CASCADE,
    FOREIGN KEY (child_id) REFERENCES enrolled_children(child_id) ON DELETE SET NULL,
    FOREIGN KEY (staff_id) REFERENCES staff_members(staff_id) ON DELETE SET NULL,
    FOREIGN KEY (reported_by) REFERENCES staff_members(staff_id) ON DELETE CASCADE,
    INDEX idx_provider_date (provider_id, incident_date),
    INDEX idx_child_date (child_id, incident_date),
    INDEX idx_type_severity (incident_type, severity),
    INDEX idx_status (status)
);

-- Activity Records Table
CREATE TABLE IF NOT EXISTS activity_records (
    activity_id INT AUTO_INCREMENT PRIMARY KEY,
    provider_id INT NOT NULL,
    activity_type ENUM('play', 'meal', 'nap', 'learning', 'outdoor', 'creative', 'other') NOT NULL,
    activity_name VARCHAR(100) NOT NULL,
    activity_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME,
    description TEXT,
    children_participated INT DEFAULT 0,
    staff_involved TEXT, -- JSON array of staff IDs
    notes TEXT,
    created_by INT NOT NULL, -- staff member who created
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (provider_id) REFERENCES providers(provider_id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES staff_members(staff_id) ON DELETE CASCADE,
    INDEX idx_provider_date (provider_id, activity_date),
    INDEX idx_activity_type (activity_type),
    INDEX idx_created_by (created_by)
);

-- Provider Settings Table
CREATE TABLE IF NOT EXISTS provider_settings (
    setting_id INT AUTO_INCREMENT PRIMARY KEY,
    provider_id INT NOT NULL,
    setting_key VARCHAR(50) NOT NULL,
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (provider_id) REFERENCES providers(provider_id) ON DELETE CASCADE,
    UNIQUE KEY unique_provider_setting (provider_id, setting_key),
    INDEX idx_provider_key (provider_id, setting_key)
);
