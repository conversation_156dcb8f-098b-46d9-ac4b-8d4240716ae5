<?php
require_once 'config/database.php';

echo "<h2>Fixing Existing Bookings Payment Status</h2>";

try {
    // Update all existing bookings that have null or incorrect payment fields
    echo "<p>Updating bookings with missing payment status...</p>";
    
    $update_stmt = $pdo->prepare("
        UPDATE bookings 
        SET payment_status = 'unpaid', 
            paid_amount = 0.00, 
            remaining_amount = total_cost 
        WHERE payment_status IS NULL 
           OR payment_status = '' 
           OR (paid_amount = 0 AND remaining_amount = 0 AND payment_status != 'paid')
    ");
    
    $update_stmt->execute();
    $updated_count = $update_stmt->rowCount();
    
    echo "<p style='color: green;'>✓ Updated {$updated_count} bookings with correct payment status!</p>";
    
    // Show current booking payment status
    echo "<h3>Current Booking Payment Status:</h3>";
    $check_stmt = $pdo->query("
        SELECT booking_id, child_name, total_cost, payment_status, paid_amount, remaining_amount, status
        FROM bookings 
        ORDER BY booking_id DESC 
        LIMIT 10
    ");
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'>
            <th>Booking ID</th>
            <th>Child Name</th>
            <th>Total Cost</th>
            <th>Payment Status</th>
            <th>Paid Amount</th>
            <th>Remaining</th>
            <th>Booking Status</th>
          </tr>";
    
    while ($booking = $check_stmt->fetch()) {
        echo "<tr>";
        echo "<td>#{$booking['booking_id']}</td>";
        echo "<td>{$booking['child_name']}</td>";
        echo "<td>LKR " . number_format($booking['total_cost'], 2) . "</td>";
        echo "<td style='color: " . ($booking['payment_status'] === 'paid' ? 'green' : ($booking['payment_status'] === 'partial' ? 'orange' : 'red')) . ";'>";
        echo ucfirst($booking['payment_status']) . "</td>";
        echo "<td>LKR " . number_format($booking['paid_amount'], 2) . "</td>";
        echo "<td>LKR " . number_format($booking['remaining_amount'], 2) . "</td>";
        echo "<td>" . ucfirst($booking['status']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p style='color: green; font-weight: bold; margin-top: 20px;'>✅ Payment status fix completed successfully!</p>";
    echo "<p><a href='Parent/payment_confirmation.php?booking_id=20' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Payment Page</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
