<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a daycare provider
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'provider') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $provider_id = $_SESSION['user_id'];
    $action = $input['action'] ?? '';
    
    if ($action === 'send_reminder' && isset($input['booking_id'])) {
        // Send reminder for specific booking
        $booking_id = $input['booking_id'];
        
        // Get booking and parent details
        $stmt = $pdo->prepare("
            SELECT
                b.*,
                p.name as parent_name,
                p.email as parent_email,
                p.phone as parent_phone,
                pr.business_name,
                b.total_cost as outstanding_amount
            FROM bookings b
            JOIN parents p ON b.parent_id = p.parent_id
            JOIN providers pr ON b.provider_id = pr.provider_id
            WHERE b.booking_id = ? AND b.provider_id = ?
            AND b.status IN ('confirmed', 'completed')
            AND b.payment_status = 'pending'
        ");
        $stmt->execute([$booking_id, $provider_id]);
        $booking = $stmt->fetch();
        
        if (!$booking) {
            throw new Exception('Booking not found or not eligible for reminder');
        }
        
        // Create notification for parent
        $notification_stmt = $pdo->prepare("
            INSERT INTO notifications (user_id, user_type, title, message, type, created_at)
            VALUES (?, 'parent', ?, ?, 'payment_reminder', NOW())
        ");
        
        $title = "Payment Reminder - {$booking['business_name']}";
        $message = "Dear {$booking['parent_name']}, you have an outstanding payment of Rs. " . 
                  number_format($booking['outstanding_amount'], 2) . 
                  " for {$booking['child_name']}'s daycare booking on " . 
                  date('M d, Y', strtotime($booking['booking_date'])) . 
                  ". Please complete your payment at your earliest convenience.";
        
        $notification_stmt->execute([$booking['parent_id'], $title, $message]);
        
        // You could also send email here if email functionality is implemented
        // sendPaymentReminderEmail($booking);
        
        echo json_encode([
            'success' => true, 
            'message' => 'Payment reminder sent successfully',
            'booking_id' => $booking_id
        ]);
        
    } elseif ($action === 'send_all_reminders') {
        // Send reminders to all parents with outstanding payments
        $stmt = $pdo->prepare("
            SELECT
                b.*,
                p.name as parent_name,
                p.email as parent_email,
                p.phone as parent_phone,
                pr.business_name,
                b.total_cost as outstanding_amount
            FROM bookings b
            JOIN parents p ON b.parent_id = p.parent_id
            JOIN providers pr ON b.provider_id = pr.provider_id
            WHERE b.provider_id = ?
            AND b.status IN ('confirmed', 'completed')
            AND b.payment_status = 'pending'
            ORDER BY b.booking_date ASC
        ");
        $stmt->execute([$provider_id]);
        $outstanding_bookings = $stmt->fetchAll();
        
        if (empty($outstanding_bookings)) {
            echo json_encode([
                'success' => true, 
                'message' => 'No outstanding payments found',
                'count' => 0
            ]);
            exit();
        }
        
        $notification_stmt = $pdo->prepare("
            INSERT INTO notifications (user_id, user_type, title, message, type, created_at)
            VALUES (?, 'parent', ?, ?, 'payment_reminder', NOW())
        ");
        
        $count = 0;
        foreach ($outstanding_bookings as $booking) {
            $title = "Payment Reminder - {$booking['business_name']}";
            $message = "Dear {$booking['parent_name']}, you have an outstanding payment of Rs. " . 
                      number_format($booking['outstanding_amount'], 2) . 
                      " for {$booking['child_name']}'s daycare booking on " . 
                      date('M d, Y', strtotime($booking['booking_date'])) . 
                      ". Please complete your payment at your earliest convenience.";
            
            $notification_stmt->execute([$booking['parent_id'], $title, $message]);
            $count++;
        }
        
        echo json_encode([
            'success' => true, 
            'message' => "Payment reminders sent to {$count} parents",
            'count' => $count
        ]);
        
    } else {
        throw new Exception('Invalid action or missing parameters');
    }
    
} catch (Exception $e) {
    error_log("Payment reminder error: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => $e->getMessage()
    ]);
}
?>
