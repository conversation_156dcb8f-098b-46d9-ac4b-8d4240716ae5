-- Database Update Script: Add Mandatory NCPA/MOH Certificates
-- Run this script to update existing database with mandatory certificate requirements

USE nestlings_db;

-- Step 1: Update provider_documents table to include NCPA and MOH certificate types
ALTER TABLE provider_documents 
MODIFY COLUMN document_type ENUM(
    'business_license', 
    'childcare_license', 
    'insurance', 
    'safety_certificate', 
    'staff_qualifications', 
    'health_certificate', 
    'fire_safety', 
    'ncpa_certificate',
    'moh_certificate',
    'other'
) NOT NULL;

-- Step 2: Add mandatory_certificate column to providers table
ALTER TABLE providers 
ADD COLUMN mandatory_certificate_type ENUM('ncpa', 'moh') NULL AFTER verification_status,
ADD COLUMN mandatory_certificate_number VARCHAR(100) NULL AFTER mandatory_certificate_type,
ADD COLUMN mandatory_certificate_expiry DATE NULL AFTER mandatory_certificate_number;

-- Step 3: Create indexes for better performance
CREATE INDEX idx_providers_mandatory_cert ON providers(mandatory_certificate_type);
CREATE INDEX idx_documents_mandatory ON provider_documents(document_type, verification_status);

-- Step 4: Update existing sample providers with mandatory certificates
-- (This is for testing purposes - in real scenario, providers would upload these during registration)

-- Update Little Stars Daycare with NCPA certificate
UPDATE providers 
SET mandatory_certificate_type = 'ncpa', 
    mandatory_certificate_number = 'NCPA/2024/001', 
    mandatory_certificate_expiry = '2025-12-31' 
WHERE email = '<EMAIL>';

-- Update Sunshine Kids Center with MOH certificate
UPDATE providers 
SET mandatory_certificate_type = 'moh', 
    mandatory_certificate_number = 'MOH/CC/2024/002', 
    mandatory_certificate_expiry = '2025-06-30' 
WHERE email = '<EMAIL>';

-- Update Rainbow Childcare with NCPA certificate
UPDATE providers 
SET mandatory_certificate_type = 'ncpa', 
    mandatory_certificate_number = 'NCPA/2024/003', 
    mandatory_certificate_expiry = '2025-08-31' 
WHERE email = '<EMAIL>';

-- Update Happy Hearts Nursery with MOH certificate
UPDATE providers 
SET mandatory_certificate_type = 'moh', 
    mandatory_certificate_number = 'MOH/CC/2024/004', 
    mandatory_certificate_expiry = '2025-11-30' 
WHERE email = '<EMAIL>';

-- Update Bright Beginnings with NCPA certificate
UPDATE providers 
SET mandatory_certificate_type = 'ncpa', 
    mandatory_certificate_number = 'NCPA/2024/005', 
    mandatory_certificate_expiry = '2025-09-30' 
WHERE email = '<EMAIL>';

-- Update Tiny Tots Academy with MOH certificate
UPDATE providers 
SET mandatory_certificate_type = 'moh', 
    mandatory_certificate_number = 'MOH/CC/2024/006', 
    mandatory_certificate_expiry = '2025-07-31' 
WHERE email = '<EMAIL>';

-- Update Caring Hands Daycare with NCPA certificate
UPDATE providers 
SET mandatory_certificate_type = 'ncpa', 
    mandatory_certificate_number = 'NCPA/2024/007', 
    mandatory_certificate_expiry = '2025-10-31' 
WHERE email = '<EMAIL>';

-- Update Garden Grove Kids with MOH certificate
UPDATE providers 
SET mandatory_certificate_type = 'moh', 
    mandatory_certificate_number = 'MOH/CC/2024/008', 
    mandatory_certificate_expiry = '2025-05-31' 
WHERE email = '<EMAIL>';

-- Update Little Explorers with NCPA certificate
UPDATE providers 
SET mandatory_certificate_type = 'ncpa', 
    mandatory_certificate_number = 'NCPA/2024/009', 
    mandatory_certificate_expiry = '2025-12-15' 
WHERE email = '<EMAIL>';

-- Update Precious Moments with MOH certificate
UPDATE providers 
SET mandatory_certificate_type = 'moh', 
    mandatory_certificate_number = 'MOH/CC/2024/010', 
    mandatory_certificate_expiry = '2025-08-15' 
WHERE email = '<EMAIL>';

-- Step 5: Add sample mandatory certificate documents
INSERT INTO provider_documents (provider_id, document_type, document_name, file_path, expiry_date, verification_status, verified_at) VALUES
-- NCPA Certificates
(1, 'ncpa_certificate', 'NCPA Child Protection Certificate 2024', 'sample_ncpa_cert_1.pdf', '2025-12-31', 'approved', '2024-01-10 10:00:00'),
(3, 'ncpa_certificate', 'NCPA Registration Certificate', 'sample_ncpa_cert_3.pdf', '2025-08-31', 'approved', '2024-01-12 11:15:00'),
(5, 'ncpa_certificate', 'NCPA Compliance Certificate', 'sample_ncpa_cert_5.pdf', '2025-09-30', 'approved', '2024-01-14 16:45:00'),
(7, 'ncpa_certificate', 'NCPA Special Care Authorization', 'sample_ncpa_cert_7.pdf', '2025-10-31', 'approved', '2024-01-16 12:00:00'),
(9, 'ncpa_certificate', 'NCPA Outdoor Activity Approval', 'sample_ncpa_cert_9.pdf', '2025-12-15', 'approved', '2024-01-18 10:45:00'),

-- MOH Certificates
(2, 'moh_certificate', 'MOH Health & Safety Certificate', 'sample_moh_cert_2.pdf', '2025-06-30', 'approved', '2024-01-11 09:30:00'),
(4, 'moh_certificate', 'MOH Food Safety Certification', 'sample_moh_cert_4.pdf', '2025-11-30', 'approved', '2024-01-13 14:20:00'),
(6, 'moh_certificate', 'MOH Childcare Facility License', 'sample_moh_cert_6.pdf', '2025-07-31', 'approved', '2024-01-15 08:30:00'),
(8, 'moh_certificate', 'MOH Organic Food Handling License', 'sample_moh_cert_8.pdf', '2025-05-31', 'approved', '2024-01-17 15:30:00'),
(10, 'moh_certificate', 'MOH Premium Facility Certificate', 'sample_moh_cert_10.pdf', '2025-08-15', 'approved', '2024-01-19 13:15:00');

-- Step 6: Create a view for easy certificate status checking
CREATE VIEW provider_certificate_status AS
SELECT 
    p.provider_id,
    p.business_name,
    p.email,
    p.verification_status,
    p.mandatory_certificate_type,
    p.mandatory_certificate_number,
    p.mandatory_certificate_expiry,
    CASE 
        WHEN p.mandatory_certificate_expiry < CURDATE() THEN 'Expired'
        WHEN p.mandatory_certificate_expiry < DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN 'Expiring Soon'
        ELSE 'Valid'
    END as certificate_status,
    pd.verification_status as document_verification_status
FROM providers p
LEFT JOIN provider_documents pd ON p.provider_id = pd.provider_id 
    AND pd.document_type IN ('ncpa_certificate', 'moh_certificate')
WHERE p.is_active = 1;

-- Step 7: Update system settings to reflect new requirements
INSERT INTO system_settings (setting_key, setting_value, description) VALUES
('mandatory_certificates', '1', 'Require NCPA or MOH certificates for provider registration (1=yes, 0=no)'),
('certificate_expiry_warning_days', '30', 'Days before certificate expiry to show warning'),
('ncpa_info', 'National Child Protection Authority certificate is required for all childcare providers', 'NCPA certificate information'),
('moh_info', 'Ministry of Health certificate is required for childcare facilities providing food services', 'MOH certificate information');

-- Verification: Check the updates
SELECT 'Database update completed successfully!' as status;
SELECT COUNT(*) as total_providers_with_certificates FROM providers WHERE mandatory_certificate_type IS NOT NULL;
SELECT COUNT(*) as total_certificate_documents FROM provider_documents WHERE document_type IN ('ncpa_certificate', 'moh_certificate');
