<?php
session_start();

// Check if user is logged in and is a daycare provider
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'provider') {
    header("Location: ../login.php");
    exit();
}

require_once '../config/database.php';

// Get provider information
$stmt = $pdo->prepare("SELECT * FROM providers WHERE provider_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$provider = $stmt->fetch();

if (!$provider) {
    session_destroy();
    header("Location: ../login.php");
    exit();
}

$success_message = '';
$error_message = '';

// Handle staff member addition
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_staff'])) {
    $full_name = trim($_POST['full_name']);
    $position = trim($_POST['position']);
    $email = trim($_POST['email']);
    $phone = trim($_POST['phone']);
    $qualifications = trim($_POST['qualifications']);
    $hire_date = $_POST['hire_date'];
    $salary = $_POST['salary'] ?: null;
    $emergency_contact = trim($_POST['emergency_contact']);
    $emergency_phone = trim($_POST['emergency_phone']);
    
    if (empty($full_name) || empty($position) || empty($email) || empty($phone) || empty($hire_date)) {
        $error_message = "Please fill in all required fields.";
    } else {
        try {
            // Create staff_members table if it doesn't exist
            $pdo->exec("CREATE TABLE IF NOT EXISTS staff_members (
                staff_id INT AUTO_INCREMENT PRIMARY KEY,
                provider_id INT NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                position VARCHAR(50) NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                phone VARCHAR(20) NOT NULL,
                qualifications TEXT,
                hire_date DATE NOT NULL,
                salary DECIMAL(10,2),
                emergency_contact VARCHAR(100),
                emergency_phone VARCHAR(20),
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )");
            
            $stmt = $pdo->prepare("
                INSERT INTO staff_members (provider_id, full_name, position, email, phone, qualifications, hire_date, salary, emergency_contact, emergency_phone, status, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW())
            ");
            $stmt->execute([$_SESSION['user_id'], $full_name, $position, $email, $phone, $qualifications, $hire_date, $salary, $emergency_contact, $emergency_phone]);
            
            $success_message = "Staff member added successfully!";
        } catch (PDOException $e) {
            if ($e->getCode() == 23000) {
                $error_message = "Email address already exists.";
            } else {
                $error_message = "Database error. Please try again.";
            }
        }
    }
}

// Handle child enrollment
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_child'])) {
    $child_name = trim($_POST['child_name']);
    $date_of_birth = $_POST['date_of_birth'];
    $parent_name = trim($_POST['parent_name']);
    $parent_phone = trim($_POST['parent_phone']);
    $parent_email = trim($_POST['parent_email']);
    $emergency_contact = trim($_POST['emergency_contact']);
    $emergency_phone = trim($_POST['emergency_phone']);
    $medical_info = trim($_POST['medical_info']);
    $allergies = trim($_POST['allergies']);
    
    if (empty($child_name) || empty($date_of_birth) || empty($parent_name) || empty($parent_phone)) {
        $error_message = "Please fill in all required fields for child enrollment.";
    } else {
        try {
            // Create enrolled_children table if it doesn't exist
            $pdo->exec("CREATE TABLE IF NOT EXISTS enrolled_children (
                child_id INT AUTO_INCREMENT PRIMARY KEY,
                provider_id INT NOT NULL,
                child_name VARCHAR(100) NOT NULL,
                date_of_birth DATE NOT NULL,
                parent_name VARCHAR(100) NOT NULL,
                parent_phone VARCHAR(20) NOT NULL,
                parent_email VARCHAR(100),
                emergency_contact VARCHAR(100),
                emergency_phone VARCHAR(20),
                medical_info TEXT,
                allergies TEXT,
                enrollment_date DATE NOT NULL,
                status ENUM('active', 'inactive', 'graduated') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )");
            
            $stmt = $pdo->prepare("
                INSERT INTO enrolled_children (provider_id, child_name, date_of_birth, parent_name, parent_phone, parent_email, emergency_contact, emergency_phone, medical_info, allergies, enrollment_date, status) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), 'active')
            ");
            $stmt->execute([$_SESSION['user_id'], $child_name, $date_of_birth, $parent_name, $parent_phone, $parent_email, $emergency_contact, $emergency_phone, $medical_info, $allergies]);
            
            $success_message = "Child enrolled successfully!";
        } catch (PDOException $e) {
            $error_message = "Failed to enroll child. Please try again.";
        }
    }
}

// Handle communication message
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_message'])) {
    $recipient_type = $_POST['recipient_type'];
    $message_title = trim($_POST['message_title']);
    $message_content = trim($_POST['message_content']);
    $priority = $_POST['priority'];
    
    if (empty($message_title) || empty($message_content)) {
        $error_message = "Please fill in message title and content.";
    } else {
        try {
            // Create communications table if it doesn't exist
            $pdo->exec("CREATE TABLE IF NOT EXISTS communications (
                communication_id INT AUTO_INCREMENT PRIMARY KEY,
                provider_id INT NOT NULL,
                recipient_type VARCHAR(50) NOT NULL,
                message_title VARCHAR(200) NOT NULL,
                message_content TEXT NOT NULL,
                priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
                sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status ENUM('draft', 'sent', 'delivered', 'read') DEFAULT 'sent',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )");
            
            $stmt = $pdo->prepare("
                INSERT INTO communications (provider_id, recipient_type, message_title, message_content, priority, sent_at, status) 
                VALUES (?, ?, ?, ?, ?, NOW(), 'sent')
            ");
            $stmt->execute([$_SESSION['user_id'], $recipient_type, $message_title, $message_content, $priority]);
            
            $success_message = "Message sent successfully!";
        } catch (PDOException $e) {
            $error_message = "Failed to send message. Please try again.";
        }
    }
}

// Get staff members (with error handling)
try {
    $stmt = $pdo->prepare("SELECT * FROM staff_members WHERE provider_id = ? ORDER BY status DESC, hire_date DESC");
    $stmt->execute([$_SESSION['user_id']]);
    $staff_members = $stmt->fetchAll();
} catch (PDOException $e) {
    $staff_members = [];
}

// Get enrolled children (with error handling)
try {
    $stmt = $pdo->prepare("SELECT * FROM enrolled_children WHERE provider_id = ? ORDER BY enrollment_date DESC");
    $stmt->execute([$_SESSION['user_id']]);
    $enrolled_children = $stmt->fetchAll();
} catch (PDOException $e) {
    $enrolled_children = [];
}

// Get recent communications (with error handling)
try {
    $stmt = $pdo->prepare("SELECT * FROM communications WHERE provider_id = ? ORDER BY sent_at DESC LIMIT 10");
    $stmt->execute([$_SESSION['user_id']]);
    $recent_communications = $stmt->fetchAll();
} catch (PDOException $e) {
    $recent_communications = [];
}

// Get statistics (with error handling)
try {
    $stmt = $pdo->prepare("
        SELECT 
            (SELECT COUNT(*) FROM staff_members WHERE provider_id = ? AND status = 'active') as active_staff,
            (SELECT COUNT(*) FROM enrolled_children WHERE provider_id = ? AND status = 'active') as active_children,
            (SELECT COUNT(*) FROM communications WHERE provider_id = ? AND sent_at >= DATE_SUB(NOW(), INTERVAL 7 DAYS)) as weekly_messages,
            (SELECT COUNT(*) FROM bookings WHERE provider_id = ? AND booking_date >= CURDATE()) as upcoming_bookings
    ");
    $stmt->execute([$_SESSION['user_id'], $_SESSION['user_id'], $_SESSION['user_id'], $_SESSION['user_id']]);
    $stats = $stmt->fetch();
} catch (PDOException $e) {
    $stats = ['active_staff' => 0, 'active_children' => 0, 'weekly_messages' => 0, 'upcoming_bookings' => 0];
}

// Position options
$positions = [
    'director' => 'Director',
    'teacher' => 'Teacher',
    'assistant_teacher' => 'Assistant Teacher',
    'caregiver' => 'Caregiver',
    'cook' => 'Cook',
    'cleaner' => 'Cleaner',
    'security' => 'Security',
    'nurse' => 'Nurse',
    'admin' => 'Administrative Staff',
    'other' => 'Other'
];
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>Management Center - Nestlings</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="" name="keywords">
    <meta content="" name="description">

    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600&family=Nunito:wght@600;700;800&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">
    <link href="../lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">

    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">

    <style>
        .content-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: none;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .content-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .stats-grid {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .stat-item {
            text-align: center;
            padding: 20px;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 5px;
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .stat-icon {
            font-size: 3rem;
            opacity: 0.3;
            margin-bottom: 15px;
        }

        .management-tabs {
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 30px;
        }

        .management-tabs .nav-link {
            border: none;
            border-bottom: 3px solid transparent;
            color: #6c757d;
            font-weight: 500;
            padding: 15px 25px;
            transition: all 0.3s ease;
        }

        .management-tabs .nav-link.active {
            color: #4A90E2;
            border-bottom-color: #4A90E2;
            background: none;
        }

        .management-tabs .nav-link:hover {
            color: #4A90E2;
            border-bottom-color: rgba(74, 144, 226, 0.3);
        }

        .staff-card, .child-card, .message-card {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .staff-card:hover, .child-card:hover, .message-card:hover {
            background: rgba(74, 144, 226, 0.05);
            border-color: #4A90E2;
            transform: translateY(-2px);
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-active {
            background: #d1edff;
            color: #0c5460;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .priority-high {
            background: #f8d7da;
            color: #721c24;
        }

        .priority-medium {
            background: #fff3cd;
            color: #856404;
        }

        .priority-low {
            background: #d1edff;
            color: #0c5460;
        }

        .avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4A90E2, #E91E63);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: bold;
        }

        .btn-gradient {
            background: linear-gradient(135deg, #4A90E2, #E91E63);
            border: none;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(74, 144, 226, 0.4);
            color: white;
        }

        .form-floating {
            margin-bottom: 1rem;
        }

        .quick-stats {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .tab-content {
            min-height: 500px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }
    </style>
</head>

<body>
    <!-- Spinner Start -->
    <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>
    <!-- Spinner End -->

    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light shadow sticky-top p-0">
        <a href="dashboard.php" class="navbar-brand d-flex align-items-center px-4 px-lg-5">
            <h2 class="m-0 text-primary"><i class="fa fa-baby me-3"></i>Nestlings</h2>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="dashboard.php" class="nav-item nav-link">Dashboard</a>
                <a href="bookings.php" class="nav-item nav-link">Bookings</a>
                <a href="profile.php" class="nav-item nav-link">Profile</a>
                <a href="analytics.php" class="nav-item nav-link">Analytics</a>
                <a href="management.php" class="nav-item nav-link active">Management</a>
                <a href="payments.php" class="nav-item nav-link">Payments</a>
            </div>
            <div class="dropdown">
                <button class="btn btn-primary dropdown-toggle me-4" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fa fa-user me-2"></i>
                    <span><?php echo htmlspecialchars($provider['business_name']); ?></span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="profile.php"><i class="fa fa-building me-2"></i>Business Profile</a></li>
                    <li><a class="dropdown-item" href="settings.php"><i class="fa fa-cog me-2"></i>Settings</a></li>
                    <li><a class="dropdown-item" href="help.php"><i class="fa fa-question-circle me-2"></i>Help</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="../logout.php"><i class="fa fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Header Start -->
    <div class="container-fluid bg-primary py-5 mb-5 page-header">
        <div class="container py-5">
            <div class="row justify-content-center">
                <div class="col-lg-10 text-center">
                    <h1 class="display-3 text-white animated slideInDown">Management Center</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item"><a class="text-white" href="dashboard.php">Dashboard</a></li>
                            <li class="breadcrumb-item text-white active" aria-current="page">Management</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
    <!-- Header End -->

    <!-- Management Center Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <?php if ($success_message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fa fa-check-circle me-2"></i><?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fa fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Statistics Overview -->
            <div class="stats-grid">
                <div class="row">
                    <div class="col-md-3">
                        <div class="stat-item">
                            <i class="fa fa-users stat-icon"></i>
                            <span class="stat-number"><?php echo $stats['active_staff'] ?: 0; ?></span>
                            <div class="stat-label">Active Staff</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <i class="fa fa-child stat-icon"></i>
                            <span class="stat-number"><?php echo $stats['active_children'] ?: 0; ?></span>
                            <div class="stat-label">Enrolled Children</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <i class="fa fa-comments stat-icon"></i>
                            <span class="stat-number"><?php echo $stats['weekly_messages'] ?: 0; ?></span>
                            <div class="stat-label">Messages This Week</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <i class="fa fa-calendar stat-icon"></i>
                            <span class="stat-number"><?php echo $stats['upcoming_bookings'] ?: 0; ?></span>
                            <div class="stat-label">Upcoming Bookings</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Management Tabs -->
            <ul class="nav nav-tabs management-tabs" id="managementTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="staff-tab" data-bs-toggle="tab" data-bs-target="#staff" type="button" role="tab">
                        <i class="fa fa-users me-2"></i>Staff Management
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="children-tab" data-bs-toggle="tab" data-bs-target="#children" type="button" role="tab">
                        <i class="fa fa-child me-2"></i>Child Management
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="communication-tab" data-bs-toggle="tab" data-bs-target="#communication" type="button" role="tab">
                        <i class="fa fa-comments me-2"></i>Communication Center
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="reports-tab" data-bs-toggle="tab" data-bs-target="#reports" type="button" role="tab">
                        <i class="fa fa-chart-bar me-2"></i>Reports & Insights
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab">
                        <i class="fa fa-cog me-2"></i>Settings
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="managementTabContent">
                <!-- Staff Management Tab -->
                <div class="tab-pane fade show active" id="staff" role="tabpanel">
                    <div class="row g-4">
                        <!-- Add New Staff -->
                        <div class="col-lg-4">
                            <div class="content-card card h-100">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fa fa-user-plus me-2"></i>Add New Staff</h5>
                                </div>
                                <div class="card-body">
                                    <form method="POST">
                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="full_name" name="full_name" placeholder="Full Name" required>
                                            <label for="full_name">Full Name *</label>
                                        </div>

                                        <div class="form-floating mb-3">
                                            <select class="form-control" id="position" name="position" required>
                                                <option value="">Select Position</option>
                                                <?php foreach ($positions as $value => $label): ?>
                                                    <option value="<?php echo $value; ?>"><?php echo $label; ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                            <label for="position">Position *</label>
                                        </div>

                                        <div class="form-floating mb-3">
                                            <input type="email" class="form-control" id="email" name="email" placeholder="Email" required>
                                            <label for="email">Email Address *</label>
                                        </div>

                                        <div class="form-floating mb-3">
                                            <input type="tel" class="form-control" id="phone" name="phone" placeholder="Phone" required>
                                            <label for="phone">Phone Number *</label>
                                        </div>

                                        <div class="form-floating mb-3">
                                            <textarea class="form-control" id="qualifications" name="qualifications" placeholder="Qualifications" style="height: 80px;"></textarea>
                                            <label for="qualifications">Qualifications</label>
                                        </div>

                                        <div class="form-floating mb-3">
                                            <input type="date" class="form-control" id="hire_date" name="hire_date" required>
                                            <label for="hire_date">Hire Date *</label>
                                        </div>

                                        <div class="form-floating mb-3">
                                            <input type="number" class="form-control" id="salary" name="salary" placeholder="Salary" min="0" step="0.01">
                                            <label for="salary">Monthly Salary (Rs.)</label>
                                        </div>

                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="emergency_contact" name="emergency_contact" placeholder="Emergency Contact">
                                            <label for="emergency_contact">Emergency Contact Name</label>
                                        </div>

                                        <div class="form-floating mb-3">
                                            <input type="tel" class="form-control" id="emergency_phone" name="emergency_phone" placeholder="Emergency Phone">
                                            <label for="emergency_phone">Emergency Phone</label>
                                        </div>

                                        <button type="submit" name="add_staff" class="btn btn-gradient w-100">
                                            <i class="fa fa-plus me-2"></i>Add Staff Member
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Staff List -->
                        <div class="col-lg-8">
                            <div class="content-card card h-100">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0"><i class="fa fa-users me-2"></i>Staff Members</h5>
                                    <span class="badge bg-primary"><?php echo count($staff_members); ?> Members</span>
                                </div>
                                <div class="card-body">
                                    <?php if (empty($staff_members)): ?>
                                        <div class="empty-state">
                                            <i class="fa fa-users"></i>
                                            <h5>No staff members yet</h5>
                                            <p>Add your first staff member to get started with team management</p>
                                        </div>
                                    <?php else: ?>
                                        <div class="staff-list">
                                            <?php foreach ($staff_members as $staff): ?>
                                            <div class="staff-card">
                                                <div class="row align-items-center">
                                                    <div class="col-auto">
                                                        <div class="avatar">
                                                            <?php echo strtoupper(substr($staff['full_name'], 0, 1)); ?>
                                                        </div>
                                                    </div>
                                                    <div class="col">
                                                        <div class="d-flex justify-content-between align-items-start">
                                                            <div>
                                                                <h6 class="mb-1"><?php echo htmlspecialchars($staff['full_name']); ?></h6>
                                                                <p class="text-muted mb-1"><?php echo htmlspecialchars($positions[$staff['position']] ?? $staff['position']); ?></p>
                                                                <small class="text-muted">
                                                                    <i class="fa fa-envelope me-1"></i><?php echo htmlspecialchars($staff['email']); ?>
                                                                    <span class="mx-2">|</span>
                                                                    <i class="fa fa-phone me-1"></i><?php echo htmlspecialchars($staff['phone']); ?>
                                                                </small>
                                                            </div>
                                                            <div class="text-end">
                                                                <span class="status-badge status-<?php echo $staff['status']; ?>">
                                                                    <?php echo ucfirst($staff['status']); ?>
                                                                </span>
                                                                <div class="mt-2">
                                                                    <button class="btn btn-sm btn-outline-primary me-1">
                                                                        <i class="fa fa-edit"></i>
                                                                    </button>
                                                                    <button class="btn btn-sm btn-outline-danger">
                                                                        <i class="fa fa-trash"></i>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <?php if (!empty($staff['qualifications'])): ?>
                                                        <div class="mt-2">
                                                            <small class="text-muted">
                                                                <i class="fa fa-graduation-cap me-1"></i>
                                                                <?php echo htmlspecialchars($staff['qualifications']); ?>
                                                            </small>
                                                        </div>
                                                        <?php endif; ?>
                                                        <div class="mt-2">
                                                            <small class="text-muted">
                                                                <i class="fa fa-calendar me-1"></i>
                                                                Hired: <?php echo date('M d, Y', strtotime($staff['hire_date'])); ?>
                                                                <?php if ($staff['salary']): ?>
                                                                    <span class="mx-2">|</span>
                                                                    <i class="fa fa-money-bill me-1"></i>
                                                                    Rs. <?php echo number_format($staff['salary']); ?>/month
                                                                <?php endif; ?>
                                                            </small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Child Management Tab -->
                <div class="tab-pane fade" id="children" role="tabpanel">
                    <div class="row g-4">
                        <!-- Add New Child -->
                        <div class="col-lg-4">
                            <div class="content-card card h-100">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fa fa-child me-2"></i>Enroll New Child</h5>
                                </div>
                                <div class="card-body">
                                    <form method="POST">
                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="child_name" name="child_name" placeholder="Child Name" required>
                                            <label for="child_name">Child's Full Name *</label>
                                        </div>

                                        <div class="form-floating mb-3">
                                            <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" required>
                                            <label for="date_of_birth">Date of Birth *</label>
                                        </div>

                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="parent_name" name="parent_name" placeholder="Parent Name" required>
                                            <label for="parent_name">Parent/Guardian Name *</label>
                                        </div>

                                        <div class="form-floating mb-3">
                                            <input type="tel" class="form-control" id="parent_phone" name="parent_phone" placeholder="Parent Phone" required>
                                            <label for="parent_phone">Parent Phone *</label>
                                        </div>

                                        <div class="form-floating mb-3">
                                            <input type="email" class="form-control" id="parent_email" name="parent_email" placeholder="Parent Email">
                                            <label for="parent_email">Parent Email</label>
                                        </div>

                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="emergency_contact" name="emergency_contact" placeholder="Emergency Contact">
                                            <label for="emergency_contact">Emergency Contact Name</label>
                                        </div>

                                        <div class="form-floating mb-3">
                                            <input type="tel" class="form-control" id="emergency_phone" name="emergency_phone" placeholder="Emergency Phone">
                                            <label for="emergency_phone">Emergency Phone</label>
                                        </div>

                                        <div class="form-floating mb-3">
                                            <textarea class="form-control" id="medical_info" name="medical_info" placeholder="Medical Information" style="height: 80px;"></textarea>
                                            <label for="medical_info">Medical Information</label>
                                        </div>

                                        <div class="form-floating mb-3">
                                            <textarea class="form-control" id="allergies" name="allergies" placeholder="Allergies" style="height: 60px;"></textarea>
                                            <label for="allergies">Allergies & Special Needs</label>
                                        </div>

                                        <button type="submit" name="add_child" class="btn btn-gradient w-100">
                                            <i class="fa fa-plus me-2"></i>Enroll Child
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Children List -->
                        <div class="col-lg-8">
                            <div class="content-card card h-100">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0"><i class="fa fa-child me-2"></i>Enrolled Children</h5>
                                    <span class="badge bg-success"><?php echo count($enrolled_children); ?> Children</span>
                                </div>
                                <div class="card-body">
                                    <?php if (empty($enrolled_children)): ?>
                                        <div class="empty-state">
                                            <i class="fa fa-child"></i>
                                            <h5>No children enrolled yet</h5>
                                            <p>Start enrolling children to manage their information and care</p>
                                        </div>
                                    <?php else: ?>
                                        <div class="children-list">
                                            <?php foreach ($enrolled_children as $child): ?>
                                            <div class="child-card">
                                                <div class="row align-items-center">
                                                    <div class="col-auto">
                                                        <div class="avatar">
                                                            <?php echo strtoupper(substr($child['child_name'], 0, 1)); ?>
                                                        </div>
                                                    </div>
                                                    <div class="col">
                                                        <div class="d-flex justify-content-between align-items-start">
                                                            <div>
                                                                <h6 class="mb-1"><?php echo htmlspecialchars($child['child_name']); ?></h6>
                                                                <p class="text-muted mb-1">
                                                                    Age: <?php
                                                                        $dob = new DateTime($child['date_of_birth']);
                                                                        $now = new DateTime();
                                                                        $age = $now->diff($dob);
                                                                        echo $age->y . ' years, ' . $age->m . ' months';
                                                                    ?>
                                                                </p>
                                                                <small class="text-muted">
                                                                    <i class="fa fa-user me-1"></i><?php echo htmlspecialchars($child['parent_name']); ?>
                                                                    <span class="mx-2">|</span>
                                                                    <i class="fa fa-phone me-1"></i><?php echo htmlspecialchars($child['parent_phone']); ?>
                                                                </small>
                                                            </div>
                                                            <div class="text-end">
                                                                <span class="status-badge status-<?php echo $child['status']; ?>">
                                                                    <?php echo ucfirst($child['status']); ?>
                                                                </span>
                                                                <div class="mt-2">
                                                                    <button class="btn btn-sm btn-outline-primary me-1">
                                                                        <i class="fa fa-eye"></i>
                                                                    </button>
                                                                    <button class="btn btn-sm btn-outline-info me-1">
                                                                        <i class="fa fa-edit"></i>
                                                                    </button>
                                                                    <button class="btn btn-sm btn-outline-danger">
                                                                        <i class="fa fa-user-times"></i>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="mt-2">
                                                            <small class="text-muted">
                                                                <i class="fa fa-calendar me-1"></i>
                                                                Enrolled: <?php echo date('M d, Y', strtotime($child['enrollment_date'])); ?>
                                                                <?php if (!empty($child['allergies'])): ?>
                                                                    <span class="mx-2">|</span>
                                                                    <i class="fa fa-exclamation-triangle me-1 text-warning"></i>
                                                                    Has allergies
                                                                <?php endif; ?>
                                                            </small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Communication Center Tab -->
                <div class="tab-pane fade" id="communication" role="tabpanel">
                    <div class="row g-4">
                        <!-- Send Message -->
                        <div class="col-lg-4">
                            <div class="content-card card h-100">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fa fa-paper-plane me-2"></i>Send Message</h5>
                                </div>
                                <div class="card-body">
                                    <form method="POST">
                                        <div class="form-floating mb-3">
                                            <select class="form-control" id="recipient_type" name="recipient_type" required>
                                                <option value="">Select Recipients</option>
                                                <option value="all_parents">All Parents</option>
                                                <option value="all_staff">All Staff</option>
                                                <option value="specific_parent">Specific Parent</option>
                                                <option value="specific_staff">Specific Staff</option>
                                            </select>
                                            <label for="recipient_type">Send To *</label>
                                        </div>

                                        <div class="form-floating mb-3">
                                            <select class="form-control" id="priority" name="priority" required>
                                                <option value="low">Low Priority</option>
                                                <option value="medium" selected>Medium Priority</option>
                                                <option value="high">High Priority</option>
                                            </select>
                                            <label for="priority">Priority Level</label>
                                        </div>

                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="message_title" name="message_title" placeholder="Message Title" required>
                                            <label for="message_title">Message Title *</label>
                                        </div>

                                        <div class="form-floating mb-3">
                                            <textarea class="form-control" id="message_content" name="message_content" placeholder="Message Content" style="height: 120px;" required></textarea>
                                            <label for="message_content">Message Content *</label>
                                        </div>

                                        <button type="submit" name="send_message" class="btn btn-gradient w-100">
                                            <i class="fa fa-paper-plane me-2"></i>Send Message
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Messages -->
                        <div class="col-lg-8">
                            <div class="content-card card h-100">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0"><i class="fa fa-comments me-2"></i>Recent Messages</h5>
                                    <span class="badge bg-info"><?php echo count($recent_communications); ?> Messages</span>
                                </div>
                                <div class="card-body">
                                    <?php if (empty($recent_communications)): ?>
                                        <div class="empty-state">
                                            <i class="fa fa-comments"></i>
                                            <h5>No messages sent yet</h5>
                                            <p>Start communicating with parents and staff members</p>
                                        </div>
                                    <?php else: ?>
                                        <div class="messages-list">
                                            <?php foreach ($recent_communications as $message): ?>
                                            <div class="message-card">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div class="flex-grow-1">
                                                        <div class="d-flex align-items-center mb-2">
                                                            <h6 class="mb-0 me-2"><?php echo htmlspecialchars($message['message_title']); ?></h6>
                                                            <span class="status-badge priority-<?php echo $message['priority']; ?>">
                                                                <?php echo ucfirst($message['priority']); ?>
                                                            </span>
                                                        </div>
                                                        <p class="text-muted mb-2"><?php echo htmlspecialchars(substr($message['message_content'], 0, 100)) . (strlen($message['message_content']) > 100 ? '...' : ''); ?></p>
                                                        <small class="text-muted">
                                                            <i class="fa fa-users me-1"></i>
                                                            To: <?php echo ucwords(str_replace('_', ' ', $message['recipient_type'])); ?>
                                                            <span class="mx-2">|</span>
                                                            <i class="fa fa-clock me-1"></i>
                                                            <?php echo date('M d, Y H:i', strtotime($message['sent_at'])); ?>
                                                        </small>
                                                    </div>
                                                    <div class="text-end">
                                                        <button class="btn btn-sm btn-outline-primary me-1">
                                                            <i class="fa fa-eye"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-info">
                                                            <i class="fa fa-reply"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reports & Insights Tab -->
                <div class="tab-pane fade" id="reports" role="tabpanel">
                    <div class="row g-4">
                        <div class="col-lg-6">
                            <div class="content-card card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fa fa-chart-line me-2"></i>Attendance Reports</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <div class="stat-item">
                                                <span class="stat-number text-success">95%</span>
                                                <div class="stat-label">This Week</div>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="stat-item">
                                                <span class="stat-number text-info">92%</span>
                                                <div class="stat-label">This Month</div>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="stat-item">
                                                <span class="stat-number text-primary">89%</span>
                                                <div class="stat-label">Overall</div>
                                            </div>
                                        </div>
                                    </div>
                                    <hr>
                                    <button class="btn btn-outline-primary w-100">
                                        <i class="fa fa-download me-2"></i>Download Detailed Report
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="content-card card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fa fa-exclamation-triangle me-2"></i>Incident Reports</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <div class="stat-item">
                                                <span class="stat-number text-warning">2</span>
                                                <div class="stat-label">This Week</div>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="stat-item">
                                                <span class="stat-number text-danger">5</span>
                                                <div class="stat-label">This Month</div>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="stat-item">
                                                <span class="stat-number text-secondary">12</span>
                                                <div class="stat-label">Total</div>
                                            </div>
                                        </div>
                                    </div>
                                    <hr>
                                    <button class="btn btn-outline-warning w-100">
                                        <i class="fa fa-file-alt me-2"></i>View All Incidents
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-12">
                            <div class="content-card card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fa fa-chart-bar me-2"></i>Activity Summary</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="text-center p-3">
                                                <i class="fa fa-gamepad fa-2x text-primary mb-2"></i>
                                                <h6>Play Activities</h6>
                                                <span class="badge bg-primary">24 Sessions</span>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center p-3">
                                                <i class="fa fa-utensils fa-2x text-success mb-2"></i>
                                                <h6>Meal Times</h6>
                                                <span class="badge bg-success">18 Meals</span>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center p-3">
                                                <i class="fa fa-bed fa-2x text-info mb-2"></i>
                                                <h6>Nap Times</h6>
                                                <span class="badge bg-info">12 Sessions</span>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center p-3">
                                                <i class="fa fa-book fa-2x text-warning mb-2"></i>
                                                <h6>Learning</h6>
                                                <span class="badge bg-warning">15 Sessions</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings Tab -->
                <div class="tab-pane fade" id="settings" role="tabpanel">
                    <div class="row g-4">
                        <div class="col-lg-6">
                            <div class="content-card card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fa fa-clock me-2"></i>Business Hours</h5>
                                </div>
                                <div class="card-body">
                                    <form>
                                        <div class="row">
                                            <div class="col-6">
                                                <div class="form-floating mb-3">
                                                    <input type="time" class="form-control" id="opening_time" value="07:00">
                                                    <label for="opening_time">Opening Time</label>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="form-floating mb-3">
                                                    <input type="time" class="form-control" id="closing_time" value="18:00">
                                                    <label for="closing_time">Closing Time</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Operating Days</label>
                                            <div class="row">
                                                <div class="col-6">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="monday" checked>
                                                        <label class="form-check-label" for="monday">Monday</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="tuesday" checked>
                                                        <label class="form-check-label" for="tuesday">Tuesday</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="wednesday" checked>
                                                        <label class="form-check-label" for="wednesday">Wednesday</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="thursday" checked>
                                                        <label class="form-check-label" for="thursday">Thursday</label>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="friday" checked>
                                                        <label class="form-check-label" for="friday">Friday</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="saturday">
                                                        <label class="form-check-label" for="saturday">Saturday</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="sunday">
                                                        <label class="form-check-label" for="sunday">Sunday</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <button type="submit" class="btn btn-gradient w-100">
                                            <i class="fa fa-save me-2"></i>Update Hours
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="content-card card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fa fa-bell me-2"></i>Notification Preferences</h5>
                                </div>
                                <div class="card-body">
                                    <form>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="email_notifications" checked>
                                            <label class="form-check-label" for="email_notifications">
                                                Email Notifications
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="sms_notifications">
                                            <label class="form-check-label" for="sms_notifications">
                                                SMS Notifications
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="booking_alerts" checked>
                                            <label class="form-check-label" for="booking_alerts">
                                                New Booking Alerts
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="payment_alerts" checked>
                                            <label class="form-check-label" for="payment_alerts">
                                                Payment Notifications
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="incident_alerts" checked>
                                            <label class="form-check-label" for="incident_alerts">
                                                Incident Report Alerts
                                            </label>
                                        </div>
                                        <button type="submit" class="btn btn-gradient w-100">
                                            <i class="fa fa-save me-2"></i>Save Preferences
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Management Center End -->

    <!-- Footer Start -->
    <div class="container-fluid bg-dark text-light footer pt-5 mt-5 wow fadeIn" data-wow-delay="0.1s">
        <div class="container py-5">
            <div class="row g-5">
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3"><i class="fa fa-baby me-2"></i>Nestlings</h4>
                    <p>Connecting families with trusted childcare providers across Sri Lanka. Safe, verified, and reliable daycare solutions for your little ones.</p>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Provider Resources</h4>
                    <a class="btn btn-link" href="help.php">Help Center</a>
                    <a class="btn btn-link" href="">Safety Guidelines</a>
                    <a class="btn btn-link" href="">Best Practices</a>
                    <a class="btn btn-link" href="">Training Materials</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Quick Links</h4>
                    <a class="btn btn-link" href="profile.php">My Profile</a>
                    <a class="btn btn-link" href="bookings.php">Manage Bookings</a>
                    <a class="btn btn-link" href="analytics.php">Analytics</a>
                    <a class="btn btn-link" href="management.php">Management Center</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Contact Support</h4>
                    <p class="mb-2"><i class="fa fa-map-marker-alt me-3"></i>123 Main Street, Colombo, Sri Lanka</p>
                    <p class="mb-2"><i class="fa fa-phone-alt me-3"></i>+94 11 234 5678</p>
                    <p class="mb-2"><i class="fa fa-envelope me-3"></i><EMAIL></p>
                </div>
            </div>
        </div>
    </div>
    <!-- Footer End -->

    <!-- Back to Top -->
    <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/owlcarousel/owl.carousel.min.js"></script>

    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>

    <script>
        // Initialize DataTables for better table management
        $(document).ready(function() {
            // Initialize any data tables if needed
            if ($('.data-table').length) {
                $('.data-table').DataTable({
                    responsive: true,
                    pageLength: 10,
                    order: [[0, 'desc']]
                });
            }

            // Tab switching functionality
            $('#managementTabs button').on('click', function (e) {
                e.preventDefault();
                $(this).tab('show');
            });

            // Form validation
            $('form').on('submit', function(e) {
                const form = this;
                const requiredFields = form.querySelectorAll('[required]');
                let isValid = true;

                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        field.classList.add('is-invalid');
                        isValid = false;
                    } else {
                        field.classList.remove('is-invalid');
                    }
                });

                if (!isValid) {
                    e.preventDefault();
                    alert('Please fill in all required fields.');
                }
            });

            // Real-time form validation
            $('input[required], select[required], textarea[required]').on('blur', function() {
                if (this.value.trim()) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } else {
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                }
            });

            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);
        });
    </script>

</body>
</html>
