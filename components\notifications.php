<?php
// Notification component for displaying user notifications
// Include this file in dashboards to show notifications

if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_type'])) {
    return;
}

// Get unread notifications count
$notification_count_stmt = $pdo->prepare("
    SELECT COUNT(*) as unread_count 
    FROM notifications 
    WHERE user_type = ? AND user_id = ? AND is_read = FALSE
");
$notification_count_stmt->execute([$_SESSION['user_type'], $_SESSION['user_id']]);
$unread_count = $notification_count_stmt->fetch()['unread_count'];

// Get recent notifications (last 10)
$notifications_stmt = $pdo->prepare("
    SELECT * FROM notifications 
    WHERE user_type = ? AND user_id = ? 
    ORDER BY created_at DESC 
    LIMIT 10
");
$notifications_stmt->execute([$_SESSION['user_type'], $_SESSION['user_id']]);
$notifications = $notifications_stmt->fetchAll();
?>

<!-- Notification Dropdown -->
<div class="dropdown">
    <button class="notification-btn" type="button" id="notificationDropdown" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="fa fa-bell"></i>
        <?php if ($unread_count > 0): ?>
            <span class="notification-count">
                <?php echo $unread_count > 99 ? '99+' : $unread_count; ?>
            </span>
        <?php endif; ?>
    </button>
    
    <ul class="dropdown-menu dropdown-menu-end notification-dropdown" aria-labelledby="notificationDropdown" style="width: 380px; max-height: 450px; overflow-y: auto; right: 0; left: auto; transform: none !important;">
        <li class="dropdown-header d-flex justify-content-between align-items-center">
            <span>Notifications</span>
            <?php if ($unread_count > 0): ?>
                <button class="btn btn-sm btn-link text-primary p-0" onclick="markAllAsRead()">
                    Mark all as read
                </button>
            <?php endif; ?>
        </li>
        
        <?php if (empty($notifications)): ?>
            <li class="dropdown-item text-center text-muted py-4">
                <i class="fa fa-bell-slash fa-2x mb-2"></i>
                <div>No notifications yet</div>
            </li>
        <?php else: ?>
            <?php foreach ($notifications as $notification): ?>
                <li class="dropdown-item notification-item <?php echo $notification['is_read'] ? '' : 'unread'; ?>" 
                    data-notification-id="<?php echo $notification['notification_id']; ?>">
                    <div class="d-flex">
                        <div class="notification-icon me-3">
                            <i class="fa fa-<?php echo $notification['type'] === 'booking' ? 'calendar' : ($notification['type'] === 'review' ? 'star' : 'info-circle'); ?> text-primary"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1 fw-bold"><?php echo htmlspecialchars($notification['title']); ?></h6>
                            <p class="mb-1 small text-muted"><?php echo htmlspecialchars($notification['message']); ?></p>
                            <small class="text-muted">
                                <?php echo date('M j, Y g:i A', strtotime($notification['created_at'])); ?>
                            </small>
                        </div>
                        <?php if (!$notification['is_read']): ?>
                            <div class="notification-badge">
                                <span class="badge bg-primary rounded-pill">&nbsp;</span>
                            </div>
                        <?php endif; ?>
                    </div>
                </li>
            <?php endforeach; ?>
            
            <li><hr class="dropdown-divider"></li>
            <li class="dropdown-item text-center">
                <a href="notifications.php" class="text-decoration-none">View all notifications</a>
            </li>
        <?php endif; ?>
    </ul>
</div>

<style>
.notification-dropdown {
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    border-radius: 15px;
    z-index: 9999 !important;
    position: absolute !important;
    top: 100% !important;
    right: 0 !important;
    left: auto !important;
    margin-top: 8px !important;
}

.notification-item {
    border-bottom: 1px solid #f8f9fa;
    padding: 15px 20px;
    transition: background-color 0.2s ease;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.unread {
    background-color: #e3f2fd;
    border-left: 4px solid var(--primary);
}

.notification-icon {
    width: 40px;
    height: 40px;
    background: #f8f9fa;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-badge {
    width: 8px;
}

.dropdown-toggle::after {
    display: none;
}

.notification-btn {
    position: relative;
    background: none;
    border: none;
    color: #6c757d;
    font-size: 1.2rem;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.notification-btn:hover {
    background-color: #f8f9fa;
    color: var(--primary);
}

.notification-count {
    position: absolute;
    top: -2px;
    right: -2px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.dropdown-header {
    font-weight: 600;
    color: var(--dark);
    padding: 15px 20px 10px;
}
</style>

<script>
function markAllAsRead() {
    fetch('mark_notifications_read.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({action: 'mark_all_read'})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => console.error('Error:', error));
}

// Mark individual notification as read when clicked
document.addEventListener('click', function(e) {
    const notificationItem = e.target.closest('.notification-item');
    if (notificationItem && notificationItem.classList.contains('unread')) {
        const notificationId = notificationItem.dataset.notificationId;
        
        fetch('mark_notifications_read.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'mark_single_read',
                notification_id: notificationId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                notificationItem.classList.remove('unread');
                const badge = notificationItem.querySelector('.notification-badge');
                if (badge) badge.remove();
                
                // Update notification count
                const countBadge = document.querySelector('#notificationDropdown .badge');
                if (countBadge) {
                    const currentCount = parseInt(countBadge.textContent);
                    if (currentCount <= 1) {
                        countBadge.remove();
                    } else {
                        countBadge.textContent = currentCount - 1;
                    }
                }
            }
        })
        .catch(error => console.error('Error:', error));
    }
});
</script>
