<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a daycare provider
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'provider') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

try {
    $provider_id = $_SESSION['user_id'];
    
    // Get pending bookings count
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as pending_count
        FROM bookings
        WHERE provider_id = ? AND status = 'pending'
    ");
    $stmt->execute([$provider_id]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    header('Content-Type: application/json');
    echo json_encode(['pending_count' => (int)$result['pending_count']]);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database error']);
}
?>
