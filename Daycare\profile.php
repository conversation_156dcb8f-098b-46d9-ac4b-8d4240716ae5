<?php
session_start();

// Check if user is logged in and is a daycare provider
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'provider') {
    header("Location: ../login.php");
    exit();
}

require_once '../config/database.php';

// Get provider information
$stmt = $pdo->prepare("SELECT * FROM providers WHERE provider_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$provider = $stmt->fetch();

if (!$provider) {
    session_destroy();
    header("Location: ../login.php");
    exit();
}

$success_message = '';
$error_message = '';

// Handle image upload
if (isset($_POST['upload_image']) && isset($_FILES['gallery_image'])) {
    $upload_dir = '../uploads/gallery/';
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }

    $file = $_FILES['gallery_image'];
    $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];

    if (in_array($file['type'], $allowed_types) && $file['size'] <= 5000000) { // 5MB limit
        $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $new_filename = 'gallery_' . $_SESSION['user_id'] . '_' . time() . '.' . $file_extension;
        $upload_path = $upload_dir . $new_filename;

        if (move_uploaded_file($file['tmp_name'], $upload_path)) {
            $caption = trim($_POST['image_caption'] ?? '');
            $stmt = $pdo->prepare("INSERT INTO provider_portfolio (provider_id, image_path, caption) VALUES (?, ?, ?)");
            if ($stmt->execute([$_SESSION['user_id'], 'uploads/gallery/' . $new_filename, $caption])) {
                $success_message = "Image uploaded successfully!";
            } else {
                $error_message = "Failed to save image information.";
                unlink($upload_path);
            }
        } else {
            $error_message = "Failed to upload image.";
        }
    } else {
        $error_message = "Invalid file type or size. Please upload JPG, PNG, or GIF files under 5MB.";
    }
}

// Handle image deletion
if (isset($_POST['delete_image'])) {
    $image_id = (int)$_POST['image_id'];
    $stmt = $pdo->prepare("SELECT image_path FROM provider_portfolio WHERE portfolio_id = ? AND provider_id = ?");
    $stmt->execute([$image_id, $_SESSION['user_id']]);
    $image = $stmt->fetch();

    if ($image) {
        $stmt = $pdo->prepare("DELETE FROM provider_portfolio WHERE portfolio_id = ? AND provider_id = ?");
        if ($stmt->execute([$image_id, $_SESSION['user_id']])) {
            $file_path = '../' . $image['image_path'];
            if (file_exists($file_path)) {
                unlink($file_path);
            }
            $success_message = "Image deleted successfully!";
        } else {
            $error_message = "Failed to delete image.";
        }
    }
}

// Handle document upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['upload_document'])) {
    $document_type = $_POST['document_type'];
    $document_name = trim($_POST['document_name']);
    $expiry_date = $_POST['expiry_date'] ?: null;

    if (empty($document_type) || empty($document_name)) {
        $error_message = "Document type and name are required.";
    } elseif (!isset($_FILES['document_file']) || $_FILES['document_file']['error'] !== UPLOAD_ERR_OK) {
        $error_message = "Please select a valid document file.";
    } else {
        $file = $_FILES['document_file'];
        $allowed_types = ['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx'];
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

        if (!in_array($file_extension, $allowed_types)) {
            $error_message = "Invalid file type. Allowed types: PDF, JPG, PNG, DOC, DOCX";
        } elseif ($file['size'] > 5 * 1024 * 1024) { // 5MB limit
            $error_message = "File size must be less than 5MB.";
        } else {
            // Create uploads directory if it doesn't exist
            $upload_dir = '../uploads/documents/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            // Generate unique filename
            $filename = $_SESSION['user_id'] . '_' . time() . '_' . uniqid() . '.' . $file_extension;
            $file_path = $upload_dir . $filename;

            if (move_uploaded_file($file['tmp_name'], $file_path)) {
                try {
                    $stmt = $pdo->prepare("
                        INSERT INTO provider_documents (provider_id, document_type, document_name, file_path, expiry_date, uploaded_at)
                        VALUES (?, ?, ?, ?, ?, NOW())
                    ");
                    $stmt->execute([$_SESSION['user_id'], $document_type, $document_name, $filename, $expiry_date]);

                    $success_message = "Document uploaded successfully!";
                } catch (PDOException $e) {
                    $error_message = "Database error. Please try again.";
                    unlink($file_path); // Remove uploaded file on database error
                }
            } else {
                $error_message = "Failed to upload file. Please try again.";
            }
        }
    }
}

// Handle document deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_document'])) {
    $document_id = (int)$_POST['document_id'];

    // Get document info to delete file
    $stmt = $pdo->prepare("SELECT file_path FROM provider_documents WHERE document_id = ? AND provider_id = ?");
    $stmt->execute([$document_id, $_SESSION['user_id']]);
    $document = $stmt->fetch();

    if ($document) {
        // Delete from database
        $stmt = $pdo->prepare("DELETE FROM provider_documents WHERE document_id = ? AND provider_id = ?");
        $stmt->execute([$document_id, $_SESSION['user_id']]);

        // Delete file
        $file_path = '../uploads/documents/' . $document['file_path'];
        if (file_exists($file_path)) {
            unlink($file_path);
        }

        $success_message = "Document deleted successfully!";
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($_POST['upload_image']) && !isset($_POST['delete_image']) && !isset($_POST['upload_document']) && !isset($_POST['delete_document'])) {
    $business_name = trim($_POST['business_name']);
    $contact_person = trim($_POST['contact_person']);
    $email = trim($_POST['email']);
    $phone = trim($_POST['phone']);
    $address = trim($_POST['address']);
    $district = $_POST['district'];
    $service_types = implode(',', $_POST['service_types'] ?? []);
    $capacity = (int)$_POST['capacity'];
    $description = trim($_POST['description']);
    
    // Validation
    if (empty($business_name) || empty($contact_person) || empty($email) || empty($phone) || 
        empty($address) || empty($district) || empty($service_types) || empty($capacity)) {
        $error_message = "All required fields must be filled.";
    } else {
        // Check if email is already used by another provider
        $stmt = $pdo->prepare("SELECT provider_id FROM providers WHERE email = ? AND provider_id != ?");
        $stmt->execute([$email, $_SESSION['user_id']]);
        
        if ($stmt->fetch()) {
            $error_message = "Email already in use by another provider.";
        } else {
            try {
                $stmt = $pdo->prepare("
                    UPDATE providers 
                    SET business_name = ?, contact_person = ?, email = ?, phone = ?, address = ?, 
                        district = ?, service_types = ?, capacity = ?, description = ?, updated_at = NOW()
                    WHERE provider_id = ?
                ");
                
                $stmt->execute([
                    $business_name, $contact_person, $email, $phone, $address, 
                    $district, $service_types, $capacity, $description, $_SESSION['user_id']
                ]);
                
                $success_message = "Profile updated successfully!";
                
                // Refresh provider data
                $stmt = $pdo->prepare("SELECT * FROM providers WHERE provider_id = ?");
                $stmt->execute([$_SESSION['user_id']]);
                $provider = $stmt->fetch();
                
            } catch (PDOException $e) {
                $error_message = "Update failed. Please try again.";
            }
        }
    }
}

// Sri Lankan districts
$districts = [
    'Colombo', 'Gampaha', 'Kalutara', 'Kandy', 'Matale', 'Nuwara Eliya', 'Galle', 'Matara', 'Hambantota',
    'Jaffna', 'Kilinochchi', 'Mannar', 'Vavuniya', 'Mullaitivu', 'Batticaloa', 'Ampara', 'Trincomalee',
    'Kurunegala', 'Puttalam', 'Anuradhapura', 'Polonnaruwa', 'Badulla', 'Moneragala', 'Ratnapura', 'Kegalle'
];

$service_types = [
    'Infant Care (0-2 years)', 'Toddler Care (2-3 years)', 'Preschool (3-5 years)', 
    'After School Care', 'Full Day Care', 'Half Day Care', 'Weekend Care', 'Holiday Care'
];

$current_services = explode(',', $provider['service_types']);

// Get gallery images
$stmt = $pdo->prepare("SELECT * FROM provider_portfolio WHERE provider_id = ? ORDER BY created_at DESC");
$stmt->execute([$_SESSION['user_id']]);
$gallery_images = $stmt->fetchAll();

// Get provider documents
$stmt = $pdo->prepare("
    SELECT * FROM provider_documents
    WHERE provider_id = ?
    ORDER BY document_type, uploaded_at DESC
");
$stmt->execute([$_SESSION['user_id']]);
$documents = $stmt->fetchAll();

// Group documents by type
$grouped_documents = [];
foreach ($documents as $doc) {
    $grouped_documents[$doc['document_type']][] = $doc;
}

// Document types
$document_types = [
    'business_license' => 'Business License',
    'childcare_license' => 'Childcare License',
    'insurance' => 'Insurance Certificate',
    'safety_certificate' => 'Safety Certificate',
    'staff_qualifications' => 'Staff Qualifications',
    'health_certificate' => 'Health Certificate',
    'fire_safety' => 'Fire Safety Certificate',
    'ncpa_certificate' => 'NCPA Certificate',
    'moh_certificate' => 'MOH Certificate',
    'other' => 'Other Documents'
];
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>Business Profile - Nestlings</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="" name="keywords">
    <meta content="" name="description">

    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600&family=Nunito:wght@600;700;800&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">
    <link href="../lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        .profile-header {
            background: linear-gradient(135deg, var(--primary) 0%, #6BB6FF 100%);
            color: white;
            padding: 60px 0;
        }
        
        .profile-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            border: none;
            overflow: hidden;
            margin-top: -50px;
            position: relative;
            z-index: 10;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary) 0%, #6BB6FF 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #6BB6FF 0%, var(--primary) 100%);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(74, 144, 226, 0.3);
        }
        
        .user-dropdown {
            border-radius: 25px;
            padding: 8px 20px;
            background: linear-gradient(135deg, var(--primary) 0%, #6BB6FF 100%);
            border: none;
            color: white;
            font-weight: 500;
        }
        
        .user-dropdown:hover {
            background: linear-gradient(135deg, #6BB6FF 0%, var(--primary) 100%);
            color: white;
        }
        
        .verification-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .verification-pending {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .verification-verified {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .verification-rejected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .service-checkbox {
            margin: 8px 0;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .service-checkbox:hover {
            border-color: var(--primary);
            background: rgba(74, 144, 226, 0.05);
        }
        
        .service-checkbox input[type="checkbox"] {
            margin-right: 10px;
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }

        /* Gallery Styles */
        .gallery-item {
            position: relative;
            overflow: hidden;
            border-radius: 12px;
            transition: transform 0.3s ease;
        }

        .gallery-item:hover {
            transform: translateY(-5px);
        }

        .gallery-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .gallery-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .gallery-item:hover .gallery-overlay {
            opacity: 1;
        }

        .gallery-actions {
            display: flex;
            gap: 10px;
        }

        .gallery-caption {
            padding: 8px 0;
            font-size: 0.9rem;
        }
        
        .profile-stats {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        /* Document Styles */
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 40px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .upload-area:hover {
            border-color: #4A90E2;
            background: rgba(74, 144, 226, 0.05);
        }

        .upload-area.dragover {
            border-color: #4A90E2;
            background: rgba(74, 144, 226, 0.1);
            transform: scale(1.02);
        }

        .document-item {
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .document-item:hover {
            background: rgba(74, 144, 226, 0.05);
            border-color: #4A90E2;
        }

        .document-type-section {
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 20px;
        }

        .document-type-section:last-child {
            border-bottom: none;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-approved {
            background: #d1edff;
            color: #0c5460;
        }

        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }

        .document-actions {
            display: flex;
            align-items: center;
        }
    </style>
</head>

<body>
    <!-- Spinner Start -->
    <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>
    <!-- Spinner End -->

    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light shadow sticky-top p-0">
        <a href="../index.html" class="navbar-brand d-flex align-items-center px-4 px-lg-5">
            <h2 class="m-0 text-primary"><i class="fa fa-baby me-3"></i>Nestlings</h2>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="dashboard.php" class="nav-item nav-link">Dashboard</a>
                <a href="bookings.php" class="nav-item nav-link">Bookings</a>
                <a href="profile.php" class="nav-item nav-link active">Profile</a>
                <a href="analytics.php" class="nav-item nav-link">Analytics</a>
                <a href="management.php" class="nav-item nav-link">Management</a>
                <a href="payments.php" class="nav-item nav-link">Payments</a>
            </div>
            <div class="dropdown">
                <button class="btn btn-primary dropdown-toggle me-4" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fa fa-user me-2"></i>
                    <span><?php echo htmlspecialchars($provider['business_name']); ?></span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="profile.php"><i class="fa fa-building me-2"></i>Business Profile</a></li>
                    <li><a class="dropdown-item" href="settings.php"><i class="fa fa-cog me-2"></i>Settings</a></li>
                    <li><a class="dropdown-item" href="help.php"><i class="fa fa-question-circle me-2"></i>Help</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="../logout.php"><i class="fa fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Profile Header Start -->
    <div class="profile-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-5 mb-3">Business Profile</h1>
                    <p class="lead mb-0">Manage your daycare business information and services</p>
                </div>
                <div class="col-lg-4 text-lg-end">
                    <div class="verification-badge verification-<?php echo $provider['verification_status']; ?>">
                        <i class="fa fa-<?php echo $provider['verification_status'] === 'verified' ? 'check-circle' : ($provider['verification_status'] === 'rejected' ? 'times-circle' : 'clock'); ?> me-2"></i>
                        <?php echo ucfirst($provider['verification_status']); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Profile Header End -->

    <!-- Profile Content Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <div class="profile-card card">
                <div class="card-body p-5">
                    <?php if ($success_message): ?>
                        <div class="alert alert-success">
                            <i class="fa fa-check-circle me-2"></i><?php echo $success_message; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($error_message): ?>
                        <div class="alert alert-danger">
                            <i class="fa fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                        </div>
                    <?php endif; ?>

                    <!-- Profile Statistics -->
                    <div class="profile-stats">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <div class="stat-number"><?php echo $provider['capacity']; ?></div>
                                    <div class="stat-label">Child Capacity</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <div class="stat-number"><?php echo number_format($provider['average_rating'], 1); ?></div>
                                    <div class="stat-label">Average Rating</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <div class="stat-number"><?php echo $provider['total_reviews']; ?></div>
                                    <div class="stat-label">Total Reviews</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <div class="stat-number"><?php echo date('Y', strtotime($provider['created_at'])); ?></div>
                                    <div class="stat-label">Member Since</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form method="POST" action="">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <label for="business_name" class="form-label">Business Name *</label>
                                <input type="text" class="form-control" id="business_name" name="business_name" 
                                       value="<?php echo htmlspecialchars($provider['business_name']); ?>" required>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="contact_person" class="form-label">Contact Person *</label>
                                <input type="text" class="form-control" id="contact_person" name="contact_person" 
                                       value="<?php echo htmlspecialchars($provider['contact_person']); ?>" required>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($provider['email']); ?>" required>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="phone" class="form-label">Phone Number *</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo htmlspecialchars($provider['phone']); ?>" required>
                            </div>
                            
                            <div class="col-12">
                                <label for="address" class="form-label">Business Address *</label>
                                <textarea class="form-control" id="address" name="address" rows="3" required><?php echo htmlspecialchars($provider['address']); ?></textarea>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="district" class="form-label">District *</label>
                                <select class="form-control" id="district" name="district" required>
                                    <option value="">Select District</option>
                                    <?php foreach ($districts as $district): ?>
                                        <option value="<?php echo $district; ?>" 
                                                <?php echo ($provider['district'] === $district) ? 'selected' : ''; ?>>
                                            <?php echo $district; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="capacity" class="form-label">Child Capacity *</label>
                                <input type="number" class="form-control" id="capacity" name="capacity" min="1" max="200"
                                       value="<?php echo $provider['capacity']; ?>" required>
                            </div>
                            
                            <div class="col-12">
                                <label class="form-label">Services Offered * (Select all that apply)</label>
                                <div class="row">
                                    <?php foreach ($service_types as $service): ?>
                                        <div class="col-md-6">
                                            <div class="service-checkbox">
                                                <input type="checkbox" id="service_<?php echo md5($service); ?>" 
                                                       name="service_types[]" value="<?php echo $service; ?>"
                                                       <?php echo (in_array($service, $current_services)) ? 'checked' : ''; ?>>
                                                <label for="service_<?php echo md5($service); ?>"><?php echo $service; ?></label>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            
                            <div class="col-12">
                                <label for="description" class="form-label">Business Description</label>
                                <textarea class="form-control" id="description" name="description" rows="5" 
                                          placeholder="Tell parents about your daycare, facilities, and approach to childcare..."><?php echo htmlspecialchars($provider['description']); ?></textarea>
                            </div>
                            
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="dashboard.php" class="btn btn-outline-secondary">
                                        <i class="fa fa-arrow-left me-2"></i>Back to Dashboard
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fa fa-save me-2"></i>Update Profile
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- Profile Content End -->

    <!-- Gallery Section Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <div class="row g-5">
                <div class="col-12">
                    <div class="content-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4 class="mb-0"><i class="fa fa-images me-2"></i>Facility Gallery</h4>
                            <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#uploadModal">
                                <i class="fa fa-plus me-2"></i>Add Image
                            </button>
                        </div>
                        <div class="card-body">
                            <?php if (empty($gallery_images)): ?>
                                <div class="text-center py-5">
                                    <i class="fa fa-images fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No images uploaded yet</h5>
                                    <p class="text-muted">Upload photos of your daycare facility to showcase your environment to parents.</p>
                                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                                        <i class="fa fa-plus me-2"></i>Upload First Image
                                    </button>
                                </div>
                            <?php else: ?>
                                <div class="row g-3">
                                    <?php foreach ($gallery_images as $image): ?>
                                        <div class="col-lg-3 col-md-4 col-sm-6">
                                            <div class="gallery-item">
                                                <div class="position-relative">
                                                    <img src="../<?php echo htmlspecialchars($image['image_path']); ?>"
                                                         class="img-fluid rounded gallery-image"
                                                         alt="<?php echo htmlspecialchars($image['caption']); ?>"
                                                         data-bs-toggle="modal"
                                                         data-bs-target="#imageModal"
                                                         data-image="../<?php echo htmlspecialchars($image['image_path']); ?>"
                                                         data-caption="<?php echo htmlspecialchars($image['caption']); ?>">
                                                    <div class="gallery-overlay">
                                                        <div class="gallery-actions">
                                                            <button class="btn btn-sm btn-light me-2"
                                                                    data-bs-toggle="modal"
                                                                    data-bs-target="#imageModal"
                                                                    data-image="../<?php echo htmlspecialchars($image['image_path']); ?>"
                                                                    data-caption="<?php echo htmlspecialchars($image['caption']); ?>">
                                                                <i class="fa fa-eye"></i>
                                                            </button>
                                                            <form method="POST" style="display: inline;">
                                                                <input type="hidden" name="image_id" value="<?php echo $image['portfolio_id']; ?>">
                                                                <button type="submit" name="delete_image" class="btn btn-sm btn-danger"
                                                                        onclick="return confirm('Are you sure you want to delete this image?')">
                                                                    <i class="fa fa-trash"></i>
                                                                </button>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                                <?php if (!empty($image['caption'])): ?>
                                                    <div class="gallery-caption mt-2">
                                                        <small class="text-muted"><?php echo htmlspecialchars($image['caption']); ?></small>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Gallery Section End -->

    <!-- Documents Section Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <div class="text-center wow fadeInUp" data-wow-delay="0.1s">
                <h6 class="section-title bg-white text-center text-primary px-3">Documents</h6>
                <h1 class="mb-5">Business Documents & Certificates</h1>
            </div>

            <div class="row g-4">
                <!-- Upload New Document -->
                <div class="col-lg-4">
                    <div class="content-card card h-100">
                        <div class="card-header">
                            <h4 class="mb-0"><i class="fa fa-upload me-2"></i>Upload Document</h4>
                        </div>
                        <div class="card-body">
                            <form method="POST" enctype="multipart/form-data">
                                <div class="mb-3">
                                    <label for="document_type" class="form-label">Document Type *</label>
                                    <select class="form-control" id="document_type" name="document_type" required>
                                        <option value="">Select Type</option>
                                        <?php foreach ($document_types as $type => $name): ?>
                                            <option value="<?php echo $type; ?>"><?php echo $name; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="document_name" class="form-label">Document Name *</label>
                                    <input type="text" class="form-control" id="document_name" name="document_name"
                                           placeholder="e.g., Business License 2024" required>
                                </div>

                                <div class="mb-3">
                                    <label for="expiry_date" class="form-label">Expiry Date (Optional)</label>
                                    <input type="date" class="form-control" id="expiry_date" name="expiry_date">
                                </div>

                                <div class="mb-3">
                                    <label for="document_file" class="form-label">Document File *</label>
                                    <div class="upload-area" id="documentUploadArea">
                                        <i class="fa fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                        <p class="text-muted mb-2">Drag & drop your document here</p>
                                        <p class="text-muted small">or click to browse</p>
                                        <input type="file" class="form-control" id="document_file" name="document_file"
                                               accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" required style="display: none;">
                                    </div>
                                    <small class="text-muted">Supported formats: PDF, JPG, PNG, DOC, DOCX (Max: 5MB)</small>
                                </div>

                                <button type="submit" name="upload_document" class="btn btn-primary w-100">
                                    <i class="fa fa-upload me-2"></i>Upload Document
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Document List -->
                <div class="col-lg-8">
                    <div class="content-card card h-100">
                        <div class="card-header">
                            <h4 class="mb-0"><i class="fa fa-folder me-2"></i>My Documents</h4>
                        </div>
                        <div class="card-body">
                            <?php if (empty($documents)): ?>
                                <div class="text-center py-5">
                                    <i class="fa fa-folder-open fa-4x text-muted mb-4"></i>
                                    <h5 class="text-muted">No documents uploaded yet</h5>
                                    <p class="text-muted">Upload your business documents to get verified and start receiving bookings</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($document_types as $type => $type_name): ?>
                                    <?php if (isset($grouped_documents[$type])): ?>
                                        <div class="document-type-section mb-4">
                                            <h5 class="mb-3"><i class="fa fa-folder me-2"></i><?php echo $type_name; ?></h5>

                                            <?php foreach ($grouped_documents[$type] as $doc): ?>
                                            <div class="document-item">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div class="flex-grow-1">
                                                        <h6 class="mb-1"><?php echo htmlspecialchars($doc['document_name']); ?></h6>
                                                        <small class="text-muted">
                                                            <i class="fa fa-calendar me-1"></i>
                                                            Uploaded: <?php echo date('M d, Y', strtotime($doc['uploaded_at'])); ?>
                                                            <?php if ($doc['expiry_date']): ?>
                                                                | Expires: <?php echo date('M d, Y', strtotime($doc['expiry_date'])); ?>
                                                            <?php endif; ?>
                                                        </small>
                                                    </div>
                                                    <div class="document-actions">
                                                        <span class="status-badge status-<?php echo $doc['verification_status']; ?>">
                                                            <?php echo ucfirst($doc['verification_status']); ?>
                                                        </span>
                                                        <a href="../uploads/documents/<?php echo $doc['file_path']; ?>"
                                                           target="_blank" class="btn btn-sm btn-outline-primary ms-2">
                                                            <i class="fa fa-eye"></i>
                                                        </a>
                                                        <form method="POST" style="display: inline;" class="ms-1">
                                                            <input type="hidden" name="document_id" value="<?php echo $doc['document_id']; ?>">
                                                            <button type="submit" name="delete_document" class="btn btn-sm btn-outline-danger"
                                                                    onclick="return confirm('Are you sure you want to delete this document?')">
                                                                <i class="fa fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Documents Section End -->

    <!-- Footer Start -->
    <div class="container-fluid bg-dark text-light footer pt-5 mt-5 wow fadeIn" data-wow-delay="0.1s">
        <div class="container py-5">
            <div class="row g-5">
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3"><i class="fa fa-baby me-2"></i>Nestlings</h4>
                    <p>Connecting families with trusted childcare providers across Sri Lanka. Safe, verified, and reliable daycare solutions for your little ones.</p>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Provider Resources</h4>
                    <a class="btn btn-link" href="help.php">Help Center</a>
                    <a class="btn btn-link" href="">Safety Guidelines</a>
                    <a class="btn btn-link" href="">Best Practices</a>
                    <a class="btn btn-link" href="">Training Materials</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Quick Links</h4>
                    <a class="btn btn-link" href="profile.php">My Profile</a>
                    <a class="btn btn-link" href="bookings.php">Manage Bookings</a>
                    <a class="btn btn-link" href="analytics.php">Analytics</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Contact Support</h4>
                    <p class="mb-2"><i class="fa fa-map-marker-alt me-3"></i>123 Main Street, Colombo, Sri Lanka</p>
                    <p class="mb-2"><i class="fa fa-phone-alt me-3"></i>+94 11 234 5678</p>
                    <p class="mb-2"><i class="fa fa-envelope me-3"></i><EMAIL></p>
                </div>
            </div>
        </div>
    </div>
    <!-- Footer End -->

    <!-- Back to Top -->
    <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>

    <!-- Upload Image Modal -->
    <div class="modal fade" id="uploadModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="uploadModalLabel">Upload Facility Image</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" enctype="multipart/form-data">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="gallery_image" class="form-label">Select Image *</label>
                            <input type="file" class="form-control" id="gallery_image" name="gallery_image"
                                   accept="image/jpeg,image/jpg,image/png,image/gif" required>
                            <div class="form-text">Supported formats: JPG, PNG, GIF. Maximum size: 5MB</div>
                        </div>
                        <div class="mb-3">
                            <label for="image_caption" class="form-label">Caption (Optional)</label>
                            <input type="text" class="form-control" id="image_caption" name="image_caption"
                                   placeholder="Describe this image (e.g., 'Play Area', 'Classroom', 'Outdoor Space')">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="upload_image" class="btn btn-primary">
                            <i class="fa fa-upload me-2"></i>Upload Image
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- View Image Modal -->
    <div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imageModalLabel">Facility Image</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" class="img-fluid rounded" alt="">
                    <p id="modalCaption" class="mt-3 text-muted"></p>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/owlcarousel/owl.carousel.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>

    <!-- Gallery JavaScript -->
    <script>
        // Handle image modal
        document.addEventListener('DOMContentLoaded', function() {
            const imageModal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            const modalCaption = document.getElementById('modalCaption');

            // Handle gallery image clicks
            document.querySelectorAll('[data-bs-target="#imageModal"]').forEach(function(element) {
                element.addEventListener('click', function() {
                    const imageSrc = this.getAttribute('data-image');
                    const caption = this.getAttribute('data-caption');

                    modalImage.src = imageSrc;
                    modalCaption.textContent = caption || 'No caption';
                });
            });
        });

        // Document upload functionality
        const documentUploadArea = document.getElementById('documentUploadArea');
        const documentFileInput = document.getElementById('document_file');

        if (documentUploadArea && documentFileInput) {
            documentUploadArea.addEventListener('click', () => {
                documentFileInput.click();
            });

            documentUploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                documentUploadArea.classList.add('dragover');
            });

            documentUploadArea.addEventListener('dragleave', () => {
                documentUploadArea.classList.remove('dragover');
            });

            documentUploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                documentUploadArea.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    documentFileInput.files = files;
                    updateDocumentDisplay(files[0]);
                }
            });

            documentFileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    updateDocumentDisplay(e.target.files[0]);
                }
            });

            function updateDocumentDisplay(file) {
                documentUploadArea.innerHTML = `
                    <i class="fa fa-file fa-3x text-primary mb-3"></i>
                    <p class="text-primary mb-2"><strong>${file.name}</strong></p>
                    <small class="text-muted">Size: ${(file.size / 1024 / 1024).toFixed(2)} MB</small>
                `;
            }
        }
    </script>
</body>

</html>
