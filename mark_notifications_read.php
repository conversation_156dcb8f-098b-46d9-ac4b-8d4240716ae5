<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_type'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

require_once 'config/database.php';

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['action'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
    exit();
}

try {
    if ($input['action'] === 'mark_all_read') {
        // Mark all notifications as read for the current user
        $stmt = $pdo->prepare("
            UPDATE notifications 
            SET is_read = TRUE 
            WHERE user_type = ? AND user_id = ? AND is_read = FALSE
        ");
        $stmt->execute([$_SESSION['user_type'], $_SESSION['user_id']]);
        
        echo json_encode(['success' => true, 'message' => 'All notifications marked as read']);
        
    } elseif ($input['action'] === 'mark_single_read' && isset($input['notification_id'])) {
        // Mark single notification as read
        $stmt = $pdo->prepare("
            UPDATE notifications 
            SET is_read = TRUE 
            WHERE notification_id = ? AND user_type = ? AND user_id = ?
        ");
        $stmt->execute([$input['notification_id'], $_SESSION['user_type'], $_SESSION['user_id']]);
        
        echo json_encode(['success' => true, 'message' => 'Notification marked as read']);
        
    } else {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database error']);
}
?>
