-- Migration: Create pricing system for daycare providers
-- Date: 2025-07-03

USE nestlings_db;

-- Create provider_pricing table
CREATE TABLE provider_pricing (
    pricing_id INT AUTO_INCREMENT PRIMARY KEY,
    provider_id INT NOT NULL,
    service_type VARCHAR(100) NOT NULL,
    hourly_rate DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    daily_rate DECIMAL(10,2) NULL,
    weekly_rate DECIMAL(10,2) NULL,
    monthly_rate DECIMAL(10,2) NULL,
    minimum_hours INT DEFAULT 1,
    maximum_hours INT DEFAULT 24,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (provider_id) REFERENCES providers(provider_id) ON DELETE CASCADE,
    UNIQUE KEY unique_provider_service (provider_id, service_type)
);

-- Insert default pricing for existing providers
INSERT INTO provider_pricing (provider_id, service_type, hourly_rate, daily_rate, description)
SELECT 
    provider_id,
    'full_day' as service_type,
    500.00 as hourly_rate,
    3500.00 as daily_rate,
    'Full day care service including meals, activities, and supervision'
FROM providers;

INSERT INTO provider_pricing (provider_id, service_type, hourly_rate, daily_rate, description)
SELECT 
    provider_id,
    'half_day' as service_type,
    600.00 as hourly_rate,
    2500.00 as daily_rate,
    'Half day care service (up to 6 hours) including snacks and activities'
FROM providers;

INSERT INTO provider_pricing (provider_id, service_type, hourly_rate, daily_rate, description)
SELECT 
    provider_id,
    'after_school' as service_type,
    400.00 as hourly_rate,
    1500.00 as daily_rate,
    'After school care service including homework assistance and snacks'
FROM providers;

INSERT INTO provider_pricing (provider_id, service_type, hourly_rate, daily_rate, description)
SELECT 
    provider_id,
    'weekend' as service_type,
    700.00 as hourly_rate,
    4000.00 as daily_rate,
    'Weekend care service with special activities and outings'
FROM providers;

INSERT INTO provider_pricing (provider_id, service_type, hourly_rate, daily_rate, description)
SELECT 
    provider_id,
    'overnight' as service_type,
    800.00 as hourly_rate,
    5000.00 as daily_rate,
    'Overnight care service including dinner, breakfast, and supervision'
FROM providers;

-- Add indexes for better performance
CREATE INDEX idx_provider_pricing_provider ON provider_pricing(provider_id);
CREATE INDEX idx_provider_pricing_service ON provider_pricing(service_type);
CREATE INDEX idx_provider_pricing_active ON provider_pricing(is_active);

-- Verify the changes
SELECT COUNT(*) as total_pricing_records FROM provider_pricing;
DESCRIBE provider_pricing;
