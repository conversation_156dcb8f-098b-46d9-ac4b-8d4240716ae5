<?php
session_start();

// Check if user is logged in and is a daycare provider
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'provider') {
    header("Location: ../login.php");
    exit();
}

require_once '../config/database.php';

// Get provider information
$stmt = $pdo->prepare("SELECT * FROM providers WHERE provider_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$provider = $stmt->fetch();

if (!$provider) {
    session_destroy();
    header("Location: ../login.php");
    exit();
}

// Get payment analytics
$provider_id = $_SESSION['user_id'];

// Total revenue (all paid bookings)
$stmt = $pdo->prepare("
    SELECT
        COALESCE(SUM(total_cost), 0) as total_revenue,
        COUNT(*) as total_paid_bookings
    FROM bookings
    WHERE provider_id = ? AND payment_status = 'paid'
");
$stmt->execute([$provider_id]);
$revenue_stats = $stmt->fetch();

// Pending payments
$stmt = $pdo->prepare("
    SELECT
        COALESCE(SUM(total_cost), 0) as pending_amount,
        COUNT(*) as pending_count
    FROM bookings
    WHERE provider_id = ? AND payment_status = 'pending' AND status IN ('confirmed', 'completed')
");
$stmt->execute([$provider_id]);
$pending_stats = $stmt->fetch();

// Monthly revenue for current year
$stmt = $pdo->prepare("
    SELECT
        MONTH(booking_date) as month,
        COALESCE(SUM(total_cost), 0) as monthly_revenue,
        COUNT(*) as monthly_bookings
    FROM bookings
    WHERE provider_id = ? AND payment_status = 'paid' AND YEAR(booking_date) = YEAR(CURDATE())
    GROUP BY MONTH(booking_date)
    ORDER BY month
");
$stmt->execute([$provider_id]);
$monthly_revenue = $stmt->fetchAll();

// Recent payments (last 30 days)
$stmt = $pdo->prepare("
    SELECT b.*, p.name as parent_name, p.phone as parent_phone, p.email as parent_email
    FROM bookings b
    JOIN parents p ON b.parent_id = p.parent_id
    WHERE b.provider_id = ? AND b.payment_status = 'paid'
    AND b.booking_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
    ORDER BY b.booking_date DESC, b.created_at DESC
    LIMIT 10
");
$stmt->execute([$provider_id]);
$recent_payments = $stmt->fetchAll();

// Check if payments table exists
$payments_table_exists = false;
try {
    $check_table = $pdo->query("SHOW TABLES LIKE 'payments'");
    $payments_table_exists = $check_table->rowCount() > 0;
    error_log("Payments table exists: " . ($payments_table_exists ? 'Yes' : 'No'));
} catch (Exception $e) {
    error_log("Error checking payments table: " . $e->getMessage());
}
// Recent payments with detailed card information
try {
    $detailed_stmt = $pdo->prepare("
        SELECT
            p.*,
            b.booking_date,
            b.start_time,
            b.end_time,
            b.service_type,
            b.total_cost,
            b.status as booking_status,
            par.first_name,
            par.last_name,
            par.email,
            par.phone
        FROM payments p
        JOIN bookings b ON p.booking_id = b.booking_id
        JOIN parents par ON p.parent_id = par.parent_id
        WHERE p.provider_id = ? AND p.payment_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        ORDER BY p.payment_date DESC, p.created_at DESC
        LIMIT 10
    ");
    $detailed_stmt->execute([$provider_id]);
    $recent_payments_detailed = $detailed_stmt->fetchAll();

    // Debug: Log payment count
    error_log("Detailed payments found for provider $provider_id: " . count($recent_payments_detailed));
} catch (Exception $e) {
    $recent_payments_detailed = [];
}

// Calculate payment statistics from payments table
$payment_stats = [
    'total_payments' => 0,
    'completed_amount' => 0,
    'pending_amount' => 0,
    'total_amount' => 0
];

try {
    $stats_stmt = $pdo->prepare("
        SELECT
            COUNT(*) as total_payments,
            SUM(CASE WHEN payment_status = 'completed' THEN amount ELSE 0 END) as completed_amount,
            SUM(CASE WHEN payment_status = 'pending' THEN amount ELSE 0 END) as pending_amount,
            SUM(amount) as total_amount
        FROM payments
        WHERE provider_id = ?
    ");
    $stats_stmt->execute([$provider_id]);
    $payment_stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);

    // Ensure we have numeric values
    $payment_stats['total_payments'] = (int)$payment_stats['total_payments'];
    $payment_stats['completed_amount'] = (float)$payment_stats['completed_amount'];
    $payment_stats['pending_amount'] = (float)$payment_stats['pending_amount'];
    $payment_stats['total_amount'] = (float)$payment_stats['total_amount'];

} catch (Exception $e) {
    // Payments table might not exist yet
    error_log("Payment stats query error: " . $e->getMessage());
}

// Outstanding payments - using safe column references
try {
    $stmt = $pdo->prepare("
        SELECT
            b.*,
            p.name as parent_name,
            p.phone as parent_phone,
            p.email as parent_email,
            b.total_cost as outstanding_amount,
            DATEDIFF(CURDATE(), b.booking_date) as days_overdue
        FROM bookings b
        JOIN parents p ON b.parent_id = p.parent_id
        WHERE b.provider_id = ?
        AND b.status IN ('confirmed', 'completed')
        AND b.payment_status = 'pending'
        ORDER BY b.booking_date ASC
    ");
    $stmt->execute([$provider_id]);
    $outstanding_payments = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Outstanding payments query error: " . $e->getMessage());
    $outstanding_payments = [];
}

// Payment status distribution
$stmt = $pdo->prepare("
    SELECT
        payment_status,
        COUNT(*) as count,
        COALESCE(SUM(total_cost), 0) as total_amount
    FROM bookings
    WHERE provider_id = ?
    GROUP BY payment_status
");
$stmt->execute([$provider_id]);
$payment_distribution = $stmt->fetchAll();

// Calculate percentage changes (comparing last 30 days vs previous 30 days)
$stmt = $pdo->prepare("
    SELECT
        COALESCE(SUM(CASE WHEN booking_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN total_cost ELSE 0 END), 0) as current_month,
        COALESCE(SUM(CASE WHEN booking_date >= DATE_SUB(CURDATE(), INTERVAL 60 DAY) AND booking_date < DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN total_cost ELSE 0 END), 0) as previous_month
    FROM bookings
    WHERE provider_id = ? AND payment_status = 'paid'
");
$stmt->execute([$provider_id]);
$comparison = $stmt->fetch();

$revenue_change = 0;
if ($comparison['previous_month'] > 0) {
    $revenue_change = (($comparison['current_month'] - $comparison['previous_month']) / $comparison['previous_month']) * 100;
}

// Prepare monthly data for chart
$monthly_data = array_fill(1, 12, 0);
foreach ($monthly_revenue as $month) {
    $monthly_data[$month['month']] = $month['monthly_revenue'];
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>Payments - Nestlings Daycare Provider</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="" name="keywords">
    <meta content="" name="description">

    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600&family=Nunito:wght@600;700;800&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">
    <link href="../lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom Payments Styles -->
    <style>
        .payments-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
            margin-bottom: 0;
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
            transition: all 0.3s ease;
            height: 100%;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .stats-icon {
            width: 70px;
            height: 70px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            font-size: 24px;
            color: white;
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .stats-label {
            color: #7f8c8d;
            font-size: 1rem;
            margin-bottom: 10px;
        }

        .stats-change {
            font-size: 0.9rem;
            font-weight: 600;
        }

        .change-positive {
            color: #27ae60;
        }

        .change-negative {
            color: #e74c3c;
        }

        .content-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            border: none;
            margin-bottom: 30px;
        }

        .content-card .card-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 1px solid #dee2e6;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px 25px;
        }

        .payment-item {
            padding: 20px;
            border-bottom: 1px solid #f1f3f4;
            transition: all 0.3s ease;
        }

        .payment-item:hover {
            background-color: #f8f9fa;
        }

        .payment-item:last-child {
            border-bottom: none;
        }

        .payment-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-paid {
            background-color: #d4edda;
            color: #155724;
        }

        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-refunded {
            background-color: #f8d7da;
            color: #721c24;
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }

        .quick-action-btn {
            border-radius: 10px;
            padding: 12px 20px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .amount-highlight {
            font-size: 1.2rem;
            font-weight: 700;
            color: #2c3e50;
        }

        .amount-positive {
            color: #27ae60;
        }

        .amount-pending {
            color: #f39c12;
        }
    </style>
</head>

<body>
    <!-- Spinner Start -->
    <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>
    <!-- Spinner End -->

    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light shadow sticky-top p-0">
        <a href="../index.html" class="navbar-brand d-flex align-items-center px-4 px-lg-5">
            <h2 class="m-0 text-primary"><i class="fa fa-baby me-3"></i>Nestlings</h2>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="dashboard.php" class="nav-item nav-link">Dashboard</a>
                <a href="bookings.php" class="nav-item nav-link">Bookings</a>
                <a href="profile.php" class="nav-item nav-link">Profile</a>
                <a href="analytics.php" class="nav-item nav-link">Analytics</a>
                <a href="management.php" class="nav-item nav-link">Management</a>
                <a href="payments.php" class="nav-item nav-link active">Payments</a>
            </div>
            <div class="dropdown">
                <button class="btn btn-primary dropdown-toggle me-4" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fa fa-user me-2"></i>
                    <span><?php echo htmlspecialchars($provider['business_name']); ?></span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="profile.php"><i class="fa fa-building me-2"></i>Business Profile</a></li>
                    <li><a class="dropdown-item" href="settings.php"><i class="fa fa-cog me-2"></i>Settings</a></li>
                    <li><a class="dropdown-item" href="help.php"><i class="fa fa-question-circle me-2"></i>Help</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="../logout.php"><i class="fa fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Payments Header Start -->
    <div class="payments-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-5 mb-3">Payment Management</h1>
                    <p class="lead mb-0">Monitor your revenue, track payments, and manage financial transactions</p>
                </div>
                <div class="col-lg-4 text-lg-end">
                    <a href="dashboard.php" class="btn btn-light btn-lg">
                        <i class="fa fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- Payments Header End -->

    <!-- Payment Statistics Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <!-- Overview Statistics -->
            <div class="row g-4 mb-5">
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <i class="fa fa-dollar-sign"></i>
                        </div>
                        <div class="stats-number">Rs. <?php echo number_format($revenue_stats['total_revenue'], 2); ?></div>
                        <div class="stats-label">Total Revenue</div>
                        <div class="stats-change <?php echo $revenue_change >= 0 ? 'change-positive' : 'change-negative'; ?>">
                            <i class="fa fa-arrow-<?php echo $revenue_change >= 0 ? 'up' : 'down'; ?> me-1"></i>
                            <?php echo abs(round($revenue_change, 1)); ?>% vs last month
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                            <i class="fa fa-clock"></i>
                        </div>
                        <div class="stats-number">Rs. <?php echo number_format($pending_stats['pending_amount'], 2); ?></div>
                        <div class="stats-label">Pending Payments</div>
                        <div class="stats-change">
                            <i class="fa fa-list me-1"></i>
                            <?php echo $pending_stats['pending_count']; ?> bookings
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                            <i class="fa fa-check-circle"></i>
                        </div>
                        <div class="stats-number"><?php echo $revenue_stats['total_paid_bookings']; ?></div>
                        <div class="stats-label">Paid Bookings</div>
                        <div class="stats-change change-positive">
                            <i class="fa fa-calendar me-1"></i>
                            All time
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                            <i class="fa fa-chart-line"></i>
                        </div>
                        <div class="stats-number">Rs. <?php echo $comparison['current_month'] > 0 ? number_format($comparison['current_month'], 2) : '0.00'; ?></div>
                        <div class="stats-label">This Month</div>
                        <div class="stats-change change-positive">
                            <i class="fa fa-calendar-alt me-1"></i>
                            Last 30 days
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Payment Statistics -->
            <?php if (!empty($payment_stats) && $payment_stats['total_payments'] > 0): ?>
            <div class="row g-4 mb-5">
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <i class="fa fa-credit-card"></i>
                        </div>
                        <div class="stats-number"><?php echo $payment_stats['total_payments']; ?></div>
                        <div class="stats-label">Total Transactions</div>
                        <div class="stats-change change-positive">
                            <i class="fa fa-receipt me-1"></i>
                            Payment records
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                            <i class="fa fa-check-double"></i>
                        </div>
                        <div class="stats-number">Rs. <?php echo number_format($payment_stats['completed_amount'], 2); ?></div>
                        <div class="stats-label">Completed Payments</div>
                        <div class="stats-change change-positive">
                            <i class="fa fa-money-bill-wave me-1"></i>
                            Successfully processed
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                            <i class="fa fa-hourglass-half"></i>
                        </div>
                        <div class="stats-number">Rs. <?php echo number_format($payment_stats['pending_amount'], 2); ?></div>
                        <div class="stats-label">Processing Payments</div>
                        <div class="stats-change">
                            <i class="fa fa-clock me-1"></i>
                            Awaiting confirmation
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                            <i class="fa fa-percentage"></i>
                        </div>
                        <div class="stats-number"><?php echo $payment_stats['total_amount'] > 0 ? round(($payment_stats['completed_amount'] / $payment_stats['total_amount']) * 100, 1) : 0; ?>%</div>
                        <div class="stats-label">Payment Success Rate</div>
                        <div class="stats-change change-positive">
                            <i class="fa fa-chart-pie me-1"></i>
                            Completion rate
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="content-card">
                        <div class="card-body p-3">
                            <div class="d-flex flex-wrap gap-2">
                                <button class="quick-action-btn btn btn-primary" onclick="location.href='bookings.php'">
                                    <i class="fa fa-calendar-plus me-2"></i>View All Bookings
                                </button>
                                <button class="quick-action-btn btn btn-success" onclick="exportPayments()">
                                    <i class="fa fa-download me-2"></i>Export Payments
                                </button>
                                <button class="quick-action-btn btn btn-info" onclick="location.href='analytics.php'">
                                    <i class="fa fa-chart-bar me-2"></i>Detailed Analytics
                                </button>
                                <button class="quick-action-btn btn btn-warning" onclick="sendPaymentReminders()">
                                    <i class="fa fa-bell me-2"></i>Send Reminders
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Revenue Chart -->
            <div class="row mb-5">
                <div class="col-12">
                    <div class="content-card">
                        <div class="card-header">
                            <h4 class="mb-0"><i class="fa fa-chart-area me-2"></i>Monthly Revenue Trend</h4>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="revenueChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Tables -->
            <div class="row g-4">
                <!-- Recent Payments -->
                <div class="col-lg-6">
                    <div class="content-card">
                        <div class="card-header">
                            <h4 class="mb-0"><i class="fa fa-money-bill-wave me-2"></i>Recent Payments</h4>
                        </div>
                        <div class="card-body p-0">
                            <?php if (empty($recent_payments)): ?>
                                <div class="text-center py-5">
                                    <i class="fa fa-money-bill-wave fa-4x text-muted mb-4"></i>
                                    <h5 class="text-muted">No recent payments</h5>
                                    <p class="text-muted">Payments from the last 30 days will appear here</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($recent_payments as $payment): ?>
                                <div class="payment-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($payment['child_name']); ?></h6>
                                            <small class="text-muted">
                                                <i class="fa fa-user me-1"></i><?php echo htmlspecialchars($payment['parent_name']); ?> •
                                                <i class="fa fa-calendar me-1"></i><?php echo date('M d, Y', strtotime($payment['booking_date'])); ?>
                                            </small>
                                        </div>
                                        <div class="text-end">
                                            <div class="amount-highlight amount-positive">Rs. <?php echo number_format($payment['total_cost'], 2); ?></div>
                                            <span class="payment-status status-paid">Paid</span>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                                <div class="p-3 text-center border-top">
                                    <a href="bookings.php?filter=paid" class="btn btn-outline-primary">
                                        <i class="fa fa-eye me-2"></i>View All Paid Bookings
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Outstanding Payments -->
                <div class="col-lg-6">
                    <div class="content-card">
                        <div class="card-header">
                            <h4 class="mb-0"><i class="fa fa-exclamation-triangle me-2"></i>Outstanding Payments</h4>
                        </div>
                        <div class="card-body p-0">
                            <?php if (empty($outstanding_payments)): ?>
                                <div class="text-center py-5">
                                    <i class="fa fa-check-circle fa-4x text-success mb-4"></i>
                                    <h5 class="text-success">All payments up to date!</h5>
                                    <p class="text-muted">No outstanding payments at this time</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($outstanding_payments as $payment): ?>
                                <div class="payment-item border-start border-3 <?php echo $payment['days_overdue'] > 7 ? 'border-danger' : 'border-warning'; ?>">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <div class="d-flex align-items-center mb-2">
                                                <h6 class="mb-0 me-2"><?php echo htmlspecialchars($payment['child_name']); ?></h6>
                                                <?php if ($payment['days_overdue'] > 0): ?>
                                                    <span class="badge bg-danger">
                                                        <?php echo $payment['days_overdue']; ?> days overdue
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                            <small class="text-muted d-block">
                                                <i class="fa fa-user me-1"></i><?php echo htmlspecialchars($payment['parent_name']); ?>
                                            </small>
                                            <small class="text-muted d-block">
                                                <i class="fa fa-phone me-1"></i><?php echo htmlspecialchars($payment['parent_phone']); ?>
                                            </small>
                                            <small class="text-muted d-block">
                                                <i class="fa fa-calendar me-1"></i><?php echo date('M d, Y', strtotime($payment['booking_date'])); ?>
                                            </small>
                                            <small class="text-muted d-block">
                                                <i class="fa fa-cog me-1"></i><?php echo htmlspecialchars($payment['service_type']); ?>
                                            </small>
                                        </div>
                                        <div class="text-end">
                                            <div class="mb-1">
                                                <small class="text-muted">Total: </small>
                                                <span class="fw-bold">Rs. <?php echo number_format($payment['total_cost'], 2); ?></span>
                                            </div>
                                            <div class="mb-2">
                                                <small class="text-muted">Outstanding: </small>
                                                <span class="amount-highlight amount-pending">Rs. <?php echo number_format($payment['outstanding_amount'], 2); ?></span>
                                            </div>
                                            <div class="d-flex gap-1">
                                                <button class="btn btn-warning btn-sm" onclick="sendPaymentReminder(<?php echo $payment['booking_id']; ?>)" title="Send Reminder">
                                                    <i class="fa fa-bell"></i>
                                                </button>
                                                <button class="btn btn-info btn-sm" onclick="viewPaymentDetails(<?php echo $payment['booking_id']; ?>)" title="View Details">
                                                    <i class="fa fa-eye"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                                <div class="p-3 text-center border-top">
                                    <button class="btn btn-warning" onclick="sendPaymentReminders()">
                                        <i class="fa fa-bell me-2"></i>Send Payment Reminders
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Detailed Payment Tracking -->
    <?php if (!empty($recent_payments_detailed)): ?>
    <div class="container-xxl py-5">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="content-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4 class="mb-0"><i class="fa fa-credit-card me-2"></i>Detailed Payment Tracking</h4>
                            <small class="text-muted">Recent transactions with card details</small>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-primary">
                                        <tr>
                                            <th>Date</th>
                                            <th>Parent</th>
                                            <th>Service</th>
                                            <th>Amount</th>
                                            <th>Payment Method</th>
                                            <th>Transaction ID</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_payments_detailed as $payment): ?>
                                        <tr>
                                            <td>
                                                <div class="fw-bold"><?php echo date('M d, Y', strtotime($payment['payment_date'])); ?></div>
                                                <small class="text-muted"><?php echo date('h:i A', strtotime($payment['payment_date'])); ?></small>
                                            </td>
                                            <td>
                                                <div class="fw-bold"><?php echo htmlspecialchars($payment['first_name'] . ' ' . $payment['last_name']); ?></div>
                                                <small class="text-muted"><?php echo htmlspecialchars($payment['email']); ?></small>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($payment['phone']); ?></small>
                                            </td>
                                            <td>
                                                <div class="fw-bold"><?php echo ucfirst($payment['service_type']); ?></div>
                                                <small class="text-muted"><?php echo date('M d', strtotime($payment['booking_date'])); ?>
                                                (<?php echo date('h:i A', strtotime($payment['start_time'])); ?> - <?php echo date('h:i A', strtotime($payment['end_time'])); ?>)</small>
                                            </td>
                                            <td>
                                                <div class="fw-bold text-success">LKR <?php echo number_format($payment['amount'], 2); ?></div>
                                                <small class="text-muted"><?php echo ucfirst($payment['payment_type']); ?> payment</small>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="fa fa-credit-card me-2 text-primary"></i>
                                                    <div>
                                                        <?php if (!empty($payment['card_last_four'])): ?>
                                                            <div class="fw-bold">**** <?php echo $payment['card_last_four']; ?></div>
                                                            <small class="text-muted"><?php echo htmlspecialchars($payment['card_holder_name']); ?></small>
                                                        <?php else: ?>
                                                            <div class="text-muted">Online Payment</div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <code class="small"><?php echo htmlspecialchars($payment['transaction_id']); ?></code>
                                            </td>
                                            <td>
                                                <span class="payment-status status-<?php echo $payment['payment_status']; ?>">
                                                    <?php echo ucfirst($payment['payment_status']); ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Comprehensive Payment History -->
    <div class="container-xxl py-5">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="content-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4 class="mb-0"><i class="fa fa-history me-2"></i>Complete Payment History</h4>
                            <div class="d-flex gap-2">
                                <select class="form-select form-select-sm" id="paymentFilter" style="width: auto;">
                                    <option value="all">All Payments</option>
                                    <option value="paid">Paid Only</option>
                                    <option value="pending">Pending Only</option>
                                    <option value="partial">Partial Payments</option>
                                </select>
                                <button class="btn btn-success btn-sm" onclick="exportPaymentHistory()">
                                    <i class="fa fa-download me-1"></i>Export
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0" id="paymentHistoryTable">
                                    <thead class="table-primary">
                                        <tr>
                                            <th>Booking ID</th>
                                            <th>Date</th>
                                            <th>Parent Details</th>
                                            <th>Child</th>
                                            <th>Service</th>
                                            <th>Total Amount</th>
                                            <th>Paid Amount</th>
                                            <th>Outstanding</th>
                                            <th>Payment Status</th>
                                            <th>Payment Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        // Get comprehensive payment history - using safe column references
                                        try {
                                            $payment_history_stmt = $pdo->prepare("
                                                SELECT
                                                    b.*,
                                                    p.name as parent_name,
                                                    p.email as parent_email,
                                                    p.phone as parent_phone,
                                                    pay.payment_date,
                                                    pay.card_last_four as card_number,
                                                    pay.transaction_id,
                                                    CASE
                                                        WHEN b.payment_status = 'paid' THEN b.total_cost
                                                        ELSE 0
                                                    END as paid_amount,
                                                    CASE
                                                        WHEN b.payment_status = 'pending' THEN b.total_cost
                                                        ELSE 0
                                                    END as outstanding_amount
                                                FROM bookings b
                                                JOIN parents p ON b.parent_id = p.parent_id
                                                LEFT JOIN payments pay ON b.booking_id = pay.booking_id
                                                WHERE b.provider_id = ? AND b.status IN ('confirmed', 'completed')
                                                ORDER BY b.booking_date DESC, b.created_at DESC
                                            ");
                                            $payment_history_stmt->execute([$provider_id]);
                                            $payment_history = $payment_history_stmt->fetchAll();
                                        } catch (Exception $e) {
                                            error_log("Payment history query error: " . $e->getMessage());
                                            $payment_history = [];
                                        }
                                        ?>

                                        <?php if (empty($payment_history)): ?>
                                            <tr>
                                                <td colspan="11" class="text-center py-5">
                                                    <i class="fa fa-credit-card fa-3x text-muted mb-3"></i>
                                                    <h5 class="text-muted">No payment history found</h5>
                                                    <p class="text-muted">Payment records will appear here once bookings are confirmed</p>
                                                </td>
                                            </tr>
                                        <?php else: ?>
                                            <?php foreach ($payment_history as $payment): ?>
                                            <tr class="payment-row" data-status="<?php echo $payment['payment_status']; ?>">
                                                <td>
                                                    <strong>#<?php echo $payment['booking_id']; ?></strong>
                                                </td>
                                                <td>
                                                    <div class="fw-bold"><?php echo date('M d, Y', strtotime($payment['booking_date'])); ?></div>
                                                    <small class="text-muted"><?php echo date('g:i A', strtotime($payment['booking_date'])); ?></small>
                                                </td>
                                                <td>
                                                    <div class="fw-bold"><?php echo htmlspecialchars($payment['parent_name']); ?></div>
                                                    <small class="text-muted d-block">
                                                        <i class="fa fa-envelope me-1"></i><?php echo htmlspecialchars($payment['parent_email']); ?>
                                                    </small>
                                                    <small class="text-muted d-block">
                                                        <i class="fa fa-phone me-1"></i><?php echo htmlspecialchars($payment['parent_phone']); ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <div class="fw-bold"><?php echo htmlspecialchars($payment['child_name']); ?></div>
                                                    <small class="text-muted"><?php echo htmlspecialchars($payment['child_age']); ?> years old</small>
                                                </td>
                                                <td>
                                                    <div class="fw-bold"><?php echo htmlspecialchars($payment['service_type']); ?></div>
                                                    <small class="text-muted"><?php echo htmlspecialchars($payment['duration']); ?></small>
                                                </td>
                                                <td>
                                                    <div class="fw-bold text-primary">Rs. <?php echo number_format($payment['total_cost'], 2); ?></div>
                                                </td>
                                                <td>
                                                    <div class="fw-bold text-success">Rs. <?php echo number_format($payment['paid_amount'], 2); ?></div>
                                                </td>
                                                <td>
                                                    <div class="fw-bold <?php echo $payment['outstanding_amount'] > 0 ? 'text-danger' : 'text-muted'; ?>">
                                                        Rs. <?php echo number_format($payment['outstanding_amount'], 2); ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="payment-status status-<?php echo $payment['payment_status']; ?>">
                                                        <?php echo ucfirst($payment['payment_status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($payment['payment_date']): ?>
                                                        <div class="fw-bold"><?php echo date('M d, Y', strtotime($payment['payment_date'])); ?></div>
                                                        <small class="text-muted"><?php echo date('g:i A', strtotime($payment['payment_date'])); ?></small>
                                                    <?php else: ?>
                                                        <span class="text-muted">Not paid</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group-vertical btn-group-sm">
                                                        <?php if ($payment['outstanding_amount'] > 0): ?>
                                                            <button class="btn btn-warning btn-sm" onclick="sendPaymentReminder(<?php echo $payment['booking_id']; ?>)">
                                                                <i class="fa fa-bell"></i> Remind
                                                            </button>
                                                        <?php endif; ?>
                                                        <button class="btn btn-info btn-sm" onclick="viewPaymentDetails(<?php echo $payment['booking_id']; ?>)">
                                                            <i class="fa fa-eye"></i> Details
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Comprehensive Payment History End -->

    <!-- Payment Statistics End -->

    <!-- Footer Start -->
    <div class="container-fluid bg-dark text-light footer pt-5 mt-5 wow fadeIn" data-wow-delay="0.1s">
        <div class="container py-5">
            <div class="row g-5">
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3"><i class="fa fa-baby me-2"></i>Nestlings</h4>
                    <p>Connecting families with trusted childcare providers across Sri Lanka. Safe, verified, and reliable daycare solutions for your little ones.</p>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Quick Links</h4>
                    <a class="btn btn-link" href="profile.php">My Profile</a>
                    <a class="btn btn-link" href="bookings.php">Manage Bookings</a>
                    <a class="btn btn-link" href="analytics.php">Analytics</a>
                    <a class="btn btn-link" href="payments.php">Payments</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Contact Support</h4>
                    <p class="mb-2"><i class="fa fa-map-marker-alt me-3"></i>123 Main Street, Colombo, Sri Lanka</p>
                    <p class="mb-2"><i class="fa fa-phone-alt me-3"></i>+94 11 234 5678</p>
                    <p class="mb-2"><i class="fa fa-envelope me-3"></i><EMAIL></p>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Business Hours</h4>
                    <p class="mb-2">Monday - Friday: 8:00 AM - 6:00 PM</p>
                    <p class="mb-2">Saturday: 9:00 AM - 4:00 PM</p>
                    <p class="mb-2">Sunday: Closed</p>
                </div>
            </div>
        </div>
        <div class="container">
            <div class="copyright">
                <div class="row">
                    <div class="col-md-6 text-center text-md-start mb-3 mb-md-0">
                        &copy; <a class="border-bottom" href="#">Nestlings</a>, All Right Reserved. Designed By <a class="border-bottom" href="#">Nestlings Team</a>
                    </div>
                    <div class="col-md-6 text-center text-md-end">
                        <div class="footer-menu">
                            <a href="../index.html">Home</a>
                            <a href="../about.html">About</a>
                            <a href="../contact.html">Contact</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Footer End -->

    <!-- Back to Top -->
    <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/owlcarousel/owl.carousel.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>

    <!-- Custom Payments JavaScript -->
    <script>
        // Revenue Chart
        const ctx = document.getElementById('revenueChart').getContext('2d');
        const monthlyData = <?php echo json_encode(array_values($monthly_data)); ?>;
        const monthLabels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: monthLabels,
                datasets: [{
                    label: 'Monthly Revenue (Rs.)',
                    data: monthlyData,
                    borderColor: 'rgb(102, 126, 234)',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: 'rgb(102, 126, 234)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'Rs. ' + value.toLocaleString();
                            }
                        }
                    }
                },
                elements: {
                    point: {
                        hoverBackgroundColor: 'rgb(102, 126, 234)'
                    }
                }
            }
        });

        // Export Payments Function
        function exportPayments() {
            // Create CSV content
            let csvContent = "data:text/csv;charset=utf-8,";
            csvContent += "Date,Child Name,Parent Name,Service Type,Amount,Payment Status,Booking Status\n";

            // Add data rows (you would fetch this from PHP or make an AJAX call)
            <?php foreach (array_merge($recent_payments, $outstanding_payments) as $payment): ?>
            csvContent += "<?php echo date('Y-m-d', strtotime($payment['booking_date'])); ?>,";
            csvContent += "<?php echo addslashes($payment['child_name']); ?>,";
            csvContent += "<?php echo addslashes($payment['parent_name']); ?>,";
            csvContent += "<?php echo addslashes($payment['service_type']); ?>,";
            csvContent += "<?php echo $payment['total_cost']; ?>,";
            csvContent += "<?php echo $payment['payment_status']; ?>,";
            csvContent += "<?php echo $payment['status']; ?>\n";
            <?php endforeach; ?>

            // Create download link
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "payments_export_" + new Date().toISOString().split('T')[0] + ".csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Send Payment Reminders Function
        function sendPaymentReminders() {
            if (confirm('Send payment reminders to all parents with outstanding payments?')) {
                // You would implement this with an AJAX call to a PHP script
                alert('Payment reminders sent successfully!');
            }
        }

        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Auto-refresh data every 5 minutes
        setTimeout(function() {
            location.reload();
        }, 300000);

        // Payment filter functionality
        document.getElementById('paymentFilter').addEventListener('change', function() {
            const filterValue = this.value;
            const rows = document.querySelectorAll('#paymentHistoryTable .payment-row');

            rows.forEach(row => {
                if (filterValue === 'all' || row.dataset.status === filterValue) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });

        // Send payment reminder for specific booking
        function sendPaymentReminder(bookingId) {
            if (confirm('Send payment reminder to the parent?')) {
                fetch('send_payment_reminder.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        booking_id: bookingId,
                        action: 'send_reminder'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Payment reminder sent successfully!');
                    } else {
                        alert('Error sending reminder: ' + (data.message || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error sending reminder. Please try again.');
                });
            }
        }

        // Send payment reminders to all outstanding payments
        function sendPaymentReminders() {
            if (confirm('Send payment reminders to all parents with outstanding payments?')) {
                fetch('send_payment_reminder.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'send_all_reminders'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`Payment reminders sent to ${data.count} parents successfully!`);
                    } else {
                        alert('Error sending reminders: ' + (data.message || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error sending reminders. Please try again.');
                });
            }
        }

        // View payment details
        function viewPaymentDetails(bookingId) {
            window.open(`booking_details.php?id=${bookingId}`, '_blank');
        }

        // Export payment history
        function exportPaymentHistory() {
            // Create CSV content
            let csvContent = "data:text/csv;charset=utf-8,";
            csvContent += "Booking ID,Date,Parent Name,Child Name,Service Type,Total Amount,Paid Amount,Outstanding,Payment Status,Payment Date\n";

            // Get visible rows only (respecting filter)
            const visibleRows = document.querySelectorAll('#paymentHistoryTable .payment-row:not([style*="display: none"])');

            visibleRows.forEach(row => {
                const cells = row.querySelectorAll('td');
                const rowData = [
                    cells[0].textContent.trim(),
                    cells[1].textContent.trim().replace(/\n/g, ' '),
                    cells[2].textContent.trim().replace(/\n/g, ' '),
                    cells[3].textContent.trim().replace(/\n/g, ' '),
                    cells[4].textContent.trim().replace(/\n/g, ' '),
                    cells[5].textContent.trim(),
                    cells[6].textContent.trim(),
                    cells[7].textContent.trim(),
                    cells[8].textContent.trim(),
                    cells[9].textContent.trim().replace(/\n/g, ' ')
                ];
                csvContent += rowData.map(field => `"${field}"`).join(',') + '\n';
            });

            // Download CSV
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", `payment_history_${new Date().toISOString().split('T')[0]}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>
</body>

</html>
