<?php
require_once 'config/database.php';

echo "<h2>Payment System Migration</h2>";

try {
    // Check if payments table exists
    $check_table = $pdo->query("SHOW TABLES LIKE 'payments'");
    $payments_table_exists = $check_table->rowCount() > 0;
    
    if (!$payments_table_exists) {
        echo "<p>Creating payments table...</p>";
        
        // Create payments table
        $pdo->exec("CREATE TABLE payments (
            payment_id INT AUTO_INCREMENT PRIMARY KEY,
            booking_id INT NOT NULL,
            parent_id INT NOT NULL,
            provider_id INT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            payment_type ENUM('partial', 'full') NOT NULL DEFAULT 'full',
            payment_status ENUM('pending', 'completed', 'failed') NOT NULL DEFAULT 'pending',
            payment_date DATETIME NOT NULL,
            transaction_id VARCHAR(100) UNIQUE NOT NULL,
            card_last_four VARCHAR(4) NULL,
            card_holder_name VARCHAR(100) NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (booking_id) REFERENCES bookings(booking_id) ON DELETE CASCADE,
            FOREIGN KEY (parent_id) REFERENCES parents(parent_id) ON DELETE CASCADE,
            FOREIGN KEY (provider_id) REFERENCES providers(provider_id) ON DELETE CASCADE,
            INDEX idx_payments_booking (booking_id),
            INDEX idx_payments_parent (parent_id),
            INDEX idx_payments_provider (provider_id),
            INDEX idx_payments_status (payment_status)
        )");
        
        echo "<p style='color: green;'>✓ Payments table created successfully!</p>";
    } else {
        echo "<p style='color: green;'>✓ Payments table already exists</p>";

        // Check if card columns exist and add them if missing
        $check_columns = $pdo->query("SHOW COLUMNS FROM payments LIKE 'card_last_four'")->rowCount();
        if ($check_columns == 0) {
            echo "<p>Adding missing card detail columns...</p>";
            $pdo->exec("ALTER TABLE payments
                        ADD COLUMN card_last_four VARCHAR(4) NULL,
                        ADD COLUMN card_holder_name VARCHAR(100) NULL");
            echo "<p style='color: green;'>✓ Card detail columns added!</p>";
        }
    }
    
    // Check if bookings table has payment columns
    $booking_columns = $pdo->query("SHOW COLUMNS FROM bookings LIKE 'payment_status'")->rowCount();
    
    if ($booking_columns == 0) {
        echo "<p>Adding payment columns to bookings table...</p>";
        
        // Add payment tracking columns to bookings
        $pdo->exec("ALTER TABLE bookings 
                    ADD COLUMN payment_status ENUM('unpaid', 'partial', 'paid') NOT NULL DEFAULT 'unpaid',
                    ADD COLUMN paid_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
                    ADD COLUMN remaining_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00");
        
        // Update existing bookings to set remaining_amount = total_cost
        $pdo->exec("UPDATE bookings SET remaining_amount = total_cost WHERE remaining_amount = 0");
        
        echo "<p style='color: green;'>✓ Payment columns added to bookings table!</p>";
    } else {
        echo "<p style='color: green;'>✓ Bookings table already has payment columns</p>";
    }
    
    echo "<h3>Migration Complete!</h3>";
    echo "<p><a href='Parent/payment_confirmation.php?booking_id=1' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Payment</a></p>";
    echo "<p><a href='daycare/payments.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Daycare Payments</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
