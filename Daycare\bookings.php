<?php
session_start();

// Check if user is logged in and is a provider
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'provider') {
    header("Location: ../login.php");
    exit();
}

require_once '../config/database.php';

// Get provider information
$stmt = $pdo->prepare("SELECT * FROM providers WHERE provider_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$provider = $stmt->fetch();

if (!$provider) {
    session_destroy();
    header("Location: ../login.php");
    exit();
}

// Handle booking status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $booking_id = (int)$_POST['booking_id'];
    $action = $_POST['action'];
    
    // Verify booking belongs to this provider
    $stmt = $pdo->prepare("SELECT * FROM bookings WHERE booking_id = ? AND provider_id = ?");
    $stmt->execute([$booking_id, $_SESSION['user_id']]);
    $booking = $stmt->fetch();
    
    if ($booking) {
        $new_status = '';
        switch ($action) {
            case 'confirm':
                $new_status = 'confirmed';
                break;
            case 'complete':
                $new_status = 'completed';
                break;
            case 'cancel':
                $new_status = 'cancelled';
                break;
        }
        
        if ($new_status) {
            $stmt = $pdo->prepare("UPDATE bookings SET status = ?, updated_at = NOW() WHERE booking_id = ?");
            $stmt->execute([$new_status, $booking_id]);

            // Create notification for parent
            $notification_title = '';
            $notification_message = '';

            switch ($new_status) {
                case 'confirmed':
                    $notification_title = 'Booking Confirmed';
                    $notification_message = "Your booking for " . htmlspecialchars($booking['child_name']) . " on " . date('M d, Y', strtotime($booking['booking_date'])) . " has been confirmed by " . htmlspecialchars($provider['business_name']);
                    break;
                case 'completed':
                    $notification_title = 'Booking Completed';
                    $notification_message = "Your booking for " . htmlspecialchars($booking['child_name']) . " on " . date('M d, Y', strtotime($booking['booking_date'])) . " has been completed. You can now leave a review.";
                    break;
                case 'cancelled':
                    $notification_title = 'Booking Cancelled';
                    $notification_message = "Your booking for " . htmlspecialchars($booking['child_name']) . " on " . date('M d, Y', strtotime($booking['booking_date'])) . " has been cancelled by " . htmlspecialchars($provider['business_name']);
                    break;
            }

            if ($notification_title) {
                $notification_stmt = $pdo->prepare("
                    INSERT INTO notifications (user_type, user_id, title, message, type, created_at)
                    VALUES ('parent', ?, ?, ?, 'booking', NOW())
                ");
                $notification_stmt->execute([$booking['parent_id'], $notification_title, $notification_message]);
            }

            $success_message = "Booking status updated successfully!";
        }
    }
}

// Get filter parameters
$status_filter = $_GET['status'] ?? 'all';
$date_filter = $_GET['date'] ?? 'all';

// Build query based on filters
$where_conditions = ["b.provider_id = ?"];
$params = [$_SESSION['user_id']];

if ($status_filter !== 'all') {
    $where_conditions[] = "b.status = ?";
    $params[] = $status_filter;
}

if ($date_filter !== 'all') {
    switch ($date_filter) {
        case 'today':
            $where_conditions[] = "DATE(b.booking_date) = CURDATE()";
            break;
        case 'week':
            $where_conditions[] = "b.booking_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)";
            break;
        case 'month':
            $where_conditions[] = "MONTH(b.booking_date) = MONTH(CURDATE()) AND YEAR(b.booking_date) = YEAR(CURDATE())";
            break;
    }
}

$where_clause = implode(' AND ', $where_conditions);

// Get bookings with parent information
$stmt = $pdo->prepare("
    SELECT b.*, p.name as parent_name, p.phone as parent_phone, p.email as parent_email
    FROM bookings b
    JOIN parents p ON b.parent_id = p.parent_id
    WHERE $where_clause
    ORDER BY b.booking_date DESC, b.created_at DESC
");
$stmt->execute($params);
$bookings = $stmt->fetchAll();

// Group bookings by status for easy display
$grouped_bookings = [
    'pending' => [],
    'confirmed' => [],
    'completed' => [],
    'cancelled' => []
];

foreach ($bookings as $booking) {
    $grouped_bookings[$booking['status']][] = $booking;
}

// Get booking statistics
$stats = [];
foreach (['pending', 'confirmed', 'completed', 'cancelled'] as $status) {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM bookings WHERE provider_id = ? AND status = ?");
    $stmt->execute([$_SESSION['user_id'], $status]);
    $stats[$status] = $stmt->fetchColumn();
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>Manage Bookings - Nestlings</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="" name="keywords">
    <meta content="" name="description">

    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600&family=Nunito:wght@600;700;800&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">
    <link href="../lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        .bookings-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
            transition: all 0.3s ease;
            height: 100%;
            text-align: center;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .stats-label {
            color: #7f8c8d;
            font-weight: 500;
            text-transform: uppercase;
            font-size: 0.85rem;
            letter-spacing: 0.5px;
        }
        
        .content-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            border: none;
            margin-bottom: 30px;
        }
        
        .content-card .card-header {
            background: linear-gradient(135deg, var(--primary) 0%, #6BB6FF 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            border: none;
            padding: 20px 25px;
        }
        
        .user-dropdown {
            border-radius: 25px;
            padding: 8px 20px;
            background: linear-gradient(135deg, var(--primary) 0%, #6BB6FF 100%);
            border: none;
            color: white;
            font-weight: 500;
        }
        
        .user-dropdown:hover {
            background: linear-gradient(135deg, #6BB6FF 0%, var(--primary) 100%);
            color: white;
        }
        
        .booking-card {
            border: 1px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            background: white;
        }
        
        .booking-card:hover {
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .status-badge {
            padding: 6px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-confirmed {
            background: #d4edda;
            color: #155724;
        }
        
        .status-completed {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .status-cancelled {
            background: #f8d7da;
            color: #721c24;
        }
        
        .filter-tabs {
            background: white;
            border-radius: 15px;
            padding: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 30px;
        }
        
        .filter-tabs .nav-link {
            border-radius: 10px;
            font-weight: 600;
            color: #6c757d;
            border: none;
            padding: 12px 20px;
            margin: 0 5px;
        }
        
        .filter-tabs .nav-link.active {
            background: linear-gradient(135deg, var(--primary) 0%, #6BB6FF 100%);
            color: white;
        }
        
        .action-btn {
            border-radius: 8px;
            padding: 8px 16px;
            font-size: 0.85rem;
            font-weight: 600;
            border: none;
            margin: 2px;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .booking-info {
            border-left: 4px solid var(--primary);
            padding-left: 15px;
        }
        
        .parent-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
        }
    </style>
</head>

<body>
    <!-- Spinner Start -->
    <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>
    <!-- Spinner End -->

    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light shadow sticky-top p-0">
        <a href="../index.html" class="navbar-brand d-flex align-items-center px-4 px-lg-5">
            <h2 class="m-0 text-primary"><i class="fa fa-baby me-3"></i>Nestlings</h2>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="dashboard.php" class="nav-item nav-link">Dashboard</a>
                <a href="bookings.php" class="nav-item nav-link active">Bookings</a>
                <a href="profile.php" class="nav-item nav-link">Profile</a>
                <a href="analytics.php" class="nav-item nav-link">Analytics</a>
                <a href="management.php" class="nav-item nav-link">Management</a>
                <a href="payments.php" class="nav-item nav-link">Payments</a>
            </div>
            <div class="dropdown">
                <button class="btn btn-primary dropdown-toggle me-4" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fa fa-user me-2"></i>
                    <span><?php echo htmlspecialchars($provider['business_name']); ?></span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="profile.php"><i class="fa fa-building me-2"></i>Business Profile</a></li>
                    <li><a class="dropdown-item" href="settings.php"><i class="fa fa-cog me-2"></i>Settings</a></li>
                    <li><a class="dropdown-item" href="help.php"><i class="fa fa-question-circle me-2"></i>Help</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="../logout.php"><i class="fa fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Bookings Header Start -->
    <div class="bookings-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-5 mb-3">Manage Bookings</h1>
                    <p class="lead mb-0">Review and manage booking requests from parents</p>
                </div>
                <div class="col-lg-4 text-lg-end">
                    <a href="dashboard.php" class="btn btn-light btn-lg">
                        <i class="fa fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- Bookings Header End -->

    <!-- Bookings Content Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fa fa-check-circle me-2"></i><?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Statistics Cards -->
            <div class="row g-4 mb-5">
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-number text-warning"><?php echo $stats['pending']; ?></div>
                        <div class="stats-label">Pending Requests</div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-number text-success"><?php echo $stats['confirmed']; ?></div>
                        <div class="stats-label">Confirmed Bookings</div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-number text-info"><?php echo $stats['completed']; ?></div>
                        <div class="stats-label">Completed</div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-number text-danger"><?php echo $stats['cancelled']; ?></div>
                        <div class="stats-label">Cancelled</div>
                    </div>
                </div>
            </div>

            <!-- Filter Tabs -->
            <div class="filter-tabs">
                <ul class="nav nav-pills justify-content-center">
                    <li class="nav-item">
                        <a class="nav-link <?php echo $status_filter === 'all' ? 'active' : ''; ?>" 
                           href="?status=all&date=<?php echo $date_filter; ?>">All Bookings</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $status_filter === 'pending' ? 'active' : ''; ?>" 
                           href="?status=pending&date=<?php echo $date_filter; ?>">Pending</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $status_filter === 'confirmed' ? 'active' : ''; ?>" 
                           href="?status=confirmed&date=<?php echo $date_filter; ?>">Confirmed</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $status_filter === 'completed' ? 'active' : ''; ?>" 
                           href="?status=completed&date=<?php echo $date_filter; ?>">Completed</a>
                    </li>
                </ul>
            </div>

            <!-- Bookings List -->
            <div class="row">
                <div class="col-12">
                    <?php if (empty($bookings)): ?>
                        <div class="content-card card">
                            <div class="card-body text-center py-5">
                                <i class="fa fa-calendar fa-4x text-muted mb-4"></i>
                                <h4 class="text-muted">No bookings found</h4>
                                <p class="text-muted">
                                    <?php if ($status_filter !== 'all'): ?>
                                        No <?php echo $status_filter; ?> bookings at the moment.
                                    <?php else: ?>
                                        You haven't received any booking requests yet. Once parents start booking your services, they will appear here.
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($bookings as $booking): ?>
                            <div class="booking-card">
                                <div class="row align-items-center">
                                    <div class="col-lg-8">
                                        <div class="booking-info">
                                            <div class="d-flex justify-content-between align-items-start mb-3">
                                                <div>
                                                    <h5 class="mb-1"><?php echo htmlspecialchars($booking['child_name']); ?></h5>
                                                    <p class="text-muted mb-0">
                                                        <i class="fa fa-calendar me-2"></i>
                                                        <?php echo date('l, F j, Y', strtotime($booking['booking_date'])); ?>
                                                        <span class="ms-3">
                                                            <i class="fa fa-clock me-2"></i>
                                                            <?php echo date('g:i A', strtotime($booking['start_time'])); ?> - 
                                                            <?php echo date('g:i A', strtotime($booking['end_time'])); ?>
                                                        </span>
                                                    </p>
                                                </div>
                                                <span class="status-badge status-<?php echo $booking['status']; ?>">
                                                    <?php echo ucfirst($booking['status']); ?>
                                                </span>
                                            </div>
                                            
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <p class="mb-1"><strong>Service:</strong> <?php echo htmlspecialchars($booking['service_type']); ?></p>
                                                    <p class="mb-1"><strong>Child Age:</strong> <?php echo $booking['child_age']; ?> years</p>
                                                </div>
                                                <div class="col-md-6">
                                                    <p class="mb-1"><strong>Duration:</strong> <?php echo $booking['duration']; ?> hours</p>
                                                    <p class="mb-1"><strong>Total Cost:</strong> LKR <?php echo number_format($booking['total_cost'], 2); ?></p>
                                                </div>
                                            </div>
                                            
                                            <!-- Contact Numbers -->
                                            <div class="mt-2">
                                                <strong>Contact Numbers:</strong>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <p class="mb-1 text-muted">
                                                            <i class="fa fa-phone me-2"></i>Primary:
                                                            <a href="tel:<?php echo $booking['contact_number_1']; ?>" class="text-decoration-none">
                                                                <?php echo htmlspecialchars($booking['contact_number_1']); ?>
                                                            </a>
                                                        </p>
                                                    </div>
                                                    <?php if ($booking['contact_number_2']): ?>
                                                    <div class="col-md-6">
                                                        <p class="mb-1 text-muted">
                                                            <i class="fa fa-phone me-2"></i>Secondary:
                                                            <a href="tel:<?php echo $booking['contact_number_2']; ?>" class="text-decoration-none">
                                                                <?php echo htmlspecialchars($booking['contact_number_2']); ?>
                                                            </a>
                                                        </p>
                                                    </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>

                                            <?php if ($booking['special_requirements']): ?>
                                                <div class="mt-2">
                                                    <strong>Special Requirements:</strong>
                                                    <p class="text-muted mb-0"><?php echo htmlspecialchars($booking['special_requirements']); ?></p>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="parent-info">
                                            <h6 class="mb-2"><i class="fa fa-user me-2"></i>Parent Information</h6>
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <strong>Name:</strong> <?php echo htmlspecialchars($booking['parent_name']); ?>
                                                </div>
                                                <div class="col-md-4">
                                                    <strong>Phone:</strong> <?php echo htmlspecialchars($booking['parent_phone']); ?>
                                                </div>
                                                <div class="col-md-4">
                                                    <strong>Email:</strong> <?php echo htmlspecialchars($booking['parent_email']); ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-lg-4 text-lg-end">
                                        <div class="d-flex flex-column gap-2">
                                            <?php if ($booking['status'] === 'pending'): ?>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="booking_id" value="<?php echo $booking['booking_id']; ?>">
                                                    <input type="hidden" name="action" value="confirm">
                                                    <button type="submit" class="btn btn-success action-btn w-100">
                                                        <i class="fa fa-check me-2"></i>Confirm Booking
                                                    </button>
                                                </form>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="booking_id" value="<?php echo $booking['booking_id']; ?>">
                                                    <input type="hidden" name="action" value="cancel">
                                                    <button type="submit" class="btn btn-danger action-btn w-100" 
                                                            onclick="return confirm('Are you sure you want to cancel this booking?')">
                                                        <i class="fa fa-times me-2"></i>Cancel Booking
                                                    </button>
                                                </form>
                                            <?php elseif ($booking['status'] === 'confirmed'): ?>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="booking_id" value="<?php echo $booking['booking_id']; ?>">
                                                    <input type="hidden" name="action" value="complete">
                                                    <button type="submit" class="btn btn-primary action-btn w-100">
                                                        <i class="fa fa-check-circle me-2"></i>Mark Complete
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                            
                                            <a href="tel:<?php echo $booking['parent_phone']; ?>" class="btn btn-outline-primary action-btn w-100">
                                                <i class="fa fa-phone me-2"></i>Call Parent
                                            </a>
                                            <a href="mailto:<?php echo $booking['parent_email']; ?>" class="btn btn-outline-secondary action-btn w-100">
                                                <i class="fa fa-envelope me-2"></i>Email Parent
                                            </a>
                                        </div>
                                        
                                        <div class="mt-3 text-muted small">
                                            <i class="fa fa-clock me-1"></i>
                                            Requested: <?php echo date('M j, Y g:i A', strtotime($booking['created_at'])); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <!-- Bookings Content End -->

    <!-- Footer Start -->
    <div class="container-fluid bg-dark text-light footer pt-5 mt-5 wow fadeIn" data-wow-delay="0.1s">
        <div class="container py-5">
            <div class="row g-5">
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3"><i class="fa fa-baby me-2"></i>Nestlings</h4>
                    <p>Connecting families with trusted childcare providers across Sri Lanka. Safe, verified, and reliable daycare solutions for your little ones.</p>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Provider Resources</h4>
                    <a class="btn btn-link" href="help.php">Help Center</a>
                    <a class="btn btn-link" href="">Safety Guidelines</a>
                    <a class="btn btn-link" href="">Best Practices</a>
                    <a class="btn btn-link" href="">Training Materials</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Quick Links</h4>
                    <a class="btn btn-link" href="profile.php">My Profile</a>
                    <a class="btn btn-link" href="bookings.php">Manage Bookings</a>
                    <a class="btn btn-link" href="analytics.php">Analytics</a>
                    <a class="btn btn-link" href="documents.php">Documents</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Contact Support</h4>
                    <p class="mb-2"><i class="fa fa-map-marker-alt me-3"></i>123 Main Street, Colombo, Sri Lanka</p>
                    <p class="mb-2"><i class="fa fa-phone-alt me-3"></i>+94 11 234 5678</p>
                    <p class="mb-2"><i class="fa fa-envelope me-3"></i><EMAIL></p>
                </div>
            </div>
        </div>
    </div>
    <!-- Footer End -->

    <!-- Back to Top -->
    <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/owlcarousel/owl.carousel.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>
</body>

</html>
