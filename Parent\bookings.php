<?php
session_start();

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user is logged in and is a parent
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'parent') {
    header("Location: ../login.php");
    exit();
}

try {
    require_once '../config/database.php';
} catch (Exception $e) {
    die("Database connection error: " . $e->getMessage());
}

// Get parent information
try {
    $stmt = $pdo->prepare("SELECT * FROM parents WHERE parent_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $parent = $stmt->fetch();

    if (!$parent) {
        die("Parent not found. Please contact support.");
    }
} catch (Exception $e) {
    die("Error fetching parent information: " . $e->getMessage());
}

// Check if payment columns exist in bookings table
$payment_columns_exist = false;
try {
    $check_columns = $pdo->query("SHOW COLUMNS FROM bookings LIKE 'payment_status'");
    $payment_columns_exist = $check_columns->rowCount() > 0;
} catch (Exception $e) {
    $payment_columns_exist = false;
}

// Get bookings with provider information including payment data
try {
    $stmt = $pdo->prepare("
        SELECT b.*, p.business_name, p.district, p.phone as provider_phone
        FROM bookings b
        JOIN providers p ON b.provider_id = p.provider_id
        WHERE b.parent_id = ?
        ORDER BY b.booking_date DESC, b.created_at DESC
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $bookings = $stmt->fetchAll();

    // Initialize payment fields for bookings that don't have them set
    foreach ($bookings as &$booking) {
        // Only set defaults if the fields are null or missing
        if (!isset($booking['payment_status']) || $booking['payment_status'] === null) {
            $booking['payment_status'] = 'unpaid';
        }
        if (!isset($booking['paid_amount']) || $booking['paid_amount'] === null) {
            $booking['paid_amount'] = 0;
        }
        if (!isset($booking['remaining_amount']) || $booking['remaining_amount'] === null) {
            $booking['remaining_amount'] = $booking['total_cost'] ?? 0;
        }
    }
    unset($booking); // Break reference

} catch (Exception $e) {
    // Show detailed error for debugging
    echo "<div style='background: red; color: white; padding: 20px; margin: 20px;'>";
    echo "Database Error: " . $e->getMessage();
    echo "<br>File: " . $e->getFile();
    echo "<br>Line: " . $e->getLine();
    echo "</div>";
    $bookings = []; // Set empty array to continue
}

// Group bookings by status
$grouped_bookings = [
    'pending' => [],
    'confirmed' => [],
    'completed' => [],
    'cancelled' => []
];

foreach ($bookings as $booking) {
    $grouped_bookings[$booking['status']][] = $booking;
}

// Debug: PHP processing completed successfully
echo "<!-- PHP processing completed. Bookings count: " . count($bookings) . " -->\n";
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>My Bookings - Nestlings</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="" name="keywords">
    <meta content="" name="description">

    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600&family=Nunito:wght@600;700;800&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">
    <link href="../lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        .booking-card {
            transition: all 0.3s ease;
            border-radius: 15px;
            border: none;
            margin-bottom: 20px;
        }
        
        .booking-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .status-badge {
            font-size: 0.8rem;
            padding: 5px 12px;
            border-radius: 20px;
            font-weight: 500;
        }

        .payment-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-unpaid {
            background: #ffeaa7;
            color: #d63031;
        }

        .status-partial {
            background: #74b9ff;
            color: white;
        }

        .status-paid {
            background: #00b894;
            color: white;
        }
        
        .status-pending { background-color: #fff3cd; color: #856404; }
        .status-confirmed { background-color: #d1ecf1; color: #0c5460; }
        .status-completed { background-color: #d4edda; color: #155724; }
        .status-cancelled { background-color: #f8d7da; color: #721c24; }
        
        .user-dropdown {
            border-radius: 25px;
            padding: 8px 20px;
            background: linear-gradient(135deg, var(--primary) 0%, #6BB6FF 100%);
            border: none;
            color: white;
            font-weight: 500;
        }
        
        .user-dropdown:hover {
            background: linear-gradient(135deg, #6BB6FF 0%, var(--primary) 100%);
            color: white;
        }
        
        .nav-tabs .nav-link {
            border-radius: 25px;
            margin-right: 10px;
            border: 2px solid transparent;
            font-weight: 500;
        }
        
        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, var(--primary) 0%, #6BB6FF 100%);
            border-color: transparent;
            color: white;
        }

        /* Force hide spinner after 2 seconds */
        #spinner.force-hide {
            display: none !important;
        }
    </style>

    <script>
        // Force hide spinner after 2 seconds as fallback
        setTimeout(function() {
            const spinner = document.getElementById('spinner');
            if (spinner) {
                spinner.classList.add('force-hide');
            }
        }, 2000);
    </script>
</head>

<body>
    <!-- Spinner Start -->
    <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>
    <!-- Spinner End -->

    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light shadow sticky-top p-0">
        <a href="../index.html" class="navbar-brand d-flex align-items-center px-4 px-lg-5">
            <h2 class="m-0 text-primary"><i class="fa fa-baby me-3"></i>Nestlings</h2>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="dashboard.php" class="nav-item nav-link">Home</a>
                <a href="search.php" class="nav-item nav-link">Find DayCare</a>
                <a href="bookings.php" class="nav-item nav-link active">My Bookings</a>
                <a href="payments.php" class="nav-item nav-link">Payments</a>
                <a href="favorites.php" class="nav-item nav-link">Favorites</a>
                <a href="reviews.php" class="nav-item nav-link">Reviews</a>
            </div>
            <div class="dropdown">
                <button class="btn user-dropdown dropdown-toggle d-flex align-items-center" type="button" data-bs-toggle="dropdown">
                    <i class="fa fa-user-circle me-2"></i>
                    <span><?php echo htmlspecialchars($parent['name']); ?></span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="profile.php"><i class="fa fa-user me-2"></i>My Profile</a></li>
                    <li><a class="dropdown-item" href="settings.php"><i class="fa fa-cog me-2"></i>Settings</a></li>
                    <li><a class="dropdown-item" href="help.php"><i class="fa fa-question-circle me-2"></i>Help</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="../logout.php"><i class="fa fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Header Start -->
    <div class="container-fluid bg-primary py-5 mb-5 page-header">
        <div class="container py-5">
            <div class="row justify-content-center">
                <div class="col-lg-10 text-center">
                    <h1 class="display-3 text-white animated slideInDown">My Bookings</h1>
                    <p class="text-white">Manage your childcare bookings and appointments</p>
                </div>
            </div>
        </div>
    </div>
    <!-- Header End -->

    <!-- Bookings Content Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <!-- Booking Stats -->
            <div class="row g-4 mb-5">
                <div class="col-lg-3 col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="bg-warning rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 60px; height: 60px;">
                                <i class="fa fa-clock text-white"></i>
                            </div>
                            <h4 class="text-warning"><?php echo count($grouped_bookings['pending']); ?></h4>
                            <p class="mb-0">Pending</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="bg-info rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 60px; height: 60px;">
                                <i class="fa fa-check-circle text-white"></i>
                            </div>
                            <h4 class="text-info"><?php echo count($grouped_bookings['confirmed']); ?></h4>
                            <p class="mb-0">Confirmed</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="bg-success rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 60px; height: 60px;">
                                <i class="fa fa-star text-white"></i>
                            </div>
                            <h4 class="text-success"><?php echo count($grouped_bookings['completed']); ?></h4>
                            <p class="mb-0">Completed</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="bg-danger rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 60px; height: 60px;">
                                <i class="fa fa-times-circle text-white"></i>
                            </div>
                            <h4 class="text-danger"><?php echo count($grouped_bookings['cancelled']); ?></h4>
                            <p class="mb-0">Cancelled</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Booking Tabs -->
            <ul class="nav nav-tabs mb-4" id="bookingTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab">
                        All Bookings (<?php echo count($bookings); ?>)
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="pending-tab" data-bs-toggle="tab" data-bs-target="#pending" type="button" role="tab">
                        Pending (<?php echo count($grouped_bookings['pending']); ?>)
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="confirmed-tab" data-bs-toggle="tab" data-bs-target="#confirmed" type="button" role="tab">
                        Confirmed (<?php echo count($grouped_bookings['confirmed']); ?>)
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="completed-tab" data-bs-toggle="tab" data-bs-target="#completed" type="button" role="tab">
                        Completed (<?php echo count($grouped_bookings['completed']); ?>)
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="bookingTabContent">
                <!-- All Bookings Tab -->
                <div class="tab-pane fade show active" id="all" role="tabpanel">
                    <?php if (empty($bookings)): ?>
                        <div class="text-center py-5">
                            <i class="fa fa-calendar-alt fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">No bookings yet</h4>
                            <p class="text-muted">Start by finding a daycare provider</p>
                            <a href="search.php" class="btn btn-primary">Find DayCare</a>
                        </div>
                    <?php else: ?>
                        <?php foreach ($bookings as $booking): ?>
                            <?php include 'booking_card.php'; ?>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <!-- Pending Bookings Tab -->
                <div class="tab-pane fade" id="pending" role="tabpanel">
                    <?php if (empty($grouped_bookings['pending'])): ?>
                        <div class="text-center py-5">
                            <i class="fa fa-clock fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">No pending bookings</h4>
                        </div>
                    <?php else: ?>
                        <?php foreach ($grouped_bookings['pending'] as $booking): ?>
                            <?php include 'booking_card.php'; ?>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <!-- Confirmed Bookings Tab -->
                <div class="tab-pane fade" id="confirmed" role="tabpanel">
                    <?php if (empty($grouped_bookings['confirmed'])): ?>
                        <div class="text-center py-5">
                            <i class="fa fa-check-circle fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">No confirmed bookings</h4>
                        </div>
                    <?php else: ?>
                        <?php foreach ($grouped_bookings['confirmed'] as $booking): ?>
                            <?php include 'booking_card.php'; ?>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <!-- Completed Bookings Tab -->
                <div class="tab-pane fade" id="completed" role="tabpanel">
                    <?php if (empty($grouped_bookings['completed'])): ?>
                        <div class="text-center py-5">
                            <i class="fa fa-star fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">No completed bookings</h4>
                        </div>
                    <?php else: ?>
                        <?php foreach ($grouped_bookings['completed'] as $booking): ?>
                            <?php include 'booking_card.php'; ?>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <!-- Bookings Content End -->

    <!-- Footer Start -->
    <div class="container-fluid bg-dark text-light footer pt-5 mt-5 wow fadeIn" data-wow-delay="0.1s">
        <div class="container py-5">
            <div class="row g-5">
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3"><i class="fa fa-baby me-2"></i>Nestlings</h4>
                    <p>Connecting families with trusted childcare providers across Sri Lanka. Safe, verified, and reliable daycare solutions for your little ones.</p>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Quick Links</h4>
                    <a class="btn btn-link" href="../about.html">About Us</a>
                    <a class="btn btn-link" href="../contact.html">Contact Us</a>
                    <a class="btn btn-link" href="../services.html">Our Services</a>
                    <a class="btn btn-link" href="search.php">Find DayCare</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Support</h4>
                    <a class="btn btn-link" href="help.php">Help Center</a>
                    <a class="btn btn-link" href="">Safety Guidelines</a>
                    <a class="btn btn-link" href="">Provider Resources</a>
                    <a class="btn btn-link" href="">Parent Guide</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Contact Info</h4>
                    <p class="mb-2"><i class="fa fa-map-marker-alt me-3"></i>123 Main Street, Colombo, Sri Lanka</p>
                    <p class="mb-2"><i class="fa fa-phone-alt me-3"></i>+94 11 234 5678</p>
                    <p class="mb-2"><i class="fa fa-envelope me-3"></i><EMAIL></p>
                </div>
            </div>
        </div>
    </div>
    <!-- Footer End -->

    <!-- Back to Top -->
    <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/owlcarousel/owl.carousel.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>

    <script>
        // Debug: Log when script runs
        console.log('Bookings page script loaded');

        // Hide spinner immediately
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, hiding spinner');
            const spinner = document.getElementById('spinner');
            if (spinner) {
                spinner.classList.remove('show');
                spinner.style.display = 'none';
                console.log('Spinner hidden');
            }
        });

        // Hide spinner when page loads
        window.addEventListener('load', function() {
            console.log('Window loaded, hiding spinner');
            const spinner = document.getElementById('spinner');
            if (spinner) {
                spinner.classList.remove('show');
                spinner.style.display = 'none';
            }
        });

        // Backup method using jQuery
        $(document).ready(function() {
            console.log('jQuery ready, hiding spinner');
            $('#spinner').removeClass('show').hide();
        });

        // Immediate execution
        setTimeout(function() {
            console.log('Timeout method, hiding spinner');
            const spinner = document.getElementById('spinner');
            if (spinner) {
                spinner.classList.remove('show');
                spinner.style.display = 'none';
            }
        }, 100);

        function cancelBooking(bookingId) {
            if (confirm('Are you sure you want to cancel this booking?')) {
                // Add cancel booking functionality here
                window.location.href = 'cancel_booking.php?id=' + bookingId;
            }
        }
    </script>
</body>

</html>
