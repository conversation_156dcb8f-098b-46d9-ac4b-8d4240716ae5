-- Create payments table for tracking booking payments
CREATE TABLE IF NOT EXISTS payments (
    payment_id INT AUTO_INCREMENT PRIMARY KEY,
    booking_id INT NOT NULL,
    parent_id INT NOT NULL,
    provider_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_type ENUM('partial', 'full') NOT NULL DEFAULT 'full',
    payment_method VARCHAR(50) DEFAULT 'online',
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') NOT NULL DEFAULT 'pending',
    transaction_id VARCHAR(100) NULL,
    card_last_four VARCHAR(4) NULL,
    card_holder_name VARCHAR(100) NULL,
    payment_date TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(booking_id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES parents(parent_id) ON DELETE CASCADE,
    FOREI<PERSON><PERSON> KEY (provider_id) REFERENCES providers(provider_id) ON DELETE CASCADE,
    INDEX idx_payments_booking (booking_id),
    INDEX idx_payments_parent (parent_id),
    INDEX idx_payments_provider (provider_id),
    INDEX idx_payments_status (payment_status)
);

-- Add payment tracking columns to bookings table
ALTER TABLE bookings 
ADD COLUMN payment_status ENUM('unpaid', 'partial', 'paid') DEFAULT 'unpaid' AFTER total_cost,
ADD COLUMN paid_amount DECIMAL(10,2) DEFAULT 0.00 AFTER payment_status,
ADD COLUMN remaining_amount DECIMAL(10,2) DEFAULT 0.00 AFTER paid_amount;

-- Update existing bookings to set remaining_amount equal to total_cost
UPDATE bookings SET remaining_amount = total_cost WHERE remaining_amount = 0 AND total_cost > 0;

-- Create index for payment status
CREATE INDEX idx_bookings_payment_status ON bookings(payment_status);
