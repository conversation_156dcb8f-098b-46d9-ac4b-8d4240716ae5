<?php
// Debug script for analytics.php
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

echo "<h2>🔍 Analytics Debug Tool</h2>";

// Check session
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'provider') {
    echo "<p>❌ Session issue: user_id=" . ($_SESSION['user_id'] ?? 'not set') . ", user_type=" . ($_SESSION['user_type'] ?? 'not set') . "</p>";
    exit();
}

echo "<p>✅ Session OK: user_id=" . $_SESSION['user_id'] . ", user_type=" . $_SESSION['user_type'] . "</p>";

// Test database connection
try {
    require_once '../config/database.php';
    echo "<p>✅ Database connection successful!</p>";
} catch(Exception $e) {
    echo "<p>❌ Database connection failed: " . $e->getMessage() . "</p>";
    exit();
}

// Test provider query
try {
    $stmt = $pdo->prepare("SELECT * FROM providers WHERE provider_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $provider = $stmt->fetch();
    
    if ($provider) {
        echo "<p>✅ Provider found: " . htmlspecialchars($provider['business_name']) . "</p>";
    } else {
        echo "<p>❌ Provider not found for ID: " . $_SESSION['user_id'] . "</p>";
        exit();
    }
} catch(Exception $e) {
    echo "<p>❌ Provider query failed: " . $e->getMessage() . "</p>";
    exit();
}

// Test each analytics query individually
echo "<h3>Testing Analytics Queries:</h3>";

// 1. Overview query
try {
    echo "<p>Testing overview query...</p>";
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_bookings,
            SUM(total_cost) as total_revenue,
            AVG(total_cost) as avg_booking_value
        FROM bookings 
        WHERE provider_id = ? AND status != 'cancelled'
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $overview = $stmt->fetch();
    echo "<p>✅ Overview: " . $overview['total_bookings'] . " bookings, LKR " . number_format($overview['total_revenue'] ?? 0, 2) . " revenue</p>";
} catch(Exception $e) {
    echo "<p>❌ Overview query failed: " . $e->getMessage() . "</p>";
}

// 2. Monthly query
try {
    echo "<p>Testing monthly query...</p>";
    $stmt = $pdo->prepare("
        SELECT 
            DATE_FORMAT(booking_date, '%Y-%m') as month,
            COUNT(*) as bookings,
            SUM(total_cost) as revenue
        FROM bookings 
        WHERE provider_id = ? AND status != 'cancelled' 
            AND booking_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
        GROUP BY DATE_FORMAT(booking_date, '%Y-%m')
        ORDER BY month DESC
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $monthly = $stmt->fetchAll();
    echo "<p>✅ Monthly data: " . count($monthly) . " months found</p>";
} catch(Exception $e) {
    echo "<p>❌ Monthly query failed: " . $e->getMessage() . "</p>";
}

// 3. Services query
try {
    echo "<p>Testing services query...</p>";
    $stmt = $pdo->prepare("
        SELECT 
            service_type,
            COUNT(*) as bookings,
            SUM(total_cost) as revenue
        FROM bookings 
        WHERE provider_id = ? AND status != 'cancelled'
        GROUP BY service_type
        ORDER BY bookings DESC
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $services = $stmt->fetchAll();
    echo "<p>✅ Services data: " . count($services) . " service types found</p>";
} catch(Exception $e) {
    echo "<p>❌ Services query failed: " . $e->getMessage() . "</p>";
}

// 4. Reviews query
try {
    echo "<p>Testing reviews query...</p>";
    $stmt = $pdo->prepare("
        SELECT 
            AVG(rating) as avg_rating,
            COUNT(*) as total_reviews,
            SUM(CASE WHEN rating = 5 THEN 1 ELSE 0 END) as five_star,
            SUM(CASE WHEN rating = 4 THEN 1 ELSE 0 END) as four_star,
            SUM(CASE WHEN rating = 3 THEN 1 ELSE 0 END) as three_star,
            SUM(CASE WHEN rating = 2 THEN 1 ELSE 0 END) as two_star,
            SUM(CASE WHEN rating = 1 THEN 1 ELSE 0 END) as one_star
        FROM reviews 
        WHERE provider_id = ? AND is_active = 1
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $ratings = $stmt->fetch();
    echo "<p>✅ Reviews data: " . ($ratings['total_reviews'] ?? 0) . " reviews, avg rating " . number_format($ratings['avg_rating'] ?? 0, 2) . "</p>";
} catch(Exception $e) {
    echo "<p>❌ Reviews query failed: " . $e->getMessage() . "</p>";
}

// 5. Performance query
try {
    echo "<p>Testing performance query...</p>";
    $stmt = $pdo->prepare("
        SELECT 
            SUM(CASE WHEN booking_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as recent_bookings,
            SUM(CASE WHEN booking_date >= DATE_SUB(CURDATE(), INTERVAL 60 DAY) AND booking_date < DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as previous_bookings,
            SUM(CASE WHEN booking_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN total_cost ELSE 0 END) as recent_revenue,
            SUM(CASE WHEN booking_date >= DATE_SUB(CURDATE(), INTERVAL 60 DAY) AND booking_date < DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN total_cost ELSE 0 END) as previous_revenue
        FROM bookings 
        WHERE provider_id = ? AND status != 'cancelled'
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $performance = $stmt->fetch();
    echo "<p>✅ Performance data: " . ($performance['recent_bookings'] ?? 0) . " recent bookings</p>";
} catch(Exception $e) {
    echo "<p>❌ Performance query failed: " . $e->getMessage() . "</p>";
}

// 6. Top customers query
try {
    echo "<p>Testing top customers query...</p>";
    $stmt = $pdo->prepare("
        SELECT 
            p.name as parent_name,
            COUNT(b.booking_id) as total_bookings,
            SUM(b.total_cost) as total_spent,
            MAX(b.booking_date) as last_booking
        FROM bookings b
        JOIN parents p ON b.parent_id = p.parent_id
        WHERE b.provider_id = ? AND b.status != 'cancelled'
        GROUP BY b.parent_id, p.name
        ORDER BY total_bookings DESC
        LIMIT 5
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $customers = $stmt->fetchAll();
    echo "<p>✅ Top customers data: " . count($customers) . " customers found</p>";
} catch(Exception $e) {
    echo "<p>❌ Top customers query failed: " . $e->getMessage() . "</p>";
}

echo "<h3>✅ All queries completed successfully!</h3>";
echo "<p>If you see this message, the analytics queries are working. The issue might be in the HTML/CSS/JavaScript rendering.</p>";
echo "<p><a href='analytics.php'>Try Analytics Page Again</a></p>";
?>
