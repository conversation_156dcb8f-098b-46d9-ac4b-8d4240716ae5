<?php
require_once 'config/database.php';

echo "<h2>Payment System Debug</h2>";

// Check if payments table exists
try {
    $check_table = $pdo->query("SHOW TABLES LIKE 'payments'");
    $payments_table_exists = $check_table->rowCount() > 0;
    
    if ($payments_table_exists) {
        echo "<p style='color: green;'>✓ Payments table exists</p>";
        
        // Check table structure
        $structure = $pdo->query("DESCRIBE payments");
        echo "<h3>Payments Table Structure:</h3>";
        echo "<table border='1'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        while ($row = $structure->fetch()) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check if there are any payments
        $count = $pdo->query("SELECT COUNT(*) FROM payments")->fetchColumn();
        echo "<p>Total payments in database: <strong>$count</strong></p>";
        
        if ($count > 0) {
            // Show recent payments
            $recent = $pdo->query("SELECT * FROM payments ORDER BY created_at DESC LIMIT 5")->fetchAll();
            echo "<h3>Recent Payments:</h3>";
            echo "<table border='1'>";
            echo "<tr><th>ID</th><th>Booking ID</th><th>Amount</th><th>Status</th><th>Date</th></tr>";
            foreach ($recent as $payment) {
                echo "<tr>";
                echo "<td>" . $payment['payment_id'] . "</td>";
                echo "<td>" . $payment['booking_id'] . "</td>";
                echo "<td>" . $payment['amount'] . "</td>";
                echo "<td>" . $payment['payment_status'] . "</td>";
                echo "<td>" . $payment['payment_date'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ Payments table does not exist</p>";
        echo "<p><a href='migrate.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Run Migration</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

// Check bookings table for payment columns
try {
    $booking_structure = $pdo->query("DESCRIBE bookings");
    $has_payment_columns = false;
    echo "<h3>Bookings Table Payment Columns:</h3>";
    while ($row = $booking_structure->fetch()) {
        if (in_array($row['Field'], ['payment_status', 'paid_amount', 'remaining_amount'])) {
            echo "<p style='color: green;'>✓ " . $row['Field'] . " exists</p>";
            $has_payment_columns = true;
        }
    }
    
    if (!$has_payment_columns) {
        echo "<p style='color: red;'>✗ Payment columns missing from bookings table</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error checking bookings table: " . $e->getMessage() . "</p>";
}
?>
