<?php
session_start();

// If user is already logged in, redirect to dashboard
if (isset($_SESSION['user_id'])) {
    if ($_SESSION['user_type'] == 'parent') {
        header("Location: Parent/dashboard.php");
    } else {
        header("Location: Daycare/dashboard.php");
    }
    exit();
}

$error_message = '';
$success_message = '';

// Handle form submission
if ($_POST) {
    require_once 'config/database.php';

    $business_name = trim($_POST['business_name']);
    $contact_person = trim($_POST['contact_person']);
    $email = trim($_POST['email']);
    $phone = trim($_POST['phone']);
    $address = trim($_POST['address']);
    $district = $_POST['district'];
    $service_types = isset($_POST['service_types']) ? implode(',', $_POST['service_types']) : '';
    $capacity = (int)$_POST['capacity'];
    $description = trim($_POST['description']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];

    // Certificate information
    $certificate_type = $_POST['certificate_type'];
    $certificate_number = trim($_POST['certificate_number']);
    $certificate_expiry = $_POST['certificate_expiry'];

    // Validation
    if (empty($business_name) || empty($contact_person) || empty($email) || empty($phone) ||
        empty($address) || empty($district) || empty($service_types) || empty($capacity) || empty($password) ||
        empty($certificate_type) || empty($certificate_number) || empty($certificate_expiry)) {
        $error_message = 'Please fill in all required fields including certificate information.';
    } elseif ($password !== $confirm_password) {
        $error_message = 'Passwords do not match.';
    } elseif (strlen($password) < 6) {
        $error_message = 'Password must be at least 6 characters long.';
    } elseif (!isset($_FILES['certificate_file']) || $_FILES['certificate_file']['error'] !== UPLOAD_ERR_OK) {
        $error_message = 'Please upload your certificate file.';
    } elseif ($_FILES['certificate_file']['size'] > 5 * 1024 * 1024) { // 5MB limit
        $error_message = 'Certificate file size must be less than 5MB.';
    } elseif (!in_array(strtolower(pathinfo($_FILES['certificate_file']['name'], PATHINFO_EXTENSION)), ['pdf', 'jpg', 'jpeg', 'png'])) {
        $error_message = 'Certificate file must be PDF, JPG, JPEG, or PNG format.';
    } else {
        // Check if email already exists
        $stmt = $pdo->prepare("SELECT email FROM parents WHERE email = ? UNION SELECT email FROM providers WHERE email = ?");
        $stmt->execute([$email, $email]);

        if ($stmt->fetch()) {
            $error_message = 'An account with this email already exists.';
        } else {
            // Handle certificate file upload
            $upload_dir = 'uploads/certificates/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            $file_extension = strtolower(pathinfo($_FILES['certificate_file']['name'], PATHINFO_EXTENSION));
            $certificate_filename = 'cert_' . uniqid() . '.' . $file_extension;
            $certificate_filepath = $upload_dir . $certificate_filename;

            if (move_uploaded_file($_FILES['certificate_file']['tmp_name'], $certificate_filepath)) {
                // Insert new provider with certificate information
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("INSERT INTO providers (business_name, contact_person, email, phone, address, district, service_types, capacity, description, password, verification_status, mandatory_certificate_type, mandatory_certificate_number, mandatory_certificate_expiry, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', ?, ?, ?, NOW())");

                if ($stmt->execute([$business_name, $contact_person, $email, $phone, $address, $district, $service_types, $capacity, $description, $hashed_password, $certificate_type, $certificate_number, $certificate_expiry])) {
                    // Get the provider ID
                    $provider_id = $pdo->lastInsertId();

                    // Insert certificate document record
                    $document_type = ($certificate_type === 'ncpa') ? 'ncpa_certificate' : 'moh_certificate';
                    $document_name = ($certificate_type === 'ncpa') ? 'NCPA Certificate' : 'MOH Certificate';

                    $doc_stmt = $pdo->prepare("INSERT INTO provider_documents (provider_id, document_type, document_name, file_path, expiry_date, verification_status, uploaded_at) VALUES (?, ?, ?, ?, ?, 'pending', NOW())");
                    $doc_stmt->execute([$provider_id, $document_type, $document_name, $certificate_filepath, $certificate_expiry]);

                    // Auto-login the provider
                    $_SESSION['user_id'] = $provider_id;
                    $_SESSION['user_type'] = 'provider';
                    $_SESSION['user_name'] = $business_name;

                    // Redirect to dashboard
                    header("Location: Daycare/dashboard.php");
                    exit();
                } else {
                    // Delete uploaded file if database insertion failed
                    if (file_exists($certificate_filepath)) {
                        unlink($certificate_filepath);
                    }
                    $error_message = 'Registration failed. Please try again.';
                }
            } else {
                $error_message = 'Failed to upload certificate file. Please try again.';
            }
        }
    }
}

// Sri Lankan districts
$districts = [
    'Ampara', 'Anuradhapura', 'Badulla', 'Batticaloa', 'Colombo', 'Galle', 'Gampaha',
    'Hambantota', 'Jaffna', 'Kalutara', 'Kandy', 'Kegalle', 'Kilinochchi', 'Kurunegala',
    'Mannar', 'Matale', 'Matara', 'Monaragala', 'Mullaitivu', 'Nuwara Eliya', 'Polonnaruwa',
    'Puttalam', 'Ratnapura', 'Trincomalee', 'Vavuniya'
];

$service_types = [
    'Full Day Care', 'Half Day Care', 'After School Care', 'Weekend Care', 'Holiday Care',
    'Infant Care (0-2 years)', 'Toddler Care (2-4 years)', 'Preschool (4-6 years)'
];
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>Provider Sign Up - Nestlings</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="" name="keywords">
    <meta content="" name="description">

    <!-- Favicon -->
    <link href="img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600&family=Nunito:wght@600;700;800&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="lib/animate/animate.min.css" rel="stylesheet">
    <link href="lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="css/style.css" rel="stylesheet">
</head>

<body>
    <!-- Spinner Start -->
    <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>
    <!-- Spinner End -->


    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light shadow sticky-top p-0">
        <a href="index.html" class="navbar-brand d-flex align-items-center px-4 px-lg-5">
            <h2 class="m-0 text-primary"><i class="fa fa-baby me-3"></i>Nestlings</h2>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="index.html" class="nav-item nav-link">Home</a>
                <a href="about.html" class="nav-item nav-link">About</a>
                <a href="services.html" class="nav-item nav-link">Services</a>
                <a href="daycare.html" class="nav-item nav-link">DayCare</a>
                <a href="contact.html" class="nav-item nav-link">Contact</a>
            </div>
            <div class="d-flex align-items-center">
                <a href="login.php" class="btn auth-btn auth-btn-login py-2 px-4 me-3 d-none d-lg-block">
                    <i class="fa fa-sign-in-alt me-2"></i>Login
                </a>
                <a href="signup.php" class="btn auth-btn auth-btn-signup py-2 px-4 d-none d-lg-block">
                    <i class="fa fa-user-plus me-2"></i>Sign Up
                </a>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->


    <!-- Header Start -->
    <div class="container-fluid bg-primary py-5 mb-5 page-header">
        <div class="container py-5">
            <div class="row justify-content-center">
                <div class="col-lg-10 text-center">
                    <h1 class="display-3 text-white animated slideInDown">Provider Sign Up</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item"><a class="text-white" href="index.html">Home</a></li>
                            <li class="breadcrumb-item"><a class="text-white" href="signup.php">Sign Up</a></li>
                            <li class="breadcrumb-item text-white active" aria-current="page">Provider</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
    <!-- Header End -->


    <!-- Provider Sign Up Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="card shadow-lg border-0">
                        <div class="card-body p-5">
                            <div class="text-center mb-4">
                                <h3 class="text-primary"><i class="fa fa-building me-2"></i>Create Provider Account</h3>
                                <p class="text-muted">Join Nestlings to connect with families seeking childcare</p>
                            </div>

                            <?php if ($error_message): ?>
                                <div class="alert alert-danger" role="alert">
                                    <?php echo htmlspecialchars($error_message); ?>
                                </div>
                            <?php endif; ?>

                            <?php if ($success_message): ?>
                                <div class="alert alert-success" role="alert">
                                    <?php echo htmlspecialchars($success_message); ?>
                                    <div class="mt-3">
                                        <a href="login.php" class="btn btn-primary">Login Now</a>
                                    </div>
                                </div>
                            <?php else: ?>

                            <form method="POST" enctype="multipart/form-data">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="business_name" class="form-label">Business Name *</label>
                                        <input type="text" class="form-control" id="business_name" name="business_name" required value="<?php echo isset($_POST['business_name']) ? htmlspecialchars($_POST['business_name']) : ''; ?>">
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="contact_person" class="form-label">Contact Person *</label>
                                        <input type="text" class="form-control" id="contact_person" name="contact_person" required value="<?php echo isset($_POST['contact_person']) ? htmlspecialchars($_POST['contact_person']) : ''; ?>">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email Address *</label>
                                        <input type="email" class="form-control" id="email" name="email" required value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">Phone Number *</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" required value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="address" class="form-label">Full Address *</label>
                                    <textarea class="form-control" id="address" name="address" rows="3" required><?php echo isset($_POST['address']) ? htmlspecialchars($_POST['address']) : ''; ?></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="district" class="form-label">District *</label>
                                        <select class="form-select" id="district" name="district" required>
                                            <option value="">Select District</option>
                                            <?php foreach ($districts as $district): ?>
                                                <option value="<?php echo $district; ?>" <?php echo (isset($_POST['district']) && $_POST['district'] == $district) ? 'selected' : ''; ?>>
                                                    <?php echo $district; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="capacity" class="form-label">Maximum Capacity *</label>
                                        <input type="number" class="form-control" id="capacity" name="capacity" min="1" required value="<?php echo isset($_POST['capacity']) ? htmlspecialchars($_POST['capacity']) : ''; ?>">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Service Types * (Select all that apply)</label>
                                    <div class="row">
                                        <?php foreach ($service_types as $service): ?>
                                            <div class="col-md-6 mb-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="service_types[]" value="<?php echo $service; ?>" id="service_<?php echo str_replace(' ', '_', $service); ?>" <?php echo (isset($_POST['service_types']) && in_array($service, $_POST['service_types'])) ? 'checked' : ''; ?>>
                                                    <label class="form-check-label" for="service_<?php echo str_replace(' ', '_', $service); ?>">
                                                        <?php echo $service; ?>
                                                    </label>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">Business Description</label>
                                    <textarea class="form-control" id="description" name="description" rows="4" placeholder="Tell parents about your daycare, facilities, experience, etc."><?php echo isset($_POST['description']) ? htmlspecialchars($_POST['description']) : ''; ?></textarea>
                                </div>

                                <!-- Mandatory Certificate Section -->
                                <div class="mb-4">
                                    <h5 class="text-primary mb-3">📋 Mandatory Certification Requirements</h5>
                                    <div class="alert alert-info">
                                        <strong>⚠️ Important:</strong> All daycare providers must have either an <strong>NCPA</strong> (National Child Protection Authority) certificate OR an <strong>MOH</strong> (Ministry of Health) certificate to operate legally in Sri Lanka.
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Certificate Type *</label>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="certificate_type" value="ncpa" id="cert_ncpa" required <?php echo (isset($_POST['certificate_type']) && $_POST['certificate_type'] == 'ncpa') ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="cert_ncpa">
                                                    <strong>NCPA Certificate</strong><br>
                                                    <small class="text-muted">National Child Protection Authority certification for childcare providers</small>
                                                </label>
                                            </div>
                                            <div class="form-check mt-2">
                                                <input class="form-check-input" type="radio" name="certificate_type" value="moh" id="cert_moh" required <?php echo (isset($_POST['certificate_type']) && $_POST['certificate_type'] == 'moh') ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="cert_moh">
                                                    <strong>MOH Certificate</strong><br>
                                                    <small class="text-muted">Ministry of Health certification for childcare facilities</small>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="certificate_number" class="form-label">Certificate Number *</label>
                                            <input type="text" class="form-control" id="certificate_number" name="certificate_number" required placeholder="e.g., NCPA/2024/001 or MOH/CC/2024/001" value="<?php echo isset($_POST['certificate_number']) ? htmlspecialchars($_POST['certificate_number']) : ''; ?>">
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="certificate_expiry" class="form-label">Certificate Expiry Date *</label>
                                            <input type="date" class="form-control" id="certificate_expiry" name="certificate_expiry" required min="<?php echo date('Y-m-d'); ?>" value="<?php echo isset($_POST['certificate_expiry']) ? htmlspecialchars($_POST['certificate_expiry']) : ''; ?>">
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="certificate_file" class="form-label">Upload Certificate *</label>
                                            <input type="file" class="form-control" id="certificate_file" name="certificate_file" accept=".pdf,.jpg,.jpeg,.png" required>
                                            <small class="text-muted">Accepted formats: PDF, JPG, PNG (Max 5MB)</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="password" class="form-label">Password *</label>
                                        <input type="password" class="form-control" id="password" name="password" required minlength="6">
                                        <small class="text-muted">Minimum 6 characters</small>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="confirm_password" class="form-label">Confirm Password *</label>
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required minlength="6">
                                    </div>
                                </div>

                                <div class="alert alert-info">
                                    <i class="fa fa-info-circle me-2"></i>
                                    <strong>Note:</strong> Your account will be pending verification until you upload required documents and admin approval. You'll be notified once your account is verified.
                                </div>

                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="terms" required>
                                    <label class="form-check-label" for="terms">
                                        I agree to the <a href="#" class="text-primary">Terms of Service</a> and <a href="#" class="text-primary">Privacy Policy</a>
                                    </label>
                                </div>

                                <button type="submit" class="btn btn-primary w-100 py-3 mb-3">Create Provider Account</button>

                                <div class="text-center">
                                    <p class="text-muted">Already have an account? <a href="login.php" class="text-primary">Login here</a></p>
                                    <p class="text-muted">Want to register as a parent? <a href="parent_signup.php" class="text-primary">Click here</a></p>
                                </div>
                            </form>

                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Provider Sign Up End -->


    <!-- Footer Start -->
    <div class="container-fluid bg-dark text-light footer pt-5 mt-5 wow fadeIn" data-wow-delay="0.1s">
        <div class="container py-5">
            <div class="row g-5">
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3"><i class="fa fa-baby me-2"></i>Nestlings</h4>
                    <p>Connecting families with trusted childcare providers across Sri Lanka. Safe, verified, and reliable daycare solutions for your little ones.</p>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Quick Links</h4>
                    <a class="btn btn-link" href="about.html">About Us</a>
                    <a class="btn btn-link" href="contact.html">Contact Us</a>
                    <a class="btn btn-link" href="services.html">Our Services</a>
                    <a class="btn btn-link" href="daycare.html">Find DayCare</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Support</h4>
                    <a class="btn btn-link" href="">Help Center</a>
                    <a class="btn btn-link" href="">Safety Guidelines</a>
                    <a class="btn btn-link" href="">Provider Resources</a>
                    <a class="btn btn-link" href="">Parent Guide</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-3">Contact Info</h4>
                    <p class="mb-2"><i class="fa fa-map-marker-alt me-3"></i>123 Main Street, Colombo, Sri Lanka</p>
                    <p class="mb-2"><i class="fa fa-phone-alt me-3"></i>+94 11 234 5678</p>
                    <p class="mb-2"><i class="fa fa-envelope me-3"></i><EMAIL></p>
                </div>
            </div>
        </div>
    </div>
    <!-- Footer End -->


    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="lib/wow/wow.min.js"></script>
    <script src="lib/easing/easing.min.js"></script>
    <script src="lib/waypoints/waypoints.min.js"></script>
    <script src="lib/owlcarousel/owl.carousel.min.js"></script>

    <!-- Template Javascript -->
    <script src="js/main.js"></script>
</body>

</html>
