<?php
session_start();

// Check if user is logged in and is a provider
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'provider') {
    header('Location: ../login.php');
    exit();
}

require_once '../config/database.php';

// Get provider information
$provider_stmt = $pdo->prepare("SELECT * FROM providers WHERE provider_id = ?");
$provider_stmt->execute([$_SESSION['user_id']]);
$provider = $provider_stmt->fetch();

if (!$provider) {
    header('Location: ../login.php');
    exit();
}

$success_message = '';
$error_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        if ($_POST['action'] === 'update_pricing') {
            $pricing_id = $_POST['pricing_id'];
            $hourly_rate = $_POST['hourly_rate'];
            $daily_rate = $_POST['daily_rate'] ?: null;
            $weekly_rate = $_POST['weekly_rate'] ?: null;
            $monthly_rate = $_POST['monthly_rate'] ?: null;
            $minimum_hours = $_POST['minimum_hours'];
            $maximum_hours = $_POST['maximum_hours'];
            $description = $_POST['description'];
            $is_active = isset($_POST['is_active']) ? 1 : 0;
            
            try {
                $stmt = $pdo->prepare("
                    UPDATE provider_pricing 
                    SET hourly_rate = ?, daily_rate = ?, weekly_rate = ?, monthly_rate = ?, 
                        minimum_hours = ?, maximum_hours = ?, description = ?, is_active = ?
                    WHERE pricing_id = ? AND provider_id = ?
                ");
                $stmt->execute([
                    $hourly_rate, $daily_rate, $weekly_rate, $monthly_rate,
                    $minimum_hours, $maximum_hours, $description, $is_active,
                    $pricing_id, $_SESSION['user_id']
                ]);
                $success_message = "Pricing updated successfully!";
            } catch (Exception $e) {
                $error_message = "Error updating pricing: " . $e->getMessage();
            }
        }
    }
}

// Get current pricing
$pricing_stmt = $pdo->prepare("
    SELECT * FROM provider_pricing 
    WHERE provider_id = ? 
    ORDER BY service_type
");
$pricing_stmt->execute([$_SESSION['user_id']]);
$pricing_data = $pricing_stmt->fetchAll();

// Service type mapping
$service_types = [
    'full_day' => 'Full Day Care',
    'half_day' => 'Half Day Care',
    'after_school' => 'After School Care',
    'weekend' => 'Weekend Care',
    'overnight' => 'Overnight Care'
];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Pricing Management - Nestlings</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600&family=Inter:wght@600;700;800&display=swap" rel="stylesheet">
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">
    <link href="../lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">
    
    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        .pricing-card {
            border: 1px solid #e9ecef;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            background: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }
        
        .pricing-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }
        
        .service-header {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .rate-input {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .rate-input:focus {
            background: white;
            border-color: var(--primary);
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }
        
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: var(--primary);
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</head>

<body>
    <div class="container-xxl bg-white p-0">
        <!-- Spinner Start -->
        <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
            <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                <span class="sr-only">Loading...</span>
            </div>
        </div>
        <!-- Spinner End -->

        <!-- Navbar Start -->
        <nav class="navbar navbar-expand-lg bg-white navbar-light shadow sticky-top p-0">
            <a href="../index.html" class="navbar-brand d-flex align-items-center px-4 px-lg-5">
                <h2 class="m-0 text-primary"><i class="fa fa-baby me-3"></i>Nestlings</h2>
            </a>
            <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarCollapse">
                <div class="navbar-nav ms-auto p-4 p-lg-0">
                    <a href="dashboard.php" class="nav-item nav-link">Dashboard</a>
                    <a href="bookings.php" class="nav-item nav-link">Bookings</a>
                    <a href="profile.php" class="nav-item nav-link">Profile</a>
                    <a href="pricing.php" class="nav-item nav-link active">Pricing</a>
                    <a href="analytics.php" class="nav-item nav-link">Analytics</a>
                    <a href="management.php" class="nav-item nav-link">Management</a>
                    <a href="payments.php" class="nav-item nav-link">Payments</a>
                </div>
                <div class="d-flex align-items-center gap-3">
                    <?php include '../components/notifications.php'; ?>
                    <div class="dropdown">
                        <button class="btn btn-primary dropdown-toggle me-4" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fa fa-user me-2"></i>
                            <span><?php echo htmlspecialchars($provider['business_name']); ?></span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php"><i class="fa fa-building me-2"></i>Business Profile</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fa fa-cog me-2"></i>Settings</a></li>
                            <li><a class="dropdown-item" href="help.php"><i class="fa fa-question-circle me-2"></i>Help</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fa fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>
        <!-- Navbar End -->

        <!-- Header Start -->
        <div class="container-fluid header bg-primary p-0 mb-5">
            <div class="row g-0 align-items-center flex-column-reverse flex-lg-row">
                <div class="col-lg-6 p-5 wow fadeIn" data-wow-delay="0.1s">
                    <h1 class="display-4 text-white mb-5">Pricing Management</h1>
                    <div class="row g-4">
                        <div class="col-sm-4">
                            <div class="border-start border-light ps-4">
                                <h2 class="text-light mb-1" data-toggle="counter-up"><?php echo count($pricing_data); ?></h2>
                                <p class="text-light mb-0">Service Types</p>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="border-start border-light ps-4">
                                <h2 class="text-light mb-1" data-toggle="counter-up"><?php echo count(array_filter($pricing_data, function($p) { return $p['is_active']; })); ?></h2>
                                <p class="text-light mb-0">Active Services</p>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="border-start border-light ps-4">
                                <h2 class="text-light mb-1">LKR</h2>
                                <p class="text-light mb-0">Currency</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 text-center text-lg-end overflow-hidden">
                    <img class="img-fluid" src="../img/hero.png" alt="">
                </div>
            </div>
        </div>
        <!-- Header End -->

        <!-- Pricing Management Start -->
        <div class="container-xxl py-5">
            <div class="container">
                <?php if ($success_message): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fa fa-check-circle me-2"></i><?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error_message): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fa fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="text-center mx-auto mb-5 wow fadeInUp" data-wow-delay="0.1s" style="max-width: 600px;">
                    <h1 class="mb-3">Manage Your Service Pricing</h1>
                    <p>Set competitive rates for your daycare services. Parents will see these prices when booking your services.</p>
                </div>

                <div class="row">
                    <?php foreach ($pricing_data as $pricing): ?>
                        <div class="col-lg-6 col-xl-4 wow fadeInUp" data-wow-delay="0.1s">
                            <div class="pricing-card">
                                <form method="POST" action="">
                                    <input type="hidden" name="action" value="update_pricing">
                                    <input type="hidden" name="pricing_id" value="<?php echo $pricing['pricing_id']; ?>">
                                    
                                    <div class="service-header">
                                        <h4 class="mb-1"><?php echo $service_types[$pricing['service_type']] ?? ucfirst($pricing['service_type']); ?></h4>
                                        <small class="opacity-75"><?php echo ucfirst($pricing['service_type']); ?></small>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Hourly Rate (LKR) *</label>
                                        <input type="number" class="form-control rate-input" name="hourly_rate" 
                                               value="<?php echo $pricing['hourly_rate']; ?>" step="0.01" min="0" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Daily Rate (LKR)</label>
                                        <input type="number" class="form-control rate-input" name="daily_rate" 
                                               value="<?php echo $pricing['daily_rate']; ?>" step="0.01" min="0">
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="mb-3">
                                                <label class="form-label">Min Hours</label>
                                                <input type="number" class="form-control rate-input" name="minimum_hours" 
                                                       value="<?php echo $pricing['minimum_hours']; ?>" min="1" max="24">
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="mb-3">
                                                <label class="form-label">Max Hours</label>
                                                <input type="number" class="form-control rate-input" name="maximum_hours" 
                                                       value="<?php echo $pricing['maximum_hours']; ?>" min="1" max="24">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Service Description</label>
                                        <textarea class="form-control" name="description" rows="3"><?php echo htmlspecialchars($pricing['description']); ?></textarea>
                                    </div>
                                    
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <span>Service Active</span>
                                        <label class="toggle-switch">
                                            <input type="checkbox" name="is_active" <?php echo $pricing['is_active'] ? 'checked' : ''; ?>>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fa fa-save me-2"></i>Update Pricing
                                    </button>
                                </form>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <!-- Pricing Management End -->

        <!-- Footer Start -->
        <div class="container-fluid bg-dark text-light footer pt-5 mt-5 wow fadeIn" data-wow-delay="0.1s">
            <div class="container py-5">
                <div class="row g-5">
                    <div class="col-lg-3 col-md-6">
                        <h4 class="text-white mb-3">Nestlings</h4>
                        <p class="mb-2"><i class="fa fa-map-marker-alt me-3"></i>Colombo, Sri Lanka</p>
                        <p class="mb-2"><i class="fa fa-phone-alt me-3"></i>+94 11 234 5678</p>
                        <p class="mb-2"><i class="fa fa-envelope me-3"></i><EMAIL></p>
                    </div>
                </div>
            </div>
        </div>
        <!-- Footer End -->

        <!-- Back to Top -->
        <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/counterup/counterup.min.js"></script>
    <script src="../lib/owlcarousel/owl.carousel.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>
</body>
</html>
