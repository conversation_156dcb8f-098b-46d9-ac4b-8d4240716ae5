-- Nestlings Database Schema - FIXED VERSION
-- Create database
CREATE DATABASE IF NOT EXISTS nestlings_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE nestlings_db;

-- Parents table
CREATE TABLE parents (
    parent_id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20) NOT NULL,
    district VARCHAR(50) NOT NULL,
    password VARCHAR(255) NOT NULL,
    profile_image VARCHAR(255) DEFAULT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Providers table
CREATE TABLE providers (
    provider_id INT AUTO_INCREMENT PRIMARY KEY,
    business_name VA<PERSON>HA<PERSON>(150) NOT NULL,
    contact_person VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20) NOT NULL,
    address TEXT NOT NULL,
    district VARCHAR(50) NOT NULL,
    service_types TEXT NOT NULL,
    capacity INT NOT NULL,
    description TEXT,
    password VARCHAR(255) NOT NULL,
    verification_status ENUM('pending', 'verified', 'rejected') DEFAULT 'pending',
    profile_image VARCHAR(255) DEFAULT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    total_reviews INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Admins table
CREATE TABLE admins (
    admin_id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('super_admin', 'admin') DEFAULT 'admin',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Provider documents table (SINGLE DEFINITION)
CREATE TABLE provider_documents (
    document_id INT PRIMARY KEY AUTO_INCREMENT,
    provider_id INT NOT NULL,
    document_type ENUM('business_license', 'childcare_license', 'insurance', 'safety_certificate', 'staff_qualifications', 'health_certificate', 'fire_safety', 'other') NOT NULL,
    document_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    expiry_date DATE NULL,
    verification_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    verified_at TIMESTAMP NULL,
    verified_by INT NULL,
    FOREIGN KEY (provider_id) REFERENCES providers(provider_id) ON DELETE CASCADE,
    FOREIGN KEY (verified_by) REFERENCES admins(admin_id) ON DELETE SET NULL,
    INDEX idx_provider_documents (provider_id, document_type)
);

-- Provider portfolio table
CREATE TABLE provider_portfolio (
    portfolio_id INT AUTO_INCREMENT PRIMARY KEY,
    provider_id INT NOT NULL,
    image_path VARCHAR(500) NOT NULL,
    caption TEXT,
    display_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (provider_id) REFERENCES providers(provider_id) ON DELETE CASCADE
);

-- Bookings table
CREATE TABLE bookings (
    booking_id INT AUTO_INCREMENT PRIMARY KEY,
    parent_id INT NOT NULL,
    provider_id INT NOT NULL,
    child_name VARCHAR(100) NOT NULL,
    child_age INT NOT NULL,
    booking_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    duration INT NOT NULL,
    service_type VARCHAR(100) NOT NULL,
    special_requirements TEXT,
    status ENUM('pending', 'confirmed', 'cancelled', 'completed') DEFAULT 'pending',
    total_cost DECIMAL(10,2) DEFAULT 0.00,
    payment_status ENUM('pending', 'paid', 'refunded') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES parents(parent_id) ON DELETE CASCADE,
    FOREIGN KEY (provider_id) REFERENCES providers(provider_id) ON DELETE CASCADE
);

-- Reviews table
CREATE TABLE reviews (
    review_id INT AUTO_INCREMENT PRIMARY KEY,
    parent_id INT NOT NULL,
    provider_id INT NOT NULL,
    booking_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    is_anonymous BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES parents(parent_id) ON DELETE CASCADE,
    FOREIGN KEY (provider_id) REFERENCES providers(provider_id) ON DELETE CASCADE,
    FOREIGN KEY (booking_id) REFERENCES bookings(booking_id) ON DELETE CASCADE,
    UNIQUE KEY unique_review (parent_id, booking_id)
);

-- Favorites table
CREATE TABLE favorites (
    favorite_id INT AUTO_INCREMENT PRIMARY KEY,
    parent_id INT NOT NULL,
    provider_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES parents(parent_id) ON DELETE CASCADE,
    FOREIGN KEY (provider_id) REFERENCES providers(provider_id) ON DELETE CASCADE,
    UNIQUE KEY unique_favorite (parent_id, provider_id)
);

-- Notifications table
CREATE TABLE notifications (
    notification_id INT AUTO_INCREMENT PRIMARY KEY,
    user_type ENUM('parent', 'provider', 'admin') NOT NULL,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('booking', 'review', 'verification', 'general') NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Reports table
CREATE TABLE reports (
    report_id INT AUTO_INCREMENT PRIMARY KEY,
    reporter_type ENUM('parent', 'provider') NOT NULL,
    reporter_id INT NOT NULL,
    reported_type ENUM('parent', 'provider', 'review') NOT NULL,
    reported_id INT NOT NULL,
    reason VARCHAR(255) NOT NULL,
    description TEXT,
    status ENUM('pending', 'investigating', 'resolved', 'dismissed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP NULL,
    resolved_by INT NULL,
    FOREIGN KEY (resolved_by) REFERENCES admins(admin_id) ON DELETE SET NULL
);

-- Provider availability table
CREATE TABLE provider_availability (
    availability_id INT AUTO_INCREMENT PRIMARY KEY,
    provider_id INT NOT NULL,
    day_of_week ENUM('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday') NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_available BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (provider_id) REFERENCES providers(provider_id) ON DELETE CASCADE
);

-- System settings table
CREATE TABLE system_settings (
    setting_id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_parents_email ON parents(email);
CREATE INDEX idx_parents_district ON parents(district);
CREATE INDEX idx_providers_email ON providers(email);
CREATE INDEX idx_providers_district ON providers(district);
CREATE INDEX idx_providers_verification ON providers(verification_status);
CREATE INDEX idx_bookings_parent ON bookings(parent_id);
CREATE INDEX idx_bookings_provider ON bookings(provider_id);
CREATE INDEX idx_bookings_date ON bookings(booking_date);
CREATE INDEX idx_bookings_status ON bookings(status);
CREATE INDEX idx_reviews_provider ON reviews(provider_id);
CREATE INDEX idx_reviews_rating ON reviews(rating);
CREATE INDEX idx_notifications_user ON notifications(user_type, user_id);
CREATE INDEX idx_notifications_read ON notifications(is_read);

-- Insert default admin user (password: 'password123')
INSERT INTO admins (name, email, password, role) VALUES
('System Administrator', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'super_admin');

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, description) VALUES
('site_name', 'Nestlings', 'Website name'),
('site_email', '<EMAIL>', 'Contact email'),
('site_phone', '+94 11 234 5678', 'Contact phone'),
('booking_advance_days', '30', 'Maximum days in advance for booking'),
('review_required_booking', '1', 'Require completed booking to review (1=yes, 0=no)'),
('auto_approve_providers', '0', 'Auto approve new providers (1=yes, 0=no)');

-- Sample Parents Data
INSERT INTO parents (name, email, phone, password, district) VALUES
('John Doe', '<EMAIL>', '+94771234567', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Colombo'),
('Jane Smith', '<EMAIL>', '+94777654321', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Kandy'),
('Michael Brown', '<EMAIL>', '+***********', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Gampaha'),
('Sarah Davis', '<EMAIL>', '+***********', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Kalutara'),
('Robert Wilson', '<EMAIL>', '+***********', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Negombo');

-- 10 Sample Daycare Providers with Complete Details
INSERT INTO providers (business_name, contact_person, email, phone, password, address, district, service_types, capacity, description, verification_status, average_rating, total_reviews) VALUES
('Little Stars Daycare', 'Mary Johnson', '<EMAIL>', '+***********', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '123 Main Street, Colombo 03', 'Colombo', 'full_day,half_day,after_school', 25, 'A nurturing environment for children aged 6 months to 5 years. We focus on early childhood development with qualified teachers and modern facilities.', 'verified', 4.8, 45),
('Sunshine Kids Center', 'David Wilson', '<EMAIL>', '+***********', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '456 Hill Road, Kandy', 'Kandy', 'full_day,half_day,weekend', 20, 'Located in the heart of Kandy, we provide comprehensive childcare services with a focus on creative learning and outdoor activities.', 'verified', 4.6, 32),
('Rainbow Childcare', 'Priya Perera', '<EMAIL>', '+***********', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '789 Beach Road, Galle', 'Galle', 'full_day,half_day,hourly', 18, 'Beachside daycare offering a unique blend of traditional and modern childcare approaches. Specializing in toddler care and pre-school education.', 'verified', 4.7, 28),
('Happy Hearts Nursery', 'Samantha Fernando', '<EMAIL>', '+94332345680', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '321 Temple Road, Negombo', 'Negombo', 'full_day,half_day,after_school,weekend', 30, 'Family-owned daycare with over 15 years of experience. We provide personalized care with small group activities and nutritious meals.', 'verified', 4.9, 67),
('Bright Beginnings', 'Rajesh Kumar', '<EMAIL>', '+94472345681', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '654 School Lane, Ratnapura', 'Ratnapura', 'full_day,half_day,hourly', 15, 'Montessori-inspired daycare focusing on child-led learning and development. Our experienced staff creates a warm, home-like environment.', 'verified', 4.5, 23),
('Tiny Tots Academy', 'Nishani Silva', '<EMAIL>', '+94372345682', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '987 Park Avenue, Anuradhapura', 'Anuradhapura', 'full_day,half_day,after_school', 22, 'Modern facility with state-of-the-art play areas and learning centers. We emphasize STEM education and creative arts for young minds.', 'verified', 4.4, 19),
('Caring Hands Daycare', 'Chaminda Jayawardena', '<EMAIL>', '+94552345683', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '147 Market Street, Badulla', 'Badulla', 'full_day,half_day,weekend,hourly', 16, 'Specialized in caring for children with special needs alongside typical development. Our inclusive approach ensures every child feels valued.', 'verified', 4.8, 34),
('Garden Grove Kids', 'Malini Wickramasinghe', '<EMAIL>', '+94632345684', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '258 Garden Road, Matara', 'Matara', 'full_day,half_day,after_school,weekend', 24, 'Eco-friendly daycare with organic gardens where children learn about nature and sustainability. We grow our own vegetables for healthy meals.', 'verified', 4.6, 41),
('Little Explorers', 'Thilak Rathnayake', '<EMAIL>', '+94912345685', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '369 River View, Kurunegala', 'Kurunegala', 'full_day,half_day,hourly', 20, 'Adventure-based learning daycare with outdoor exploration programs. We encourage curiosity and discovery through hands-on experiences.', 'verified', 4.7, 29),
('Precious Moments', 'Dilani Mendis', '<EMAIL>', '+94772345686', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '741 Central Road, Gampaha', 'Gampaha', 'full_day,half_day,after_school,weekend,hourly', 28, 'Premium childcare facility with bilingual education (English/Sinhala). We offer music, dance, and art programs alongside traditional learning.', 'verified', 4.9, 52);

-- Sample Bookings Data
INSERT INTO bookings (parent_id, provider_id, child_name, child_age, service_type, booking_date, start_time, end_time, duration, total_cost, special_requirements, status) VALUES
(1, 1, 'Emma Johnson', 3, 'full_day', '2024-01-15', '08:00:00', '17:00:00', 9, 4500.00, 'Allergic to peanuts', 'completed'),
(2, 1, 'Liam Smith', 4, 'half_day', '2024-01-16', '08:00:00', '13:00:00', 5, 2500.00, '', 'completed'),
(3, 2, 'Olivia Brown', 2, 'full_day', '2024-01-17', '07:30:00', '16:30:00', 9, 4200.00, 'Needs afternoon nap', 'completed'),
(4, 3, 'Noah Davis', 5, 'after_school', '2024-01-18', '15:00:00', '18:00:00', 3, 1800.00, '', 'completed'),
(5, 4, 'Ava Wilson', 3, 'full_day', '2024-01-19', '08:00:00', '17:00:00', 9, 4800.00, 'Vegetarian meals only', 'completed'),
(1, 5, 'Emma Johnson', 3, 'half_day', '2024-01-20', '08:00:00', '13:00:00', 5, 2200.00, '', 'completed'),
(2, 6, 'Liam Smith', 4, 'full_day', '2024-01-21', '08:00:00', '17:00:00', 9, 4000.00, '', 'completed'),
(3, 7, 'Olivia Brown', 2, 'hourly', '2024-01-22', '10:00:00', '14:00:00', 4, 2000.00, 'First time at daycare', 'completed'),
(4, 8, 'Noah Davis', 5, 'weekend', '2024-01-23', '09:00:00', '15:00:00', 6, 3600.00, '', 'completed'),
(5, 9, 'Ava Wilson', 3, 'full_day', '2024-01-24', '08:00:00', '17:00:00', 9, 4500.00, '', 'completed'),
(1, 10, 'Emma Johnson', 3, 'half_day', '2024-01-25', '08:00:00', '13:00:00', 5, 2800.00, '', 'completed'),
(2, 1, 'Liam Smith', 4, 'full_day', '2024-02-01', '08:00:00', '17:00:00', 9, 4500.00, '', 'confirmed'),
(3, 2, 'Olivia Brown', 2, 'half_day', '2024-02-02', '08:00:00', '13:00:00', 5, 2100.00, '', 'confirmed'),
(4, 3, 'Noah Davis', 5, 'full_day', '2024-02-03', '08:00:00', '17:00:00', 9, 4200.00, '', 'pending'),
(5, 4, 'Ava Wilson', 3, 'after_school', '2024-02-04', '15:00:00', '18:00:00', 3, 1800.00, '', 'pending');

-- Sample Reviews Data
INSERT INTO reviews (parent_id, provider_id, booking_id, rating, review_text, is_active) VALUES
(1, 1, 1, 5, 'Excellent care for my daughter Emma. The staff is very professional and caring. Highly recommended!', TRUE),
(2, 1, 2, 5, 'Great facility with wonderful teachers. My son loves going there every day.', TRUE),
(3, 2, 3, 4, 'Good daycare with nice facilities. Staff could be more attentive during meal times.', TRUE),
(4, 3, 4, 5, 'Perfect after-school care. My child enjoys the activities and homework help.', TRUE),
(5, 4, 5, 5, 'Outstanding service! They accommodate dietary restrictions perfectly. Very satisfied.', TRUE),
(1, 5, 6, 4, 'Nice environment but could use more outdoor play equipment.', TRUE),
(2, 6, 7, 4, 'Good care overall. Teachers are friendly and professional.', TRUE),
(3, 7, 8, 5, 'Amazing inclusive environment. They handle special needs children with great care.', TRUE),
(4, 8, 9, 5, 'Love the eco-friendly approach and organic meals. Great for environmentally conscious parents.', TRUE),
(5, 9, 10, 5, 'Adventure-based learning is fantastic. My child has learned so much about nature.', TRUE),
(1, 10, 11, 5, 'Premium quality care with excellent bilingual education. Worth every penny!', TRUE);

-- Sample Provider Documents Data
INSERT INTO provider_documents (provider_id, document_type, document_name, file_path, expiry_date, verification_status, verified_at) VALUES
(1, 'business_license', 'Little Stars Business License 2024', 'sample_business_license_1.pdf', '2024-12-31', 'approved', '2024-01-10 10:00:00'),
(1, 'childcare_license', 'Childcare Operating License', 'sample_childcare_license_1.pdf', '2025-06-30', 'approved', '2024-01-10 10:00:00'),
(1, 'insurance', 'Public Liability Insurance Certificate', 'sample_insurance_1.pdf', '2024-11-15', 'approved', '2024-01-10 10:00:00'),
(2, 'business_license', 'Sunshine Kids Business Registration', 'sample_business_license_2.pdf', '2024-12-31', 'approved', '2024-01-11 09:30:00'),
(2, 'childcare_license', 'Provincial Childcare License', 'sample_childcare_license_2.pdf', '2025-03-31', 'approved', '2024-01-11 09:30:00'),
(2, 'safety_certificate', 'Fire Safety Compliance Certificate', 'sample_safety_cert_2.pdf', '2024-08-31', 'approved', '2024-01-11 09:30:00'),
(3, 'business_license', 'Rainbow Childcare Business License', 'sample_business_license_3.pdf', '2024-12-31', 'approved', '2024-01-12 11:15:00'),
(3, 'insurance', 'Comprehensive Insurance Coverage', 'sample_insurance_3.pdf', '2024-10-31', 'approved', '2024-01-12 11:15:00'),
(4, 'business_license', 'Happy Hearts Nursery License', 'sample_business_license_4.pdf', '2024-12-31', 'approved', '2024-01-13 14:20:00'),
(4, 'childcare_license', 'Early Childhood Education License', 'sample_childcare_license_4.pdf', '2025-12-31', 'approved', '2024-01-13 14:20:00'),
(4, 'health_certificate', 'Health Department Approval', 'sample_health_cert_4.pdf', '2024-07-31', 'approved', '2024-01-13 14:20:00'),
(5, 'business_license', 'Bright Beginnings Registration', 'sample_business_license_5.pdf', '2024-12-31', 'approved', '2024-01-14 16:45:00'),
(5, 'staff_qualifications', 'Montessori Teacher Certifications', 'sample_staff_quals_5.pdf', NULL, 'approved', '2024-01-14 16:45:00'),
(6, 'business_license', 'Tiny Tots Academy License', 'sample_business_license_6.pdf', '2024-12-31', 'approved', '2024-01-15 08:30:00'),
(6, 'safety_certificate', 'Building Safety Inspection Report', 'sample_safety_cert_6.pdf', '2024-09-30', 'approved', '2024-01-15 08:30:00'),
(7, 'business_license', 'Caring Hands Daycare License', 'sample_business_license_7.pdf', '2024-12-31', 'approved', '2024-01-16 12:00:00'),
(7, 'childcare_license', 'Special Needs Care Authorization', 'sample_childcare_license_7.pdf', '2025-05-31', 'approved', '2024-01-16 12:00:00'),
(8, 'business_license', 'Garden Grove Kids Registration', 'sample_business_license_8.pdf', '2024-12-31', 'approved', '2024-01-17 15:30:00'),
(8, 'health_certificate', 'Organic Food Handling Certificate', 'sample_health_cert_8.pdf', '2024-06-30', 'approved', '2024-01-17 15:30:00'),
(9, 'business_license', 'Little Explorers Business License', 'sample_business_license_9.pdf', '2024-12-31', 'approved', '2024-01-18 10:45:00'),
(9, 'insurance', 'Adventure Activity Insurance', 'sample_insurance_9.pdf', '2024-12-15', 'approved', '2024-01-18 10:45:00'),
(10, 'business_license', 'Precious Moments Premium License', 'sample_business_license_10.pdf', '2024-12-31', 'approved', '2024-01-19 13:15:00'),
(10, 'childcare_license', 'Bilingual Education Authorization', 'sample_childcare_license_10.pdf', '2025-08-31', 'approved', '2024-01-19 13:15:00'),
(10, 'staff_qualifications', 'Music & Arts Teacher Certifications', 'sample_staff_quals_10.pdf', NULL, 'approved', '2024-01-19 13:15:00');
